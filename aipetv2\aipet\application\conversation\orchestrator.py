"""
对话编排器

协调整个对话流程，这是解决当前系统复杂对话逻辑的核心组件
"""

from typing import Optional, AsyncIterator, Dict, Any, List
import asyncio
from datetime import datetime

from aipet.core.entities.conversation import Conversation, ConversationPhase
from aipet.core.entities.message import Message, UserMessage, AssistantMessage, SystemMessage
from aipet.core.services.base import BaseService, ServiceResult
from aipet.core.services.conversation_manager import ConversationManager
from aipet.core.services.message_validator import MessageValidator
from aipet.core.services.title_generator import TitleGenerator
from aipet.core.events.base import EventBus
# from aipet.core.workflow.simple_task_detector import SimpleTaskDetector  # 已废弃，使用FileBasedTaskDetector
from aipet.core.foundation import SystemFoundation


class ConversationOrchestrator(BaseService):
    """对话编排器 - 管理完整的对话流程"""

    def __init__(self,
                 conversation_manager: ConversationManager,
                 message_validator: MessageValidator,
                 event_bus: EventBus,
                 api_client=None,
                 model_manager=None,
                 plugin_service=None):
        super().__init__("ConversationOrchestrator")
        self.conversation_manager = conversation_manager
        self.message_validator = message_validator
        self.event_bus = event_bus
        self.api_client = api_client
        self.model_manager = model_manager
        self.plugin_service = plugin_service  # 添加插件服务引用
        self.title_generator = TitleGenerator(api_client)
        self._active_sessions: Dict[str, bool] = {}  # conversation_id -> is_processing
        self._current_assistant_data: Dict[str, Any] = {}  # 当前助手数据
        self._current_assistant_id: Optional[str] = None  # 当前助手ID

        # 🌟 初始化SystemFoundation和VCP占位符解析器
        self._initialize_vcp_system()

    def _initialize_vcp_system(self):
        """初始化VCP占位符系统"""
        try:
            # 导入SystemFoundation
            from ...core.foundation import SystemFoundation
            self.foundation = SystemFoundation.get_instance()

            # 初始化VCP占位符解析器
            if self.plugin_service:
                from ...services.vcp_placeholder_resolver import VCPPlaceholderResolver

                # 获取VCP服务
                vcp_service = getattr(self.plugin_service, 'vcp_service', None)

                # 🌟 新增：尝试获取笔记控制器
                notes_controller = None
                try:
                    notes_controller = self.foundation.get_service("notes_controller")
                    if notes_controller:
                        self.logger.info("✅ 成功获取笔记控制器，CurrentFile占位符将可用")
                    else:
                        self.logger.warning("⚠️ 笔记控制器未找到，CurrentFile占位符将不可用")
                except Exception as e:
                    self.logger.warning(f"⚠️ 获取笔记控制器失败: {e}")

                # 创建VCP占位符解析器
                self.vcp_placeholder_resolver = VCPPlaceholderResolver(
                    vcp_service=vcp_service,
                    plugin_service=self.plugin_service,
                    notes_controller=notes_controller
                )

                self.logger.info("✅ VCP占位符系统初始化完成")
            else:
                self.logger.warning("⚠️ 插件服务未可用，VCP占位符系统初始化跳过")
                self.vcp_placeholder_resolver = None

        except Exception as e:
            self.logger.error(f"❌ VCP占位符系统初始化失败: {e}")
            self.foundation = None
            self.vcp_placeholder_resolver = None

        # 基于文件的任务检测器
        self.task_detector = None  # FileBasedTaskDetector

        # 系统基础设施
        self.foundation = SystemFoundation.get_instance()
    
    async def _initialize_impl(self):
        """初始化编排器"""
        # 确保依赖服务已初始化
        await self.conversation_manager.initialize()
        await self.message_validator.initialize()
        await self.title_generator.initialize()

        # 初始化基于文件的任务检测器
        try:
            self.logger.info("Starting file-based task detector initialization...")
            from ...core.workflow.file_based_task_detector import FileBasedTaskDetector
            self.task_detector = FileBasedTaskDetector(plugin_service=self.plugin_service)
            task_detector_success = await self.task_detector.initialize()
            if task_detector_success:
                self.logger.info("File-based task detector initialized successfully")
            else:
                self.logger.warning("File-based task detector initialization failed, disabling task detection")
                self.task_detector = None
        except Exception as e:
            self.logger.error(f"Error initializing file-based task detector: {e}")
            self.task_detector = None

        # 初始化基于文件的专家代理
        try:
            self.logger.info("Starting file-based autonomous agents initialization...")
            # 使用通用的专家发现和启动系统
            await self._initialize_all_file_based_agents()

            self.logger.info("File-based autonomous agents initialized and started successfully")

        except Exception as e:
            self.logger.error(f"Error initializing file-based autonomous agents: {e}")
            self.file_based_agents = {}

        # 订阅助手数据相关事件
        if hasattr(self, 'event_bus') and self.event_bus:
            self.logger.info(f"🔧 ConversationOrchestrator使用的事件总线: {type(self.event_bus)}")
            self.logger.info(f"🔧 事件总线实例ID: {id(self.event_bus)}")

            self.event_bus.subscribe_simple("assistant_data_response", self._handle_assistant_data_response)
            self.event_bus.subscribe_simple("current_assistant_id_response", self._handle_current_assistant_id_response)
            # 🚀 订阅Agent任务完成事件
            self.event_bus.subscribe_simple("agent_task_completed", self._handle_agent_task_completed)
            # 🚀 新增：订阅工作流完成事件
            self.event_bus.subscribe_simple("workflow_completed", self._handle_workflow_completed)
            # 🔧 修复：订阅工作流切换事件，确保实时更新助手数据
            self.event_bus.subscribe_simple("assistant_workflow_toggled", self._handle_assistant_workflow_toggled)

            self.logger.info("✅ 已订阅agent_task_completed、workflow_completed和assistant_workflow_toggled事件")
        else:
            self.logger.warning("❌ 事件总线不可用，无法订阅事件")

        self.logger.info("Conversation orchestrator initialized")

    def set_current_assistant(self, assistant_data: Dict[str, Any]):
        """设置当前助手数据"""
        self._current_assistant_data = assistant_data
        self.logger.info(f"Current assistant set: {assistant_data.get('name', 'Unknown')}")

    async def _ensure_current_assistant_data(self):
        """确保当前助手数据是最新的"""
        try:
            # 如果没有助手数据，或者数据不完整，尝试重新获取
            if not self._current_assistant_data or not self._current_assistant_data.get('system_prompt'):
                self.logger.warning("🔄 Current assistant data is missing or incomplete, requesting refresh...")

                # 直接请求当前助手ID和数据，不通过ChatController中转
                if hasattr(self, 'event_bus') and self.event_bus:
                    # 首先获取当前助手ID
                    self.event_bus.publish_simple("current_assistant_id_requested", {
                        "requester": "conversation_orchestrator"
                    })

                    # 等待助手ID响应
                    await asyncio.sleep(0.15)

                    # 如果我们有助手ID，请求助手数据
                    if hasattr(self, '_current_assistant_id') and self._current_assistant_id:
                        self.event_bus.publish_simple("assistant_data_requested", {
                            "assistant_id": self._current_assistant_id,
                            "requester": "conversation_orchestrator"
                        })

                        # 等待助手数据响应
                        await asyncio.sleep(0.15)

                        self.logger.info(f"🔄 Requested assistant data for: {self._current_assistant_id}")
                    else:
                        self.logger.warning("⚠️ No current assistant ID available")

                    # 如果仍然没有数据，使用默认数据
                    if not self._current_assistant_data or not self._current_assistant_data.get('system_prompt'):
                        self.logger.warning("⚠️ Still no assistant data after refresh, using fallback")
                        self._current_assistant_data = {
                            "name": "AI助手",
                            "system_prompt": "你是一个友善、专业的AI助手。请根据用户的需求提供准确、有用的回答。",
                            "max_tokens": 4096,
                            "temperature": 0.7
                        }
                else:
                    self.logger.warning("⚠️ No event bus available for assistant data refresh")

            self.logger.info(f"✅ Assistant data ensured: {self._current_assistant_data.get('name', 'Unknown')}")

        except Exception as e:
            self.logger.error(f"❌ Error ensuring assistant data: {e}")
            # 使用默认数据作为备用
            self._current_assistant_data = {
                "name": "AI助手",
                "system_prompt": "你是一个友善、专业的AI助手。请根据用户的需求提供准确、有用的回答。",
                "max_tokens": 4096,
                "temperature": 0.7
            }

    async def _handle_assistant_data_response(self, event_data: Dict[str, Any]):
        """处理助手数据响应"""
        try:
            requester = event_data.get("requester")

            # 🔧 修复：接受来自chat_controller和conversation_orchestrator的响应
            if requester in ["conversation_orchestrator", "chat_controller"]:
                assistant_id = event_data.get("assistant_id")
                assistant_data = event_data.get("assistant_data", {})

                if assistant_data:
                    self._current_assistant_data = assistant_data
                    self.logger.info(f"🔄 Received assistant data for {assistant_id} from {requester}: {assistant_data.get('name', 'Unknown')}")

                    # 🔧 记录system_prompt信息用于调试
                    system_prompt = assistant_data.get('system_prompt', '')
                    if system_prompt:
                        self.logger.info(f"✅ System prompt loaded: {system_prompt[:100]}{'...' if len(system_prompt) > 100 else ''}")
                    else:
                        self.logger.warning(f"⚠️ No system prompt found in assistant data!")
                else:
                    self.logger.warning(f"⚠️ Received empty assistant data for {assistant_id} from {requester}")

        except Exception as e:
            self.logger.error(f"❌ Error handling assistant data response: {e}")

    async def _handle_current_assistant_id_response(self, event_data: Dict[str, Any]):
        """处理当前助手ID响应"""
        try:
            requester = event_data.get("requester")

            # 只处理发给对话编排器的响应
            if requester == "conversation_orchestrator":
                assistant_id = event_data.get("assistant_id")
                self._current_assistant_id = assistant_id
                self.logger.info(f"🔄 Received current assistant ID: {assistant_id}")

        except Exception as e:
            self.logger.error(f"❌ Error handling current assistant ID response: {e}")

    async def _handle_assistant_workflow_toggled(self, event_data: Dict[str, Any]):
        """处理助手工作流切换事件"""
        try:
            assistant_id = event_data.get("assistant_id")
            workflow_enabled = event_data.get("workflow_enabled", False)

            self.logger.info(f"🔧 [Orchestrator] Received workflow toggle event: {assistant_id} -> {workflow_enabled}")

            # 如果切换的是当前助手，更新当前助手数据
            if (assistant_id == self._current_assistant_id and
                self._current_assistant_data):

                old_state = self._current_assistant_data.get('workflow_enabled', False)
                self._current_assistant_data['workflow_enabled'] = workflow_enabled

                self.logger.info(f"🔧 [Orchestrator] Updated current assistant workflow state: {old_state} -> {workflow_enabled}")
                self.logger.info(f"🔧 [Orchestrator] Current assistant data: {self._current_assistant_data.get('name', 'Unknown')}")
            else:
                self.logger.info(f"🔧 [Orchestrator] Workflow toggle for non-current assistant: {assistant_id} (current: {self._current_assistant_id})")

        except Exception as e:
            self.logger.error(f"❌ Error handling assistant workflow toggle: {e}")

    def _resolve_placeholders_in_system_prompt(self, system_prompt: str) -> str:
        """解析系统提示词中的占位符并动态增强工作流内容"""
        try:
            # 🌟 检查工作流模式，决定VCP占位符处理方式
            workflow_enabled = self._current_assistant_data.get('workflow_enabled', False) if self._current_assistant_data else False

            # 🔍 调试日志：确认工作流模式状态
            self.logger.info(f"🔍 [DEBUG] 工作流模式检查:")
            self.logger.info(f"🔍 [DEBUG]   _current_assistant_data: {self._current_assistant_data}")
            self.logger.info(f"🔍 [DEBUG]   workflow_enabled: {workflow_enabled}")
            self.logger.info(f"🔍 [DEBUG]   system_prompt: {system_prompt}")

            # 🔧 修复：在工作流模式下，通过上下文控制VCP占位符解析行为
            # 1. 使用SystemFoundation解析所有占位符，在工作流模式下传递特殊上下文
            if workflow_enabled:
                # 工作流模式：传递上下文，让VCP占位符保持原样
                context = {'workflow_mode': True}
                resolved_prompt = self.foundation.resolve_text(system_prompt, context)
                self.logger.debug(f"🔧 工作流模式SystemFoundation占位符解析: {len(system_prompt)} -> {len(resolved_prompt)} 字符")
                self.logger.info(f"🔍 [DEBUG]   工作流模式解析后包含VCPAllTools: {'{{VCPAllTools}}' in resolved_prompt}")

                # 2. 工作流模式后处理：替换解析后文本中的VCP占位符为协调员提示
                workflow_replaced = self._replace_vcp_with_workflow_prompt(resolved_prompt)
                self.logger.debug("🤖 工作流模式：VCP占位符已替换为协调员提示")
                self.logger.info(f"🔍 [DEBUG]   工作流替换后包含VCPAllTools: {'{{VCPAllTools}}' in workflow_replaced}")
                self.logger.info(f"🔍 [DEBUG]   工作流替换后包含协调员提示: {'工作流协调模式' in workflow_replaced}")
                resolved_prompt = workflow_replaced
            else:
                # 🌟 关键修复：强制清除所有缓存，确保CurrentFile占位符实时更新
                try:
                    # 清除UniversalResolver的所有缓存
                    if hasattr(self.foundation, '_universal_resolver'):
                        if hasattr(self.foundation._universal_resolver, '_path_cache'):
                            cache_size = len(self.foundation._universal_resolver._path_cache)
                            self.foundation._universal_resolver._path_cache.clear()
                            self.logger.info(f"🗑️ 强制清除UniversalResolver路径缓存: {cache_size} 项")

                        if hasattr(self.foundation._universal_resolver, 'placeholder_resolver') and hasattr(self.foundation._universal_resolver.placeholder_resolver, '_cache'):
                            cache_size = len(self.foundation._universal_resolver.placeholder_resolver._cache)
                            self.foundation._universal_resolver.placeholder_resolver._cache.clear()
                            self.logger.info(f"🗑️ 强制清除PlaceholderResolver缓存: {cache_size} 项")

                    # 清除PlaceholderRegistry缓存
                    if hasattr(self.foundation, 'placeholder_registry') and hasattr(self.foundation.placeholder_registry, '_cache'):
                        cache_size = len(self.foundation.placeholder_registry._cache)
                        self.foundation.placeholder_registry._cache.clear()
                        self.logger.info(f"🗑️ 强制清除PlaceholderRegistry缓存: {cache_size} 项")

                    self.logger.info("✅ 强制清除所有占位符缓存完成，确保CurrentFile实时更新")

                except Exception as e:
                    self.logger.warning(f"⚠️ 清除占位符缓存时出错: {e}")

                # 🌟 强制添加时间戳，确保每次都重新解析
                import time
                timestamp = str(int(time.time() * 1000))  # 毫秒时间戳
                system_prompt_with_timestamp = system_prompt + f"<!-- FORCE_REFRESH_{timestamp} -->"

                # 正常模式：正常解析所有占位符
                resolved_prompt = self.foundation.resolve_text(system_prompt_with_timestamp)

                # 移除时间戳标记
                resolved_prompt = resolved_prompt.replace(f"<!-- FORCE_REFRESH_{timestamp} -->", "")

                self.logger.info(f"🔧 正常模式SystemFoundation占位符解析 [强制刷新]: {len(system_prompt)} -> {len(resolved_prompt)} 字符")

            # 3. 根据工作流配置动态增强内容
            workflow_content = self._get_workflow_coordination_content()
            if workflow_content:
                resolved_prompt += workflow_content
                self.logger.debug(f"🤖 添加工作流协调内容: {len(workflow_content)} 字符")

            return resolved_prompt

        except Exception as e:
            self.logger.error(f"❌ SystemFoundation占位符解析失败: {e}")
            # 如果新系统失败，尝试使用旧的插件服务作为备用
            try:
                if self.plugin_service and hasattr(self.plugin_service, 'resolve_placeholders'):
                    self.logger.warning("⚠️ 回退到插件服务进行占位符解析")
                    fallback_prompt = self.plugin_service.resolve_placeholders(system_prompt)
                    # 即使是备用方案，也要尝试添加工作流内容
                    try:
                        workflow_content = self._get_workflow_coordination_content()
                        if workflow_content:
                            fallback_prompt += workflow_content
                    except Exception as workflow_error:
                        self.logger.warning(f"⚠️ 备用方案添加工作流内容失败: {workflow_error}")
                    return fallback_prompt
            except Exception as fallback_error:
                self.logger.error(f"❌ 备用占位符解析也失败: {fallback_error}")

            return system_prompt

    def _replace_vcp_with_workflow_prompt(self, text: str) -> str:
        """工作流模式下替换VCP占位符为协调员提示词"""
        try:
            from ...services.vcp_placeholder_resolver import create_workflow_prompt_replacer
            replacer = create_workflow_prompt_replacer()
            return replacer(text)
        except Exception as e:
            self.logger.error(f"❌ VCP占位符替换失败: {e}")
            return text

    def _resolve_non_vcp_placeholders(self, text: str) -> str:
        """解析非VCP占位符（工作流模式专用）"""
        try:
            import re

            # 临时替换VCP占位符，避免被SystemFoundation解析
            vcp_pattern = r'\{\{VCP[^}]*\}\}'
            vcp_matches = re.findall(vcp_pattern, text)

            # 用临时标记替换VCP占位符
            temp_text = text
            for i, match in enumerate(vcp_matches):
                temp_text = temp_text.replace(match, f"__VCP_PLACEHOLDER_{i}__")

            # 使用SystemFoundation解析非VCP占位符
            resolved_text = self.foundation.resolve_text(temp_text)

            # 恢复VCP占位符（但它们应该已经被工作流提示替换了）
            for i, match in enumerate(vcp_matches):
                resolved_text = resolved_text.replace(f"__VCP_PLACEHOLDER_{i}__", match)

            return resolved_text

        except Exception as e:
            self.logger.error(f"❌ 非VCP占位符解析失败: {e}")
            # 如果失败，直接使用SystemFoundation（可能会有VCP占位符泄露）
            return self.foundation.resolve_text(text)

    async def _generate_vcp_followup_response(self, conversation_id: str, vcp_result: str, original_messages: list) -> str:
        """生成VCP工具执行后的AI后续回复"""
        try:
            self.logger.info("🎯 Generating AI follow-up response based on VCP tool results...")

            # 构建包含工具结果的消息
            followup_messages = original_messages.copy()

            # 添加工具执行结果作为系统消息
            tool_result_message = {
                "role": "system",
                "content": f"工具执行完成，结果如下：\n\n{vcp_result}\n\n请基于以上工具执行结果，为用户提供完整的回复。"
            }
            followup_messages.append(tool_result_message)

            # 调用API生成后续回复
            followup_result = await self.api_client.send_chat_request(
                messages=followup_messages,
                stream=False  # 使用非流式，简化处理
            )

            if followup_result.success and followup_result.data:
                ai_followup = followup_result.data['choices'][0]['message']['content']
                self.logger.info(f"✅ Generated AI follow-up response: {len(ai_followup)} characters")

                # 只返回AI的后续回复，VCP结果已经替换了工具调用标记
                return ai_followup
            else:
                self.logger.error("❌ Failed to generate AI follow-up response")
                return ""  # 返回空字符串，表示没有后续回复

        except Exception as e:
            self.logger.error(f"❌ Error generating VCP follow-up response: {e}")
            return vcp_result

    async def start_conversation(self, title: str = None, assistant_id: str = None, model_id: str = None) -> ServiceResult[Conversation]:
        """开始新对话"""
        try:
            result = await self.conversation_manager.create_conversation(title, assistant_id, model_id)
            if not result.success:
                return result

            conversation = result.data
            self._active_sessions[conversation.id] = False

            self._log_operation("start_conversation",
                              conversation_id=conversation.id,
                              assistant_id=assistant_id,
                              model_id=model_id)

            return ServiceResult.success_result(data=conversation)

        except Exception as e:
            self._log_error("start_conversation", e)
            return ServiceResult.error_result(
                error=f"Failed to start conversation: {str(e)}",
                error_code="CONVERSATION_START_ERROR"
            )
    
    async def handle_user_message(self, conversation_id: str, content: str) -> ServiceResult[Dict[str, Any]]:
        """处理用户消息"""
        try:
            # 检查会话是否正在处理中
            if self._active_sessions.get(conversation_id, False):
                return ServiceResult.error_result(
                    error="Conversation is currently processing another message",
                    error_code="CONVERSATION_BUSY"
                )
            
            # 标记会话为处理中
            self._active_sessions[conversation_id] = True
            
            try:
                # 获取对话
                conv_result = await self.conversation_manager.get_conversation(conversation_id)
                if not conv_result.success or not conv_result.data:
                    return ServiceResult.error_result(
                        error="Conversation not found",
                        error_code="CONVERSATION_NOT_FOUND"
                    )
                
                conversation = conv_result.data
                
                # 创建用户消息
                user_message = UserMessage(content=content)
                
                # 验证消息
                validation_result = await self.message_validator.validate_message(user_message)
                if not validation_result.success:
                    return validation_result
                
                # 添加消息到对话
                add_result = await self.conversation_manager.add_message_to_conversation(
                    conversation_id, user_message
                )
                if not add_result.success:
                    return add_result

                # 更新对话阶段
                await self.conversation_manager.update_conversation_phase(
                    conversation_id, ConversationPhase.AI_THINKING
                )
                
                self._log_operation(
                    "handle_user_message",
                    conversation_id=conversation_id,
                    message_id=user_message.id
                )
                
                return ServiceResult.success_result(data={
                    "message_id": user_message.id,
                    "conversation_phase": ConversationPhase.AI_THINKING.value,
                    "ready_for_ai_response": True
                })
                
            finally:
                # 确保会话状态被重置
                self._active_sessions[conversation_id] = False
                
        except Exception as e:
            self._log_error("handle_user_message", e, conversation_id=conversation_id)
            self._active_sessions[conversation_id] = False
            return ServiceResult.error_result(
                error=f"Failed to handle user message: {str(e)}",
                error_code="USER_MESSAGE_ERROR"
            )
    
    async def process_ai_response_backup_original(self, conversation_id: str) -> ServiceResult[AsyncIterator[Dict[str, Any]]]:
        """处理AI响应（流式）- 原始版本备份

        备份说明：这是原始的单次工具调用版本，在实施多轮工具调用前进行备份
        如果新版本出现问题，可以通过重命名方法来快速回滚
        """
        # 备份完成标记 - 原始方法已保存在此处
        # 如需回滚，将此方法重命名为 process_ai_response 即可
        return ServiceResult.error_result(
            error="This is a backup method, use process_ai_response instead",
            error_code="BACKUP_METHOD_CALLED"
        )

    async def process_ai_response(self, conversation_id: str) -> ServiceResult[AsyncIterator[Dict[str, Any]]]:
        """处理AI响应（流式）- 支持多轮工具调用"""
        try:
            # 获取对话
            conv_result = await self.conversation_manager.get_conversation(conversation_id)
            if not conv_result.success or not conv_result.data:
                return ServiceResult.error_result(
                    error="Conversation not found",
                    error_code="CONVERSATION_NOT_FOUND"
                )

            conversation = conv_result.data

            # 更新阶段为AI回复中
            await self.conversation_manager.update_conversation_phase(
                conversation_id, ConversationPhase.AI_RESPONDING
            )

            # 返回异步生成器用于多轮工具调用流式响应
            async def multi_turn_response_generator():
                try:
                    # 🔧 在生成响应前，强制刷新助手数据
                    await self._ensure_current_assistant_data()

                    # 多轮工具调用控制变量
                    max_tool_rounds = 10  # 最大工具调用轮数，防止无限循环
                    current_round = 0
                    final_ai_message = None

                    self.logger.info("🔄 Starting multi-turn tool calling process...")

                    # 🔄 多轮工具调用主循环
                    while current_round < max_tool_rounds:
                        current_round += 1
                        self.logger.info(f"🔄 Tool calling round {current_round}/{max_tool_rounds}")

                        # 获取最新的对话状态
                        conv_result = await self.conversation_manager.get_conversation(conversation_id)
                        if not conv_result.success or not conv_result.data:
                            self.logger.error("Failed to get conversation in tool calling loop")
                            break
                        conversation = conv_result.data

                        # 生成AI响应
                        ai_response_result = await self._generate_single_ai_response(conversation_id, conversation)
                        if not ai_response_result:
                            self.logger.error("Failed to generate AI response")
                            break

                        ai_content, ai_message, has_tools = ai_response_result
                        final_ai_message = ai_message

                        # 流式发送AI内容
                        if ai_content:
                            yield {
                                "type": "content",
                                "content": ai_content,
                                "finished": False
                            }

                        # 检查是否有工具调用
                        if not has_tools:
                            self.logger.info(f"✅ No more tool calls found, ending multi-turn process after {current_round} rounds")
                            break

                        # 执行工具调用
                        tool_execution_success = await self._execute_tools_in_ai_response(
                            conversation_id, ai_content, ai_message
                        )

                        if not tool_execution_success:
                            self.logger.error("Tool execution failed, ending multi-turn process")
                            break

                        # 继续下一轮
                        self.logger.info(f"🔄 Round {current_round} completed, checking for next round...")

                    # 循环结束处理
                    if current_round >= max_tool_rounds:
                        self.logger.warning(f"⚠️ Reached maximum tool calling rounds ({max_tool_rounds})")
                        yield {
                            "type": "content",
                            "content": "\n\n[系统提示：已达到最大工具调用轮数限制]",
                            "finished": False
                        }

                    # 🎯 多轮工具调用完成，进行最终处理
                    self.logger.info("🎯 Multi-turn tool calling completed, performing final processing...")

                    # 更新阶段为完成 - 只有在循环结束后才设置
                    await self.conversation_manager.update_conversation_phase(
                        conversation_id, ConversationPhase.COMPLETED
                    )

                    # 🚀 检测AI回复中的任务指令
                    if final_ai_message:
                        asyncio.create_task(self._detect_tasks_in_ai_response(final_ai_message, conversation_id))

                        # 🎵 发布AI响应事件（包含文本内容用于TTS自动播放）
                        message_content = final_ai_message.content if hasattr(final_ai_message, 'content') else ""
                        await self._publish_ai_response_events(final_ai_message, conversation_id, message_content)

                    # 尝试生成标题（异步执行，不阻塞响应）
                    asyncio.create_task(self._try_generate_title(conversation_id))

                    # 发送完成信号
                    yield {
                        "type": "finished",
                        "message_id": final_ai_message.id if final_ai_message and hasattr(final_ai_message, 'id') else 'unknown',
                        "finished": True
                    }

                except Exception as e:
                    self.logger.error(f"Error in multi-turn tool calling: {e}")
                    yield {
                        "type": "error",
                        "error": str(e),
                        "finished": True
                    }

            return ServiceResult.success_result(data=multi_turn_response_generator())

        except Exception as e:
            self._log_error("process_ai_response", e, conversation_id=conversation_id)
            return ServiceResult.error_result(
                error=f"Failed to process AI response: {str(e)}",
                error_code="AI_RESPONSE_ERROR"
            )

    async def get_conversation_status(self, conversation_id: str) -> ServiceResult[Dict[str, Any]]:
        """获取对话状态"""
        try:
            conv_result = await self.conversation_manager.get_conversation(conversation_id)
            if not conv_result.success or not conv_result.data:
                return ServiceResult.error_result(
                    error="Conversation not found",
                    error_code="CONVERSATION_NOT_FOUND"
                )

            conversation = conv_result.data

            return ServiceResult.success_result(data={
                "conversation_id": conversation.id,
                "phase": conversation.state.current_phase.value,
                "is_processing": self._active_sessions.get(conversation_id, False),
                "message_count": len(conversation.messages),
                "last_activity": conversation.state.last_activity.isoformat(),
                "metadata": conversation.metadata.__dict__ if hasattr(conversation.metadata, '__dict__') else {}
            })

        except Exception as e:
            self._log_error("get_conversation_status", e, conversation_id=conversation_id)
            return ServiceResult.error_result(
                error=f"Failed to get conversation status: {str(e)}",
                error_code="CONVERSATION_STATUS_ERROR"
            )

    async def list_conversations(self) -> ServiceResult[List[Dict[str, Any]]]:
        """列出所有对话"""
        try:
            return await self.conversation_manager.list_active_conversations()

        except Exception as e:
            self._log_error("list_conversations", e)
            return ServiceResult.error_result(
                error=f"Failed to list conversations: {str(e)}",
                error_code="CONVERSATION_LIST_ERROR"
            )

    async def delete_conversation(self, conversation_id: str) -> ServiceResult[None]:
        """删除对话"""
        try:
            # 检查对话是否存在
            conv_result = await self.conversation_manager.get_conversation(conversation_id)
            if not conv_result.success or not conv_result.data:
                return ServiceResult.error_result(
                    error="Conversation not found",
                    error_code="CONVERSATION_NOT_FOUND"
                )

            # 清理活动会话
            if conversation_id in self._active_sessions:
                del self._active_sessions[conversation_id]

            # 删除对话
            result = await self.conversation_manager.delete_conversation(conversation_id)
            if not result.success:
                return result

            self._log_operation("delete_conversation", conversation_id=conversation_id)
            return result

        except Exception as e:
            self._log_error("delete_conversation", e, conversation_id=conversation_id)
            return ServiceResult.error_result(
                error=f"Failed to delete conversation: {str(e)}",
                error_code="CONVERSATION_DELETE_ERROR"
            )

    async def switch_conversation(self, conversation_id: str) -> ServiceResult[Conversation]:
        """切换到指定对话"""
        try:
            # 获取对话
            conv_result = await self.conversation_manager.get_conversation(conversation_id)
            if not conv_result.success or not conv_result.data:
                return ServiceResult.error_result(
                    error="Conversation not found",
                    error_code="CONVERSATION_NOT_FOUND"
                )

            conversation = conv_result.data

            # 设置为当前对话
            set_result = await self.conversation_manager.set_current_conversation(conversation_id)
            if not set_result.success:
                return set_result

            self._log_operation("switch_conversation", conversation_id=conversation_id)

            return ServiceResult.success_result(data=conversation)

        except Exception as e:
            self._log_error("switch_conversation", e, conversation_id=conversation_id)
            return ServiceResult.error_result(
                error=f"Failed to switch conversation: {str(e)}",
                error_code="CONVERSATION_SWITCH_ERROR"
            )

    async def _try_generate_title(self, conversation_id: str):
        """尝试为对话生成标题（异步执行）"""
        try:
            # 获取对话
            conv_result = await self.conversation_manager.get_conversation(conversation_id)
            if not conv_result.success or not conv_result.data:
                return

            conversation = conv_result.data

            # 检查是否已有标题或消息太少
            if (hasattr(conversation.metadata, 'title') and
                conversation.metadata.title and
                not conversation.metadata.title.startswith("新对话")):
                return

            if len(conversation.messages) < 2:
                return

            # 使用标题生成器
            if self.title_generator:
                title_result = await self.title_generator.generate_title(conversation.messages)
                if title_result.success and title_result.data:
                    await self.conversation_manager.update_conversation_title(
                        conversation_id, title_result.data
                    )
                    self.logger.info(f"✅ Generated title for conversation {conversation_id}: {title_result.data}")
                else:
                    self.logger.warning(f"Failed to generate title: {title_result.error if title_result else 'No result'}")

        except Exception as e:
            self.logger.error(f"Error in title generation: {e}")



    async def _detect_tasks_in_ai_response(self, ai_message, conversation_id: str):
        """检测AI响应中的任务指令"""
        try:
            if not ai_message or not hasattr(ai_message, 'content'):
                return

            content = ai_message.content
            if not content:
                return

            # 检测任务指令模式
            import re
            task_pattern = r'task start(.+?)task end'
            matches = re.findall(task_pattern, content, re.DOTALL | re.IGNORECASE)

            if matches:
                self.logger.info(f"🎯 Detected {len(matches)} task instructions in AI response")

                # 这里可以添加任务分发逻辑
                for task_content in matches:
                    self.logger.info(f"Task instruction: {task_content.strip()}")

        except Exception as e:
            self.logger.error(f"Error detecting tasks in AI response: {e}")

    # 🔄 多轮工具调用辅助方法

    async def _generate_single_ai_response(self, conversation_id: str, conversation) -> tuple:
        """生成单次AI响应

        Returns:
            tuple: (ai_content, ai_message, has_tools) 或 None if failed
        """
        try:
            if not self.api_client:
                self.logger.error("No API client available")
                return None

            # 准备消息历史
            messages = []

            # 添加系统提示词
            if self._current_assistant_data and self._current_assistant_data.get('system_prompt'):
                raw_system_prompt = self._current_assistant_data['system_prompt']
                system_prompt = self._resolve_placeholders_in_system_prompt(raw_system_prompt)
                system_message = SystemMessage(content=system_prompt)
                messages.append(system_message.to_api_format())

            # 添加对话历史
            for msg in conversation.messages:
                if hasattr(msg, 'to_api_format'):
                    api_format_msg = msg.to_api_format()
                    messages.append(api_format_msg)

            # 🚀 关键修改：检查上一条消息是否是工具结果，如果是，则添加强制总结指令
            if messages and messages[-1].get("role") == "tool":
                summary_instruction = {
                    "role": "system",
                    "content": "你已经成功调用了工具并获取了结果。现在，请你**必须**基于刚才的工具执行结果，为用户生成一段最终的、易于理解的自然语言回复。不要再次调用工具，直接总结并回答。"
                }
                messages.append(summary_instruction)
                self.logger.info("🔧 已添加强制总结指令，引导AI生成最终回复。")

            # 准备API参数
            api_params = {}
            if self._current_assistant_data:
                if self._current_assistant_data.get('temperature') is not None:
                    api_params['temperature'] = self._current_assistant_data['temperature']
                if self._current_assistant_data.get('max_tokens') is not None:
                    api_params['max_tokens'] = self._current_assistant_data['max_tokens']
                if self._current_assistant_data.get('tools'):
                    api_params['tools'] = self._current_assistant_data['tools']

            # 调用API
            result = await self.api_client.send_chat_request(
                messages=messages,
                stream=False,  # 使用非流式，简化处理
                **api_params
            )

            if not result.success or not result.data:
                self.logger.error("API call failed")
                return None

            # 解析响应
            response_data = result.data
            ai_content = response_data.get('choices', [{}])[0].get('message', {}).get('content', '')
            tool_calls = response_data.get('choices', [{}])[0].get('message', {}).get('tool_calls', [])

            # 创建AI消息
            from aipet.core.entities.message import AssistantMessage, ToolCall
            tool_call_objects = []
            if tool_calls:
                for tc in tool_calls:
                    tool_call_obj = ToolCall(
                        id=tc.get("id", ""),
                        name=tc.get("function", {}).get("name", ""),
                        arguments=tc.get("function", {}).get("arguments", "{}")
                    )
                    tool_call_objects.append(tool_call_obj)

            # 🔧 修复空响应问题：如果AI返回空内容且没有工具调用，提供默认内容
            if not ai_content.strip() and not tool_call_objects:
                ai_content = "[AI响应为空，可能是网络问题或API异常]"
                self.logger.warning("AI returned empty response, using fallback content")

            ai_message = AssistantMessage(
                content=ai_content,
                tool_calls=tool_call_objects if tool_call_objects else None
            )

            # 保存AI消息到对话历史
            await self.conversation_manager.add_message_to_conversation(conversation_id, ai_message)

            # 检查是否有工具调用（包括VCP和OpenAI格式）
            has_vcp_tools = ai_content and "<<<[TOOL_REQUEST]>>>" in ai_content
            has_openai_tools = bool(tool_calls)
            has_tools = has_vcp_tools or has_openai_tools

            self.logger.info(f"Generated AI response: content_length={len(ai_content)}, has_vcp_tools={has_vcp_tools}, has_openai_tools={has_openai_tools}")

            return (ai_content, ai_message, has_tools)

        except Exception as e:
            self.logger.error(f"Error generating single AI response: {e}")
            return None

    async def _execute_tools_in_ai_response(self, conversation_id: str, ai_content: str, ai_message) -> bool:
        """执行AI响应中的工具调用

        Args:
            conversation_id: 对话ID
            ai_content: AI响应内容
            ai_message: AI消息对象

        Returns:
            bool: 是否执行成功
        """
        try:
            # 检查VCP工具调用
            if ai_content and "<<<[TOOL_REQUEST]>>>" in ai_content:
                self.logger.info("🎯 Executing VCP tool calls...")

                # 使用VCP服务处理工具调用
                if hasattr(self.plugin_service, 'vcp_service') and self.plugin_service.vcp_service:
                    vcp_adapter = self.plugin_service.vcp_service

                    # 🔧 修复：直接执行每个工具并分别创建ToolResponseMessage，保持与OpenAI格式一致
                    if hasattr(vcp_adapter, 'parser') and hasattr(vcp_adapter, 'plugin_manager'):
                        # 解析工具调用
                        tool_calls = vcp_adapter.parser.parse_tool_calls(ai_content)

                        if tool_calls:
                            from aipet.core.entities.message import ToolResponseMessage
                            import uuid

                            # 分别执行每个工具调用
                            for tool_call in tool_calls:
                                try:
                                    # 执行单个工具
                                    result = await vcp_adapter.plugin_manager.execute_tool(
                                        tool_call.name,
                                        tool_call.params
                                    )

                                    # 为VCP工具调用生成唯一ID
                                    tool_call_id = f"vcp_{tool_call.name}_{str(uuid.uuid4())[:8]}"

                                    if result.success:
                                        # 格式化成功结果
                                        result_text = vcp_adapter._format_tool_result(result.result, tool_call.name)
                                        self.logger.info(f"🔧 VCP工具 {tool_call.name} 执行成功")

                                        tool_result_message = ToolResponseMessage(
                                            tool_call_id=tool_call_id,
                                            tool_name=tool_call.name,
                                            content=result_text
                                        )
                                    else:
                                        # 处理执行失败
                                        error_text = f"[{tool_call.name} 执行失败: {result.error}]"
                                        self.logger.warning(f"🔧 VCP工具 {tool_call.name} 执行失败: {result.error}")

                                        tool_result_message = ToolResponseMessage(
                                            tool_call_id=tool_call_id,
                                            tool_name=tool_call.name,
                                            content=error_text
                                        )

                                    # 添加工具结果消息到对话
                                    await self.conversation_manager.add_message_to_conversation(
                                        conversation_id, tool_result_message
                                    )

                                except Exception as e:
                                    self.logger.error(f"Error executing VCP tool {tool_call.name}: {e}")

                                    # 创建错误消息
                                    error_message = ToolResponseMessage(
                                        tool_call_id=f"vcp_{tool_call.name}_{str(uuid.uuid4())[:8]}",
                                        tool_name=tool_call.name,
                                        content=f"[{tool_call.name} 执行异常: {str(e)}]"
                                    )
                                    await self.conversation_manager.add_message_to_conversation(
                                        conversation_id, error_message
                                    )

                            return True

                    # 降级处理：如果无法分别处理，使用原来的整体处理方式
                    vcp_processed_content = await vcp_adapter.process_message(ai_content)
                    if vcp_processed_content != ai_content:
                        from aipet.core.entities.message import ToolResponseMessage
                        import uuid

                        tool_result_message = ToolResponseMessage(
                            tool_call_id=f"vcp_batch_{str(uuid.uuid4())[:8]}",
                            tool_name="vcp_tools",
                            content=vcp_processed_content
                        )
                        await self.conversation_manager.add_message_to_conversation(
                            conversation_id, tool_result_message
                        )
                        return True
                else:
                    self.logger.warning("⚠️ VCP service not available")
                    return False

            # 检查OpenAI格式工具调用
            if ai_message and hasattr(ai_message, 'tool_calls') and ai_message.tool_calls:
                self.logger.info(f"🎯 Executing {len(ai_message.tool_calls)} OpenAI format tool calls...")

                for tool_call in ai_message.tool_calls:
                    try:
                        # 执行工具调用
                        tool_result = await self._execute_tool_call({
                            "id": tool_call.id,
                            "function": {
                                "name": tool_call.name,
                                "arguments": tool_call.arguments
                            }
                        })

                        # 创建工具响应消息
                        from aipet.core.entities.message import ToolResponseMessage
                        tool_response_message = ToolResponseMessage(
                            tool_call_id=tool_call.id,
                            tool_name=tool_call.name,
                            content=tool_result
                        )
                        await self.conversation_manager.add_message_to_conversation(
                            conversation_id, tool_response_message
                        )

                    except Exception as tool_error:
                        self.logger.error(f"Tool execution error for {tool_call.name}: {tool_error}")

                        # 创建错误响应消息
                        from aipet.core.entities.message import ToolResponseMessage
                        error_message = ToolResponseMessage(
                            tool_call_id=tool_call.id,
                            tool_name=tool_call.name,
                            content=f"工具执行失败: {str(tool_error)}"
                        )
                        await self.conversation_manager.add_message_to_conversation(
                            conversation_id, error_message
                        )

                return True

            # 没有工具调用
            return False

        except Exception as e:
            self.logger.error(f"Error executing tools in AI response: {e}")
            return False

            return ServiceResult.success_result(data=multi_turn_response_generator())

        except Exception as e:
            self._log_error("process_ai_response", e, conversation_id=conversation_id)
            return ServiceResult.error_result(
                error=f"Failed to process AI response: {str(e)}",
                error_code="AI_RESPONSE_ERROR"
            )
    
    async def get_conversation_status(self, conversation_id: str) -> ServiceResult[Dict[str, Any]]:
        """获取对话状态"""
        try:
            conv_result = await self.conversation_manager.get_conversation(conversation_id)
            if not conv_result.success or not conv_result.data:
                return ServiceResult.error_result(
                    error="Conversation not found",
                    error_code="CONVERSATION_NOT_FOUND"
                )
            
            conversation = conv_result.data
            
            return ServiceResult.success_result(data={
                "conversation_id": conversation.id,
                "phase": conversation.state.current_phase.value,
                "is_processing": self._active_sessions.get(conversation_id, False),
                "message_count": len(conversation.messages),
                "last_activity": conversation.state.last_activity.isoformat(),
                "has_pending_tool_calls": conversation.state.has_pending_tool_calls()
            })
            
        except Exception as e:
            self._log_error("get_conversation_status", e, conversation_id=conversation_id)
            return ServiceResult.error_result(
                error=f"Failed to get conversation status: {str(e)}",
                error_code="STATUS_GET_ERROR"
            )
    
    async def list_conversations(self) -> ServiceResult[List[Dict[str, Any]]]:
        """列出所有对话"""
        try:
            return await self.conversation_manager.list_active_conversations()

        except Exception as e:
            self._log_error("list_conversations", e)
            return ServiceResult.error_result(
                error=f"Failed to list conversations: {str(e)}",
                error_code="CONVERSATION_LIST_ERROR"
            )

    async def delete_conversation(self, conversation_id: str) -> ServiceResult[None]:
        """删除对话"""
        try:
            # 检查对话是否存在
            conv_result = await self.conversation_manager.get_conversation(conversation_id)
            if not conv_result.success or not conv_result.data:
                return ServiceResult.error_result(
                    error="Conversation not found",
                    error_code="CONVERSATION_NOT_FOUND"
                )

            # 清理活动会话
            if conversation_id in self._active_sessions:
                del self._active_sessions[conversation_id]

            # 删除对话
            result = await self.conversation_manager.delete_conversation(conversation_id)

            if result.success:
                self._log_operation("delete_conversation", conversation_id=conversation_id)

            return result

        except Exception as e:
            self._log_error("delete_conversation", e, conversation_id=conversation_id)
            return ServiceResult.error_result(
                error=f"Failed to delete conversation: {str(e)}",
                error_code="CONVERSATION_DELETE_ERROR"
            )

    async def switch_conversation(self, conversation_id: str) -> ServiceResult[Conversation]:
        """切换到指定对话"""
        try:
            # 获取对话
            conv_result = await self.conversation_manager.get_conversation(conversation_id)
            if not conv_result.success or not conv_result.data:
                return ServiceResult.error_result(
                    error="Conversation not found",
                    error_code="CONVERSATION_NOT_FOUND"
                )

            conversation = conv_result.data

            # 设置为当前对话
            set_result = await self.conversation_manager.set_current_conversation(conversation_id)
            if not set_result.success:
                return set_result

            self._log_operation("switch_conversation", conversation_id=conversation_id)

            return ServiceResult.success_result(data=conversation)

        except Exception as e:
            self._log_error("switch_conversation", e, conversation_id=conversation_id)
            return ServiceResult.error_result(
                error=f"Failed to switch conversation: {str(e)}",
                error_code="CONVERSATION_SWITCH_ERROR"
            )

    async def _try_generate_title(self, conversation_id: str):
        """尝试为对话生成标题（异步执行）"""
        try:
            # 获取对话
            conv_result = await self.conversation_manager.get_conversation(conversation_id)
            if not conv_result.success or not conv_result.data:
                return

            conversation = conv_result.data

            # 检查是否应该生成标题
            if await self.title_generator.should_generate_title(conversation):
                self.logger.info(f"Generating title for conversation: {conversation_id}")

                # 生成标题
                title_result = await self.title_generator.generate_title(conversation)

                if title_result.success and title_result.data:
                    new_title = title_result.data

                    # 更新对话标题
                    update_result = await self.conversation_manager.update_conversation_title(
                        conversation_id, new_title
                    )

                    if update_result.success:
                        self.logger.info(f"Title updated for conversation {conversation_id}: {new_title}")

                        # 发布标题更新事件
                        await self.event_bus.publish_simple("conversation_updated", {
                            "conversation_id": conversation_id,
                            "title": new_title,
                            "timestamp": datetime.now().isoformat()
                        })
                    else:
                        self.logger.error(f"Failed to update title: {update_result.error}")
                else:
                    self.logger.warning(f"Failed to generate title: {title_result.error if title_result else 'No result'}")

        except Exception as e:
            self.logger.error(f"Error in title generation: {e}")

    def _convert_to_standard_params(self, legacy_params: dict, tool_name: str) -> dict:
        """
        将AI传递的旧格式参数转换为标准参数格式

        Args:
            legacy_params: AI传递的原始参数字典
            tool_name: 工具名称，用于特定的转换规则

        Returns:
            标准格式的参数字典
        """
        # 如果已经是标准格式，直接返回
        if "input" in legacy_params:
            return legacy_params

        # 转换规则映射
        conversion_rules = {
            "enhanced_read_file": {
                "path": "input.path",
                "encoding": "input.encoding"
            },
            "enhanced_write_file": {
                "path": "input.path",
                "content": "input.content",
                "encoding": "input.encoding"
            },
            "enhanced_list_files": {
                "path": "input.path",
                "recursive": "options.recursive"
            },
            "execute_command": {
                "command": "input.command",
                "working_directory": "input.working_directory",
                "timeout": "options.timeout",
                "shell": "options.shell"
            },
            "play_music": {
                "music_name": "input.identifier",
                "identifier": "input.identifier"
            },
            "set_volume": {
                "volume": "input.volume"
            },
            "set_play_mode": {
                "mode": "input.mode"
            }
        }

        # 初始化标准参数结构
        standard_params = {
            "input": {},
            "options": {},
            "context": {},
            "output": {}
        }

        # 获取当前工具的转换规则
        rules = conversion_rules.get(tool_name, {})

        # 应用转换规则
        for old_key, old_value in legacy_params.items():
            if old_key in rules:
                target_path = rules[old_key]
                self._set_nested_value(standard_params, target_path, old_value)
            else:
                # 未知参数默认放入input
                standard_params["input"][old_key] = old_value

        return standard_params

    def _set_nested_value(self, obj: dict, path: str, value):
        """设置嵌套字典值"""
        keys = path.split('.')
        current = obj

        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]

        current[keys[-1]] = value

    async def _execute_tool_call(self, tool_call: Dict[str, Any]) -> str:
        """执行工具调用（支持VCP工具）"""
        try:
            if not self.plugin_service:
                return "错误：插件服务不可用"

            function_info = tool_call.get("function", {})
            tool_name = function_info.get("name")
            arguments = function_info.get("arguments", "{}")

            if not tool_name:
                return "错误：工具名称为空"

            # 解析参数
            try:
                import json
                args = json.loads(arguments) if isinstance(arguments, str) else arguments
            except json.JSONDecodeError:
                return f"错误：无法解析工具参数: {arguments}"

            self.logger.info(f"Executing tool: {tool_name} with args: {args}")

            # 检查是否为VCP工具
            if hasattr(self.plugin_service, 'vcp_service') and self.plugin_service.vcp_service:
                vcp_service = self.plugin_service.vcp_service
                if vcp_service.is_vcp_tool(tool_name):
                    self.logger.info(f"Executing VCP tool: {tool_name}")

                    # 处理VCP工具调用
                    if "vcp_request" in args:
                        # 新格式：使用vcp_request参数
                        vcp_request = args["vcp_request"]
                        self.logger.info(f"Processing VCP request: {vcp_request}")

                        # 使用VCP服务处理消息
                        result = await vcp_service.process_message(vcp_request)
                        return result
                    else:
                        # 旧格式：直接使用参数
                        result = await vcp_service.execute_tool(tool_name, args)
                        return result.result if result.success else f"VCP工具执行失败: {result.error}"

            # 转换为标准参数格式（旧插件系统）
            standard_params = self._convert_to_standard_params(args, tool_name)

            # 使用VCP插件系统执行工具
            self.logger.info(f"Executing VCP tool: {tool_name} with standard params: {standard_params}")

            # 统一使用VCP插件服务
            result = await self.plugin_service.execute_tool_async(tool_name, standard_params)
            if result.get("success"):
                return str(result.get("result", "工具执行成功"))
            else:
                return f"工具执行失败: {result.get('error', '未知错误')}"

        except Exception as e:
            self.logger.error(f"Error executing tool call: {e}")
            return f"工具执行异常: {str(e)}"

    def _generate_tool_status_text(self, tool_calls: List[Dict]) -> str:
        """
        生成工具调用状态文本，让用户知道AI正在做什么

        Args:
            tool_calls: 工具调用列表

        Returns:
            格式化的状态文本
        """
        if not tool_calls:
            return ""

        # 工具名称到友好描述的映射
        tool_descriptions = {
            "TavilySearch": "🔍 正在搜索网络信息",
            "SciCalculatorRequest": "🧮 正在进行数学计算",
            "AgentMessage": "📨 正在发送消息",
            "WeatherReporter": "🌤️ 正在查询天气信息",
            "SayHello": "👋 正在打招呼"
        }

        status_lines = []
        for tool_call in tool_calls:
            tool_name = tool_call.get("function", {}).get("name", "")
            description = tool_descriptions.get(tool_name, f"🔧 正在执行 {tool_name}")
            status_lines.append(description)

        if len(status_lines) == 1:
            return f"\n{status_lines[0]}...\n\n"
        else:
            return f"\n正在执行以下操作：\n" + "\n".join(f"• {line}" for line in status_lines) + "\n\n"

    async def _health_check_impl(self) -> Dict[str, Any]:
        """健康检查实现"""
        return {
            "active_sessions": len(self._active_sessions),
            "processing_sessions": sum(1 for is_processing in self._active_sessions.values() if is_processing),
            "status": "healthy"
        }

    async def _detect_tasks_in_ai_response(self, ai_message, conversation_id: str):
        """检测AI回复中的任务指令"""
        try:
            if not ai_message or not hasattr(ai_message, 'content'):
                return

            content = ai_message.content
            if not content:
                return

            # 使用简化的任务检测器
            if self.task_detector:
                result = await self.task_detector.check_ai_response(
                    content, conversation_id, ai_message.id
                )

                if result.get("type") == "task_detected":
                    self.logger.info(f"🎯 {result.get('message')}")
                elif result.get("type") == "error":
                    self.logger.error(f"❌ 任务检测失败: {result.get('message')}")

        except Exception as e:
            self.logger.error(f"❌ 任务检测失败: {e}")

    async def _handle_agent_task_completed(self, event_data: Dict[str, Any]):
        """处理Agent任务完成事件"""
        try:
            self.logger.info(f"🎉 ConversationOrchestrator收到Agent任务完成事件!")
            self.logger.info(f"📡 事件数据: {event_data}")
            self.logger.info(f"🔧 当前事件总线: {type(self.event_bus)}")

            agent_name = event_data.get('agent_name', '专家')
            task_description = event_data.get('task_description', '任务')
            board_id = event_data.get('board_id')
            result = event_data.get('result', {})

            if not board_id:
                self.logger.warning("任务完成事件缺少board_id，无法确定对话")
                return

            # 从board_id中提取conversation_id
            conversation_id = await self._find_conversation_for_task(board_id)
            if not conversation_id:
                self.logger.warning(f"无法找到任务 {board_id} 对应的对话")
                return

            # 🚀 获取公告板上的完整信息并让AI智能总结
            completion_message = await self._generate_intelligent_task_summary(
                agent_name, task_description, board_id, result
            )

            # 创建AI消息并添加到对话
            ai_message = AssistantMessage(content=completion_message)
            await self.conversation_manager.add_message_to_conversation(conversation_id, ai_message)

            # 发布AI消息事件，通知前端更新
            if hasattr(self, 'event_bus') and self.event_bus:
                # 1. 先发布响应开始事件，创建消息气泡
                result1 = self.event_bus.publish_simple("ai_response_started", {
                    "message_id": ai_message.id,
                    "conversation_id": conversation_id,
                    "assistant_name": self._current_assistant_data.get("name", "芊芊") if self._current_assistant_data else "芊芊",
                    "avatar_path": self._current_assistant_data.get("avatar_path") if self._current_assistant_data else None
                })
                if result1 is not None and asyncio.iscoroutine(result1):
                    await result1

                # 2. 发布内容块事件，填充消息内容
                result2 = self.event_bus.publish_simple("ai_content_chunk", {
                    "message_id": ai_message.id,
                    "content": completion_message,
                    "conversation_id": conversation_id,
                    "assistant_name": self._current_assistant_data.get("name", "芊芊") if self._current_assistant_data else "芊芊",
                    "avatar_path": self._current_assistant_data.get("avatar_path") if self._current_assistant_data else None
                })
                if result2 is not None and asyncio.iscoroutine(result2):
                    await result2

                # 注意：ai_response_finished 事件将在 _publish_ai_response_events 中发布，避免重复

            self.logger.info(f"✅ 任务完成回复已发送到对话 {conversation_id}")

        except Exception as e:
            self.logger.error(f"❌ 处理Agent任务完成事件失败: {e}")

    async def _handle_workflow_completed(self, event_data: Dict[str, Any]):
        """处理工作流完成事件"""
        try:
            self.logger.info(f"🎉 ConversationOrchestrator收到工作流完成事件!")

            board_id = event_data.get('board_id')
            workflow_info = event_data.get('workflow_info', {})
            trigger_agent = event_data.get('trigger_agent', '专家')

            if not board_id:
                self.logger.warning("工作流完成事件缺少board_id")
                return

            # 🚀 新增：只发送状态栏更新，不发送聊天消息
            await self._publish_workflow_completion_to_status_bar(board_id, workflow_info, trigger_agent)

            # 🚀 注释掉原有的聊天消息发送逻辑，避免前端消息污染
            # # 查找对应的对话
            # conversation_id = await self._find_conversation_for_task(board_id)
            # if not conversation_id:
            #     self.logger.warning(f"无法找到工作流 {board_id} 对应的对话")
            #     return

            # # 🚀 生成完整的工作流汇总
            # summary_result = self._generate_complete_workflow_summary(board_id, workflow_info)
            # # 检查是否是协程
            # if asyncio.iscoroutine(summary_result):
            #     summary_message = await summary_result
            # else:
            #     summary_message = summary_result

            # # 创建AI消息并添加到对话
            # ai_message = AssistantMessage(content=summary_message)
            # await self.conversation_manager.add_message_to_conversation(conversation_id, ai_message)

            # # 发布AI消息事件，通知前端更新
            # await self._publish_ai_response_events(ai_message, conversation_id, summary_message)

            self.logger.info(f"✅ 工作流完成状态已更新到状态栏: {board_id}")

        except Exception as e:
            self.logger.error(f"❌ 处理工作流完成事件失败: {e}")

    async def _publish_workflow_completion_to_status_bar(self, board_id: str, workflow_info: Dict, trigger_agent: str):
        """发布工作流完成状态到状态栏"""
        try:
            # 发布状态栏更新事件
            await self.event_bus.publish_simple("status_bar_update", {
                "type": "workflow_progress",
                "agent": trigger_agent,
                "status": "completed",
                "progress": "",
                "board_id": board_id,
                "workflow_info": workflow_info
            })

            self.logger.info(f"📊 工作流完成状态已发送到状态栏: {board_id}")

        except Exception as e:
            self.logger.error(f"发送工作流完成状态到状态栏失败: {e}")

    async def _generate_intelligent_task_summary(self, agent_name: str, task_description: str,
                                               board_id: str, result: Dict) -> str:
        """生成智能的任务完成总结"""
        try:
            # 1. 获取公告板上的完整信息
            bulletin_board_content_result = self._get_bulletin_board_content(board_id)
            # 检查是否是协程
            if asyncio.iscoroutine(bulletin_board_content_result):
                bulletin_board_content = await bulletin_board_content_result
            else:
                bulletin_board_content = bulletin_board_content_result

            # 2. 提取工具执行结果
            tool_results = result.get('tool_results', [])
            actual_data = ""
            tool_name = ""

            for tool_result in tool_results:
                tool_name = tool_result.get('tool', '')
                tool_data = tool_result.get('result', {})

                # 处理不同类型的工具结果
                if tool_name == 'web_search':
                    if isinstance(tool_data, dict) and 'data' in tool_data:
                        actual_data = tool_data['data']
                    elif isinstance(tool_data, str):
                        actual_data = tool_data
                elif tool_name == 'get_weather':
                    # 处理天气查询结果
                    if isinstance(tool_data, dict) and 'data' in tool_data:
                        actual_data = tool_data['data']
                    elif isinstance(tool_data, str):
                        actual_data = tool_data
                else:
                    # 处理其他工具结果
                    if isinstance(tool_data, dict):
                        if 'data' in tool_data:
                            actual_data = tool_data['data']
                        elif 'result' in tool_data:
                            actual_data = str(tool_data['result'])
                        else:
                            actual_data = str(tool_data)
                    elif isinstance(tool_data, str):
                        actual_data = tool_data
                    else:
                        actual_data = str(tool_data)

                # 如果找到了数据就跳出循环
                if actual_data:
                    break

            # 3. 如果有API客户端，让AI智能总结
            if self.api_client and actual_data:
                # 根据工具类型生成不同的提示词
                if tool_name == 'get_weather':
                    summary_prompt = f"""请根据以下天气查询结果，为用户提供简洁友好的天气信息：

用户任务：{task_description}
执行专家：{agent_name}

天气数据：
{actual_data}

请以用户友好的方式总结天气信息，包括：
1. 当前天气状况和温度
2. 体感温度、湿度等详细信息
3. 风力风向等其他信息

回复要简洁明了，突出最重要的天气信息。"""
                elif tool_name == 'web_search':
                    summary_prompt = f"""请根据以下搜索结果，为用户提供简洁有用的总结回复：

用户任务：{task_description}
执行专家：{agent_name}

搜索结果：
{actual_data}

请以用户友好的方式总结关键信息，包括：
1. 主要功能和特点
2. 安装或使用方法
3. 重要链接或资源

回复要简洁明了，突出最有价值的信息。"""
                else:
                    summary_prompt = f"""请根据以下任务执行结果，为用户提供简洁有用的总结回复：

用户任务：{task_description}
执行专家：{agent_name}
使用工具：{tool_name}

执行结果：
{actual_data}

请以用户友好的方式总结关键信息，回复要简洁明了，突出最有价值的信息。"""

                try:
                    response = await self.api_client.send_chat_request([
                        {"role": "system", "content": "你是芊芊，用户的智能助手。请用简洁友好的语言总结任务结果。"},
                        {"role": "user", "content": summary_prompt}
                    ], stream=False)

                    if hasattr(response, 'data') and response.data:
                        ai_summary = response.data['choices'][0]['message']['content'].strip()
                        return f"✅ {agent_name}已经完成了任务！\n\n{ai_summary}"
                except Exception as e:
                    self.logger.warning(f"AI总结失败，使用默认格式: {e}")

            # 4. 如果AI总结失败，提供格式化的原始结果
            if actual_data:
                # 根据工具类型提供不同的展示格式
                if tool_name == 'get_weather':
                    return f"✅ {agent_name}已经完成了任务！\n\n📋 任务：{task_description}\n\n🌤️ 天气信息：\n{actual_data}"
                elif tool_name == 'web_search':
                    return f"✅ {agent_name}已经完成了任务！\n\n📋 任务：{task_description}\n\n🔍 搜索结果：\n{actual_data}"
                else:
                    return f"✅ {agent_name}已经完成了任务！\n\n📋 任务：{task_description}\n\n📊 执行结果：\n{actual_data}"
            else:
                return f"✅ {agent_name}已经完成了任务！\n\n📋 任务：{task_description}\n\n✅ 任务执行成功"

        except Exception as e:
            self.logger.error(f"生成智能总结失败: {e}")
            return f"✅ {agent_name}已经完成了任务！\n\n📋 任务：{task_description}\n\n✅ 任务执行成功"

    async def _get_bulletin_board_content(self, board_id: str) -> str:
        """获取公告板内容（已废弃）"""
        self.logger.warning("⚠️ 公告板系统已废弃，请使用基于文件的工作流系统")
        return f"工作流 #{board_id} 的公告板内容暂时无法获取（公告板系统已废弃）"

    async def _find_conversation_for_task(self, board_id: str) -> Optional[str]:
        """根据任务board_id查找对应的对话ID"""
        try:
            # 这里需要实现查找逻辑
            # 暂时返回当前活跃的对话ID（简化实现）
            # 在实际应用中，应该维护任务ID到对话ID的映射

            # 获取最近的对话
            if hasattr(self.conversation_manager, 'list_active_conversations'):
                conversations_result = self.conversation_manager.list_active_conversations()
                # 检查是否是协程
                if asyncio.iscoroutine(conversations_result):
                    conversations_result = await conversations_result

                if hasattr(conversations_result, 'success') and conversations_result.success and conversations_result.data:
                    # 返回最新的对话ID
                    latest_conversation = conversations_result.data[0]
                    return latest_conversation.get('id')

            # 如果没有找到对话，返回一个默认的对话ID
            # 这样可以避免None导致的问题
            return "default_conversation"

        except Exception as e:
            self.logger.error(f"查找对话失败: {e}")
            return "default_conversation"

    async def _generate_complete_workflow_summary(self, board_id: str, workflow_info: Dict) -> str:
        """生成完整的工作流汇总"""
        try:
            # 1. 收集公告板完整信息
            board_data = await self._collect_complete_board_data(board_id)

            # 2. 分析工作流类型
            workflow_type = self._analyze_workflow_type(board_data)

            # 3. 生成智能汇总
            if self.api_client:
                return await self._generate_ai_workflow_summary(board_data, workflow_type, workflow_info)
            else:
                return await self._generate_fallback_summary(board_data, workflow_type, workflow_info)

        except Exception as e:
            self.logger.error(f"生成工作流汇总失败: {e}")
            return f"✅ 工作流已完成！共处理了 {workflow_info.get('total_tasks', 0)} 个任务。"

    async def _collect_complete_board_data(self, board_id: str) -> Dict:
        """收集公告板完整数据"""
        try:
            # 获取公告板内容
            bulletin_board_content = await self._get_bulletin_board_content(board_id)

            # 这里可以扩展收集更多信息
            return {
                "board_id": board_id,
                "bulletin_board_content": bulletin_board_content,
                "agents_work": [],  # 可以从公告板内容中解析
                "board_messages": [],  # 可以从公告板内容中解析
                "workflow_type": "unknown"
            }
        except Exception as e:
            self.logger.error(f"收集公告板数据失败: {e}")
            return {"board_id": board_id, "bulletin_board_content": "公告板系统已废弃", "agents_work": [], "board_messages": []}

    def _analyze_workflow_type(self, board_data: Dict) -> str:
        """分析工作流类型"""
        try:
            bulletin_content = board_data.get('bulletin_board_content', '').lower()

            # 检查是否有文件操作相关的关键词
            file_keywords = ['保存', 'save', '文件', 'file', '写入', 'write', '存储', 'store']
            has_file_operation = any(keyword in bulletin_content for keyword in file_keywords)

            return "search_and_save" if has_file_operation else "pure_query"
        except Exception as e:
            self.logger.error(f"分析工作流类型失败: {e}")
            return "unknown"

    async def _generate_ai_workflow_summary(self, board_data: Dict, workflow_type: str, workflow_info: Dict) -> str:
        """使用AI生成工作流汇总"""
        try:
            # 构建汇总提示词
            prompt = self._build_workflow_summary_prompt(board_data, workflow_type, workflow_info)

            response = await self.api_client.send_chat_request([
                {"role": "system", "content": "你是芊芊，用户的智能助手。请根据工作流完成情况生成简洁友好的汇总。"},
                {"role": "user", "content": prompt}
            ], stream=False)

            if hasattr(response, 'data') and response.data:
                return response.data['choices'][0]['message']['content'].strip()
            else:
                return await self._generate_fallback_summary(board_data, workflow_type, workflow_info)

        except Exception as e:
            self.logger.error(f"AI汇总生成失败: {e}")
            return await self._generate_fallback_summary(board_data, workflow_type, workflow_info)

    def _build_workflow_summary_prompt(self, board_data: Dict, workflow_type: str, workflow_info: Dict) -> str:
        """构建工作流汇总提示词"""
        total_tasks = workflow_info.get('total_tasks', 0)
        bulletin_content = board_data.get('bulletin_board_content', '')

        if workflow_type == "search_and_save":
            return f"""请为用户总结这次多Agent协作的工作流完成情况：

工作流类型：搜索+文件保存
总任务数：{total_tasks}个
工作流状态：已完成

公告板内容：
{bulletin_content}

请按以下格式回复：
✅ 多Agent协作工作流已完成！

📊 [主题]搜索结果大致如下：
[关键信息摘要]

📁 详细内容已保存到相关文件

回复要简洁友好，突出协作完成的成果。"""
        else:
            return f"""请为用户总结这次多Agent协作的工作流完成情况：

工作流类型：纯信息查询
总任务数：{total_tasks}个
工作流状态：已完成

公告板内容：
{bulletin_content}

请按以下格式回复：
✅ 多Agent协作工作流已完成！

📊 查询结果详细信息：

[完整结构化内容]

回复要简洁友好，提供有价值的信息。"""

    async def _generate_fallback_summary(self, board_data: Dict, workflow_type: str, workflow_info: Dict) -> str:
        """生成备用汇总"""
        total_tasks = workflow_info.get('total_tasks', 0)

        if workflow_type == "search_and_save":
            return f"✅ 多Agent协作工作流已完成！{total_tasks}个专家协同工作，搜索结果已保存到文件。"
        else:
            return f"✅ 多Agent协作工作流已完成！{total_tasks}个专家协同工作，信息查询任务已完成。"

    async def _publish_ai_response_events(self, ai_message, conversation_id: str, content: str):
        """发布AI响应事件"""
        if hasattr(self, 'event_bus') and self.event_bus:
            # 发布响应开始事件
            result1 = self.event_bus.publish_simple("ai_response_started", {
                "message_id": ai_message.id,
                "conversation_id": conversation_id,
                "assistant_name": self._current_assistant_data.get("name", "芊芊") if self._current_assistant_data else "芊芊",
                "avatar_path": self._current_assistant_data.get("avatar_path") if self._current_assistant_data else None
            })
            if result1 is not None and asyncio.iscoroutine(result1):
                await result1

            # 发布内容块事件
            result2 = self.event_bus.publish_simple("ai_content_chunk", {
                "message_id": ai_message.id,
                "content": content,
                "conversation_id": conversation_id,
                "assistant_name": self._current_assistant_data.get("name", "芊芊") if self._current_assistant_data else "芊芊",
                "avatar_path": self._current_assistant_data.get("avatar_path") if self._current_assistant_data else None
            })
            if result2 is not None and asyncio.iscoroutine(result2):
                await result2

            # 发布响应完成事件（包含文本内容用于TTS自动播放）
            result3 = self.event_bus.publish_simple("ai_response_finished", {
                "message_id": ai_message.id,
                "conversation_id": conversation_id,
                "text": content  # 添加文本内容用于TTS自动播放
            })
            if result3 is not None and asyncio.iscoroutine(result3):
                await result3

            # 发布TTS请求事件（新的事件处理方式）
            if content and content.strip():
                result4 = self.event_bus.publish_simple("tts_request", {
                    "text": content,
                    "conversation_id": conversation_id
                })
                if result4 is not None and asyncio.iscoroutine(result4):
                    await result4

    def _get_workflow_coordination_content(self) -> str:
        """生成模式指导内容（双向动态，从config.env读取）"""
        try:
            # 检查是否启用工作流模式
            if (hasattr(self, '_current_assistant_data') and
                self._current_assistant_data and
                self._current_assistant_data.get('workflow_enabled', False)):

                # 工作流模式：读取工作流提示词
                workflow_prompt_config = self.foundation.get_config("workflow.prompt")
                if workflow_prompt_config:
                    # 检查是否是文件路径（以.txt结尾）
                    if workflow_prompt_config.endswith('.txt'):
                        try:
                            # 从txt文件读取提示词内容
                            from pathlib import Path
                            prompt_file = Path(workflow_prompt_config)
                            if prompt_file.exists():
                                workflow_prompt = prompt_file.read_text(encoding='utf-8')
                                self.logger.debug(f"🤖 从文件读取工作流提示词: {workflow_prompt_config}")
                            else:
                                self.logger.warning(f"⚠️ 工作流提示词文件不存在: {workflow_prompt_config}")
                                workflow_prompt = workflow_prompt_config  # 回退到配置内容
                        except Exception as e:
                            self.logger.error(f"❌ 读取工作流提示词文件失败: {e}")
                            workflow_prompt = workflow_prompt_config  # 回退到配置内容
                    else:
                        # 直接使用配置内容
                        workflow_prompt = workflow_prompt_config

                    return f"""

## 🤖 工作流协调模式

你现在处于**工作流协调模式**，作为智能任务分析师和专家团队协调员：

### 🎯 你的角色定位
- **身份**：专家团队的协调员和任务分析师
- **职责**：理解用户需求，分析任务复杂度，合理分配给专业团队
- **工作方式**：不直接执行具体操作，而是智能派发任务给专家

{workflow_prompt}

### ⚡ 工作流程
1. **理解需求** - 分析用户请求的复杂度和专业性
2. **选择专家** - 根据任务性质选择合适的专家团队
3. **派发任务** - 使用标准格式清晰地分配任务
4. **协调跟进** - 关注任务进展，及时向用户汇报

保持你的个性化特征，同时发挥专业协调员的作用！
"""
                else:
                    # 工作流模式兜底
                    return self._get_default_workflow_content()
            else:
                # 直接执行模式：读取直接执行提示词
                direct_mode_config = self.foundation.get_config("direct.mode.prompt")
                if direct_mode_config:
                    # 检查是否是文件路径（以.txt结尾）
                    if direct_mode_config.endswith('.txt'):
                        try:
                            # 从txt文件读取提示词内容
                            from pathlib import Path
                            prompt_file = Path(direct_mode_config)
                            if prompt_file.exists():
                                direct_mode_prompt = prompt_file.read_text(encoding='utf-8')
                                self.logger.debug(f"🔧 从文件读取直接执行提示词: {direct_mode_config}")
                            else:
                                self.logger.warning(f"⚠️ 直接执行提示词文件不存在: {direct_mode_config}")
                                direct_mode_prompt = direct_mode_config  # 回退到配置内容
                        except Exception as e:
                            self.logger.error(f"❌ 读取直接执行提示词文件失败: {e}")
                            direct_mode_prompt = direct_mode_config  # 回退到配置内容
                    else:
                        # 直接使用配置内容
                        direct_mode_prompt = direct_mode_config

                    return f"""

## 🔧 直接执行模式

{direct_mode_prompt}
"""
                else:
                    # 直接执行模式兜底
                    return self._get_default_direct_mode_content()

        except Exception as e:
            self.logger.error(f"❌ Error reading mode configuration: {e}")
            return ""

    def _get_default_workflow_content(self) -> str:
        """获取默认工作流内容（兜底方案）"""
        return """

## 🤖 工作流协调模式

你现在处于**工作流协调模式**，作为智能任务分析师和专家团队协调员：

### 🎯 你的角色定位
- **身份**：专家团队的协调员和任务分析师
- **职责**：理解用户需求，分析任务复杂度，合理分配给专业团队
- **工作方式**：不直接执行具体操作，而是智能派发任务给专家

### 👥 可调用的专家团队
- **搜索专家**：网络搜索、信息收集、资料查找
- **文件专家**：文件读写、数据保存、文档整理
- **天气专家**：天气查询、气象信息、天气预报
- **通知专家**：钉钉通知、消息推送、提醒服务
- **音乐专家**：音乐播放、歌曲搜索、播放控制
- **图像专家**：图片处理、图像分析、表情包管理
- **浏览器专家**：网页操作、内容提取、自动化任务
- **记忆专家**：记忆管理、日记服务、信息存储
- **翻译专家**：多语言翻译、语言转换服务
- **调度专家**：任务调度、时间管理、定时任务
- **系统专家**：系统管理、配置调整、状态监控

### 📋 任务派发格式
```
task start
1. 专家名称：具体任务描述
2. 专家名称：具体任务描述
task end
```

保持你的个性化特征，同时发挥专业协调员的作用！
"""

    def _get_default_direct_mode_content(self) -> str:
        """获取默认直接执行模式内容（兜底方案）"""
        return """

## 🔧 直接执行模式

你现在处于**直接执行模式**，可以直接使用所有可用的工具来完成用户请求：

### 🎯 你的角色定位
- **身份**：全能执行者，拥有完整的工具权限
- **职责**：直接理解并执行用户请求，无需派发给其他专家
- **工作方式**：直接调用相应工具，快速高效地完成任务

### 🛠️ 可用工具
你拥有完整的工具权限，包括搜索、文件操作、天气查询、通知发送、音乐播放、图像处理、浏览器操作等功能。

### 💡 工作方式
- **直接执行**：无需派发任务，直接调用相应工具
- **即时响应**：快速高效地完成用户请求
- **工具组合**：可以连续使用多个工具完成复杂任务

### ⚡ 执行原则
1. **理解需求** - 分析用户请求的具体要求
2. **选择工具** - 根据任务性质选择合适的工具
3. **直接执行** - 立即调用工具完成操作
4. **结果反馈** - 提供清晰的执行结果

记住：你现在是全能执行者，不是协调员！直接使用工具，不要使用task start/end格式！
"""

    async def _initialize_all_file_based_agents(self):
        """通用的专家发现和启动系统"""
        try:
            self.logger.info("🚀 开始初始化所有基于文件的专家代理...")

            # 导入必要的模块
            from ...core.workflow.file_based_autonomous_agent import FileBasedAutonomousAgent
            from ...core.workflow.configuration_engine import ConfigurationEngine

            # 创建配置引擎
            config_engine = ConfigurationEngine()

            # VCP系统不需要设置插件管理器
            # config_engine.set_plugin_manager(self.plugin_service)

            # 加载所有专家配置
            expert_configs = await config_engine.load_expert_configurations()

            self.file_based_agents = {}

            # 为每个配置的专家创建代理
            for expert_name, config in expert_configs.items():
                try:
                    # 创建专家代理实例
                    agent = FileBasedAutonomousAgent(
                        name=config.name,
                        specialty=expert_name,
                        agent_role=config.agent_role,
                        api_client=self.api_client,
                        plugin_manager=self.plugin_service.vcp_service
                    )

                    # 初始化专家代理
                    await agent.initialize()

                    # 启动文件监听
                    import asyncio
                    asyncio.create_task(agent.start_file_monitoring())

                    # 存储到字典中
                    self.file_based_agents[config.agent_role] = agent

                    self.logger.info(f"✅ 专家代理已启动: {config.name}({expert_name}) - {config.agent_role}")

                except Exception as e:
                    self.logger.error(f"❌ 初始化专家代理失败 {expert_name}: {e}")
                    continue

            self.logger.info(f"🎉 成功启动 {len(self.file_based_agents)} 个专家代理")

        except Exception as e:
            self.logger.error(f"❌ 初始化专家代理系统失败: {e}")
            self.file_based_agents = {}




























