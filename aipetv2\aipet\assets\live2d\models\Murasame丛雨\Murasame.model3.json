{"Version": 3, "FileReferences": {"Moc": "Murasame.moc3", "Textures": ["Murasame.4096/texture_00.png"], "Physics": "Murasame.physics3.json", "PhysicsV2": {"File": "Murasame.physics3.json"}, "Motions": {"Idle": [{"File": "motion/motion01.motion3.json", "Interruptable": true}], "Tapface": [{"File": "motion/motion02.motion3.json", "Sound": "sounds/bandicam 2021-11-23 02-18-04-516.mp4.wav", "Text": "吾名丛雨，乃是这“丛雨丸”的管理者……简单来说，也算是“丛雨丸”的灵魂", "Interruptable": true}, {"File": "motion/motion03.motion3.json", "Sound": "sounds/bandicam 2021-11-23 02-19-03-123.mp4.wav", "Text": "你，就是本座的主人？", "Interruptable": true}], "Taphair": [{"File": "motion/motion04.motion3.json", "Sound": "sounds/bandicam 2021-11-23 02-19-13-234.mp4.wav", "Text": "在这里，这里", "Interruptable": true}, {"File": "motion/motion05.motion3.json", "Sound": "sounds/bandicam 2021-11-23 02-19-22-154.mp4.wav", "Text": "你看，复原了", "Interruptable": true}], "Tapxiongbu": [{"File": "motion/motion07.motion3.json", "Sound": "sounds/bandicam 2021-11-23 02-19-37-560.mp4.wav", "Text": "主人就是主人。是你拔出了丛雨丸吧？", "Interruptable": true}, {"File": "motion/motion10.motion3.json", "Sound": "sounds/bandicam 2021-11-23 02-20-13-844.mp4.wav", "Text": "你这————！！", "Interruptable": true}], "Tapqunzi": [{"File": "motion/motion06.motion3.json", "Sound": "sounds/bandicam 2021-11-23 02-19-30-578.mp4.wav", "Text": "——着陆", "Interruptable": true}, {"File": "motion/motion08.motion3.json", "Sound": "sounds/bandicam 2021-11-23 02-19-47-794.mp4.wav", "Text": "本座才不是幽灵！完全不是！不要把幽灵和本座相提并论！", "Interruptable": true}], "Tapleg": [{"File": "motion/motion09.motion3.json", "Sound": "sounds/bandicam 2021-11-23 02-19-58-125.mp4.wav", "Text": "哪是什么幽灵，别……别别别把本座和那种毫无事实依据的东西混为一谈", "Interruptable": true}, {"File": "motion/motion11.motion3.json", "Sound": "sounds/bandicam 2021-11-23 02-20-24-955.mp4.wav", "Text": "你醒了吗，主人。早上好", "Interruptable": true}, {"File": "motion/motion12.motion3.json", "Sound": "sounds/bandicam 2021-11-23 02-20-39-029.mp4.wav", "Text": "本座不是幻觉，更不是幽灵，主人！", "Interruptable": true}]}, "Expressions": [{"Name": "exp1.exp3", "File": "exp/exp1.exp3.json"}, {"Name": "exp2.exp3", "File": "exp/exp2.exp3.json"}, {"Name": "exp3.exp3", "File": "exp/exp3.exp3.json"}, {"Name": "exp4.exp3", "File": "exp/exp4.exp3.json"}, {"Name": "exp5.exp3", "File": "exp/exp5.exp3.json"}, {"Name": "exp6.exp3", "File": "exp/exp6.exp3.json"}, {"Name": "exp7.exp3", "File": "exp/exp7.exp3.json"}]}, "Controllers": {"ParamHit": {}, "ParamLoop": {}, "KeyTrigger": {}, "ParamTrigger": {}, "AreaTrigger": {}, "HandTrigger": {}, "EyeBlink": {"MinInterval": 500, "MaxInterval": 6000, "Enabled": true}, "LipSync": {"Gain": 5.0}, "MouseTracking": {"SmoothTime": 0.15, "Items": [{"Id": "ParamAngleX", "Min": -30.0, "Max": 30.0, "BlendMode": 1, "Input": 0}, {"Id": "ParamAngleY", "Min": -30.0, "Max": 30.0, "BlendMode": 1, "Axis": 1, "Input": 0}, {"Id": "ParamAngleZ", "Min": -30.0, "Max": 30.0, "BlendMode": 1, "Input": 0}, {"Id": "ParamEyeBallX", "Min": -1.0, "Max": 1.0, "BlendMode": 1, "Input": 0}, {"Id": "ParamEyeBallY", "Min": -1.0, "Max": 1.0, "BlendMode": 1, "Axis": 1, "Input": 0}, {"Id": "ParamBodyAngleX", "Min": -10.0, "Max": 10.0, "BlendMode": 1, "Input": 0}, {"Id": "ParamBodyAngleY", "Min": -10.0, "Max": 10.0, "BlendMode": 1, "Axis": 1, "Input": 0}, {"Id": "ParamBodyAngleZ", "Min": -10.0, "Max": 10.0, "BlendMode": 1, "Input": 0}], "Enabled": true}, "AutoBreath": {"Enabled": true}, "ExtraMotion": {"Enabled": true}, "Accelerometer": {"Enabled": true}, "Microphone": {}, "Transform": {}, "FaceTracking": {"AngleX": [{"Id": "ParamAngleX", "Min": -30.0, "Max": 30.0, "DefaultValue": 0.0, "Input": 0}], "AngleY": [{"Id": "ParamAngleY", "Min": -30.0, "Max": 30.0, "DefaultValue": 0.0, "Input": 0}], "AngleZ": [{"Id": "ParamAngleZ", "Min": -30.0, "Max": 30.0, "DefaultValue": 0.0, "Inverted": true, "Input": 0}], "PositionX": [{"Id": "ParamBodyAngleX", "Min": -10.0, "Max": 10.0, "DefaultValue": 0.0, "Input": 0}], "PositionY": [{"Id": "ParamBodyAngleY", "Min": -10.0, "Max": 10.0, "DefaultValue": 0.0, "Input": 0}], "PositionZ": [{"Id": "ParamBodyAngleZ", "Min": -10.0, "Max": 10.0, "DefaultValue": 0.0, "Input": 0}], "EyeLOpen": [{"Id": "ParamEyeLOpen", "Min": 0.0, "Max": 1.0, "DefaultValue": 1.0, "Input": 0}], "EyeROpen": [{"Id": "ParamEyeROpen", "Min": 0.0, "Max": 1.0, "DefaultValue": 1.0, "Input": 0}], "EyeballX": [{"Id": "ParamEyeBallX", "Min": -1.0, "Max": 1.0, "DefaultValue": 0.0, "Input": 0}], "EyeballY": [{"Id": "ParamEyeBallY", "Min": -1.0, "Max": 1.0, "DefaultValue": 0.0, "Input": 0}], "BrowLY": [{"Id": "ParamBrowLY", "Min": -1.0, "Max": 1.0, "DefaultValue": 0.0, "Input": 0}], "BrowRY": [{"Id": "ParamBrowRY", "Min": -1.0, "Max": 1.0, "DefaultValue": 0.0, "Input": 0}], "MouthOpenY": [{"Id": "ParamMouthOpenY", "Min": 0.0, "Max": 1.0, "DefaultValue": 0.0, "Input": 0}], "MouthForm": [{"Id": "ParamMouthForm", "Min": -1.0, "Max": 1.0, "DefaultValue": 0.0, "Inverted": true, "Input": 0}], "Enabled": true}, "HandTracking": {}, "ParamValue": {}, "PartOpacity": {}, "ArtmeshOpacity": {}, "ArtmeshColor": {}, "ArtmeshCulling": {"DefaultMode": 0}, "IntimacySystem": {}}, "HitAreas": [{"Name": "face", "Id": "HitArea", "Motion": "<PERSON><PERSON><PERSON>"}, {"Name": "hair", "Id": "HitArea2", "Motion": "<PERSON><PERSON><PERSON>"}, {"Name": "xiongbu", "Id": "HitArea3", "Motion": "Tapxiongbu"}, {"Name": "qunzi", "Id": "HitArea4", "Motion": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Name": "leg", "Id": "HitArea5", "Motion": "<PERSON><PERSON><PERSON>"}], "Options": {"ScaleFactor": 0.7}}