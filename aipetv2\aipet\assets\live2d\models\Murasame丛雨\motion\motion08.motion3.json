{"Version": 3, "Meta": {"Duration": 7, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 92, "TotalSegmentCount": 7740, "TotalPointCount": 7832, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Model", "Id": "Opacity", "Segments": [0, 1, 0, 7, 1]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 2, 2.033, -2.255, 2, 2.067, -7.726, 2, 2.1, -15, 2, 2.133, -22.274, 2, 2.167, -27.745, 2, 2.2, -30, 2, 2.233, -29.154, 2, 2.267, -26.693, 2, 2.3, -22.5, 2, 2.333, -16.615, 2, 2.367, -9.232, 2, 2.4, 0, 2, 2.433, 9.232, 2, 2.467, 16.615, 2, 2.5, 22.5, 2, 2.533, 26.693, 2, 2.567, 29.154, 2, 2.6, 30, 2, 2.633, 29.154, 2, 2.667, 26.693, 2, 2.7, 22.5, 2, 2.733, 16.615, 2, 2.767, 9.232, 2, 2.8, 0, 2, 2.833, -9.232, 2, 2.867, -16.615, 2, 2.9, -22.5, 2, 2.933, -26.693, 2, 2.967, -29.154, 2, 3, -30, 2, 3.033, -27.745, 2, 3.067, -22.274, 2, 3.1, -15, 2, 3.133, -7.726, 2, 3.167, -2.255, 2, 3.2, 0, 2, 3.6, 0.645, 2, 3.633, 2.344, 2, 3.667, 4.746, 2, 3.7, 7.5, 2, 3.733, 10.254, 2, 3.767, 12.656, 2, 3.8, 14.355, 2, 3.833, 15, 2, 3.867, 14.101, 2, 3.9, 11.691, 2, 3.933, 7.969, 2, 3.967, 3.294, 2, 4, -1.884, 2, 4.033, -7.5, 2, 4.067, -13.116, 2, 4.1, -18.294, 2, 4.133, -22.969, 2, 4.167, -26.691, 2, 4.2, -29.101, 2, 4.233, -30, 2, 7, -30]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 2, 0.033, 0.483, 2, 0.067, 1.778, 2, 0.1, 3.652, 2, 0.133, 5.873, 2, 0.167, 8.167, 2, 0.2, 10.388, 2, 0.233, 12.262, 2, 0.267, 13.557, 2, 0.3, 14.04, 2, 0.333, 13.747, 2, 0.367, 12.907, 2, 0.4, 11.609, 2, 0.433, 9.857, 2, 0.467, 7.73, 2, 0.5, 5.286, 2, 0.533, 2.641, 2, 0.567, -0.259, 2, 0.6, -3.303, 2, 0.633, -6.433, 2, 0.667, -9.527, 2, 0.7, -12.657, 2, 0.733, -15.701, 2, 0.767, -18.601, 2, 0.8, -21.246, 2, 0.833, -23.69, 2, 0.867, -25.817, 2, 0.9, -27.569, 2, 0.933, -28.867, 2, 0.967, -29.707, 2, 1, -30, 2, 1.033, -29.785, 2, 1.067, -29.219, 2, 1.1, -28.418, 2, 1.133, -27.5, 2, 1.167, -26.582, 2, 1.2, -25.781, 2, 1.233, -25.215, 2, 1.267, -25, 2, 1.3, -25.031, 2, 1.333, -25.116, 2, 1.367, -25.255, 2, 1.4, -25.435, 2, 1.433, -25.659, 2, 1.467, -25.911, 2, 1.5, -26.198, 2, 1.533, -26.5, 2, 1.567, -26.826, 2, 1.6, -27.157, 2, 1.633, -27.5, 2, 1.667, -27.843, 2, 1.7, -28.174, 2, 1.733, -28.5, 2, 1.767, -28.802, 2, 1.8, -29.089, 2, 1.833, -29.341, 2, 1.867, -29.565, 2, 1.9, -29.745, 2, 1.933, -29.884, 2, 1.967, -29.969, 2, 2, -30, 2, 7, -30]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.033, 0.999, 2, 0.067, 0.996, 2, 0.1, 0.992, 2, 0.133, 0.985, 2, 0.167, 0.978, 2, 0.2, 0.969, 2, 0.233, 0.959, 2, 0.267, 0.947, 2, 0.3, 0.935, 2, 0.333, 0.922, 2, 0.367, 0.908, 2, 0.4, 0.894, 2, 0.433, 0.88, 2, 0.467, 0.865, 2, 0.5, 0.85, 2, 0.533, 0.835, 2, 0.567, 0.82, 2, 0.6, 0.805, 2, 0.633, 0.791, 2, 0.667, 0.777, 2, 0.7, 0.764, 2, 0.733, 0.752, 2, 0.767, 0.741, 2, 0.8, 0.731, 2, 0.833, 0.722, 2, 0.867, 0.714, 2, 0.9, 0.708, 2, 0.933, 0.704, 2, 0.967, 0.701, 2, 1, 0.7, 2, 1.867, 0.73, 2, 1.9, 0.804, 2, 1.933, 0.894, 2, 1.967, 0.969, 2, 2, 1, 2, 2.033, 0.925, 2, 2.067, 0.742, 2, 2.1, 0.5, 2, 2.133, 0.258, 2, 2.167, 0.075, 2, 2.2, 0, 2, 3.1, 0.075, 2, 3.133, 0.258, 2, 3.167, 0.5, 2, 3.2, 0.742, 2, 3.233, 0.925, 2, 3.267, 1, 2, 4.5, 0.897, 2, 4.533, 0.65, 2, 4.567, 0.35, 2, 4.6, 0.103, 2, 4.633, 0, 2, 4.667, 0.103, 2, 4.7, 0.35, 2, 4.733, 0.65, 2, 4.767, 0.897, 2, 4.8, 1, 2, 7, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 1, 2, 7, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.033, 0.999, 2, 0.067, 0.996, 2, 0.1, 0.992, 2, 0.133, 0.985, 2, 0.167, 0.978, 2, 0.2, 0.969, 2, 0.233, 0.959, 2, 0.267, 0.947, 2, 0.3, 0.935, 2, 0.333, 0.922, 2, 0.367, 0.908, 2, 0.4, 0.894, 2, 0.433, 0.88, 2, 0.467, 0.865, 2, 0.5, 0.85, 2, 0.533, 0.835, 2, 0.567, 0.82, 2, 0.6, 0.805, 2, 0.633, 0.791, 2, 0.667, 0.777, 2, 0.7, 0.764, 2, 0.733, 0.752, 2, 0.767, 0.741, 2, 0.8, 0.731, 2, 0.833, 0.722, 2, 0.867, 0.714, 2, 0.9, 0.708, 2, 0.933, 0.704, 2, 0.967, 0.701, 2, 1, 0.7, 2, 1.867, 0.73, 2, 1.9, 0.804, 2, 1.933, 0.894, 2, 1.967, 0.969, 2, 2, 1, 2, 2.033, 0.925, 2, 2.067, 0.742, 2, 2.1, 0.5, 2, 2.133, 0.258, 2, 2.167, 0.075, 2, 2.2, 0, 2, 3.1, 0.075, 2, 3.133, 0.258, 2, 3.167, 0.5, 2, 3.2, 0.742, 2, 3.233, 0.925, 2, 3.267, 1, 2, 4.5, 0.897, 2, 4.533, 0.65, 2, 4.567, 0.35, 2, 4.6, 0.103, 2, 4.633, 0, 2, 4.667, 0.103, 2, 4.7, 0.35, 2, 4.733, 0.65, 2, 4.767, 0.897, 2, 4.8, 1, 2, 7, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 1, 2, 7, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 2, 3.6, -0.021, 2, 3.633, -0.078, 2, 3.667, -0.158, 2, 3.7, -0.25, 2, 3.733, -0.342, 2, 3.767, -0.422, 2, 3.8, -0.479, 2, 3.833, -0.5, 2, 3.867, -0.47, 2, 3.9, -0.39, 2, 3.933, -0.266, 2, 3.967, -0.11, 2, 4, 0.063, 2, 4.033, 0.25, 2, 4.067, 0.437, 2, 4.1, 0.61, 2, 4.133, 0.766, 2, 4.167, 0.89, 2, 4.2, 0.97, 2, 4.233, 1, 2, 7, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 2, 0.033, 0.003, 2, 0.067, 0.013, 2, 0.1, 0.028, 2, 0.133, 0.049, 2, 0.167, 0.074, 2, 0.2, 0.104, 2, 0.233, 0.138, 2, 0.267, 0.175, 2, 0.3, 0.216, 2, 0.333, 0.259, 2, 0.367, 0.305, 2, 0.4, 0.352, 2, 0.433, 0.401, 2, 0.467, 0.45, 2, 0.5, 0.5, 2, 0.533, 0.55, 2, 0.567, 0.599, 2, 0.6, 0.648, 2, 0.633, 0.695, 2, 0.667, 0.741, 2, 0.7, 0.784, 2, 0.733, 0.825, 2, 0.767, 0.862, 2, 0.8, 0.896, 2, 0.833, 0.926, 2, 0.867, 0.951, 2, 0.9, 0.972, 2, 0.933, 0.987, 2, 0.967, 0.997, 2, 1, 1, 2, 7, 1]}, {"Target": "Parameter", "Id": "ParamYanZhuSuoFangL", "Segments": [0, 0, 2, 0.033, 0.003, 2, 0.067, 0.01, 2, 0.1, 0.019, 2, 0.133, 0.026, 2, 0.167, 0.029, 2, 0.2, 0.028, 2, 0.233, 0.028, 2, 0.267, 0.027, 2, 0.3, 0.025, 2, 0.333, 0.023, 2, 0.367, 0.021, 2, 0.4, 0.019, 2, 0.433, 0.016, 2, 0.467, 0.013, 2, 0.5, 0.011, 2, 0.533, 0.008, 2, 0.567, 0.004, 2, 0.6, 0.001, 2, 0.633, -0.002, 2, 0.667, -0.005, 2, 0.7, -0.008, 2, 0.733, -0.01, 2, 0.767, -0.013, 2, 0.8, -0.015, 2, 0.833, -0.018, 2, 0.867, -0.019, 2, 0.9, -0.021, 2, 0.933, -0.022, 2, 0.967, -0.023, 2, 1, -0.023, 2, 1.033, -0.02, 2, 1.067, -0.015, 2, 1.1, -0.007, 2, 1.133, 0.001, 2, 1.167, 0.007, 2, 1.2, 0.01, 2, 1.233, 0.009, 2, 1.267, 0.006, 2, 1.3, 0.003, 2, 1.333, -0.001, 2, 1.367, -0.003, 2, 1.4, -0.004, 2, 1.467, -0.004, 2, 1.5, -0.003, 2, 1.533, -0.001, 2, 1.567, 0, 2, 1.6, 0.001, 2, 1.633, 0.002, 2, 1.667, -0.011, 2, 1.7, -0.044, 2, 1.733, -0.093, 2, 1.767, -0.15, 2, 1.8, -0.209, 2, 1.833, -0.267, 2, 1.867, -0.315, 2, 1.9, -0.348, 2, 1.933, -0.361, 2, 1.967, -0.241, 2, 2, 0.045, 2, 2.033, 0.395, 2, 2.067, 0.681, 2, 2.1, 0.801, 2, 2.133, 0.652, 2, 2.167, 0.296, 2, 2.2, -0.137, 2, 2.233, -0.493, 2, 2.267, -0.641, 2, 2.3, -0.594, 2, 2.333, -0.473, 2, 2.367, -0.304, 2, 2.4, -0.126, 2, 2.433, 0.043, 2, 2.467, 0.165, 2, 2.5, 0.212, 2, 2.533, 0.189, 2, 2.567, 0.134, 2, 2.6, 0.062, 2, 2.633, -0.011, 2, 2.667, -0.066, 2, 2.7, -0.089, 2, 2.733, -0.079, 2, 2.767, -0.056, 2, 2.8, -0.025, 2, 2.833, 0.005, 2, 2.867, 0.028, 2, 2.9, 0.038, 2, 2.933, 0.006, 2, 2.967, -0.078, 2, 3, -0.198, 2, 3.033, -0.334, 2, 3.067, -0.471, 2, 3.1, -0.59, 2, 3.133, -0.675, 2, 3.167, -0.707, 2, 3.2, -0.567, 2, 3.233, -0.234, 2, 3.267, 0.173, 2, 3.3, 0.507, 2, 3.333, 0.646, 2, 3.367, 0.599, 2, 3.4, 0.477, 2, 3.433, 0.307, 2, 3.467, 0.127, 2, 3.5, -0.043, 2, 3.533, -0.165, 2, 3.567, -0.212, 2, 3.6, -0.19, 2, 3.633, -0.135, 2, 3.667, -0.062, 2, 3.7, 0.011, 2, 3.733, 0.066, 2, 3.767, 0.089, 2, 3.8, 0.079, 2, 3.833, 0.056, 2, 3.867, 0.025, 2, 3.9, -0.005, 2, 3.933, -0.028, 2, 3.967, -0.038, 2, 4, -0.034, 2, 4.033, -0.024, 2, 4.067, -0.011, 2, 4.1, 0.002, 2, 4.133, 0.012, 2, 4.167, 0.016, 2, 4.2, 0.015, 2, 4.233, 0.01, 2, 4.267, 0.005, 2, 4.3, -0.001, 2, 4.333, -0.005, 2, 4.367, -0.007, 2, 4.4, 0.053, 2, 4.433, 0.197, 2, 4.467, 0.389, 2, 4.5, 0.581, 2, 4.533, 0.725, 2, 4.567, 0.785, 2, 4.6, 0.612, 2, 4.633, 0.2, 2, 4.667, -0.302, 2, 4.7, -0.714, 2, 4.733, -0.887, 2, 4.767, -0.651, 2, 4.8, -0.133, 2, 4.833, 0.384, 2, 4.867, 0.62, 2, 4.9, 0.574, 2, 4.933, 0.456, 2, 4.967, 0.291, 2, 5, 0.117, 2, 5.033, -0.047, 2, 5.067, -0.165, 2, 5.1, -0.211, 2, 5.133, -0.189, 2, 5.167, -0.134, 2, 5.2, -0.062, 2, 5.233, 0.011, 2, 5.267, 0.066, 2, 5.3, 0.088, 2, 5.333, 0.079, 2, 5.367, 0.056, 2, 5.4, 0.025, 2, 5.433, -0.005, 2, 5.467, -0.028, 2, 5.5, -0.037, 2, 5.533, -0.033, 2, 5.567, -0.024, 2, 5.6, -0.011, 2, 5.633, 0.002, 2, 5.667, 0.012, 2, 5.7, 0.016, 2, 5.733, 0.014, 2, 5.767, 0.01, 2, 5.8, 0.005, 2, 5.833, -0.001, 2, 5.867, -0.005, 2, 5.9, -0.007, 2, 5.933, -0.006, 2, 5.967, -0.004, 2, 6, -0.002, 2, 6.033, 0, 2, 6.067, 0.002, 2, 6.1, 0.003, 2, 6.133, 0.003, 2, 6.167, 0.002, 2, 6.2, 0.001, 2, 6.233, 0, 2, 6.267, -0.001, 2, 6.3, -0.001, 2, 6.367, -0.001, 2, 6.4, -0.001, 2, 6.433, 0, 2, 6.467, 0, 2, 6.5, 0, 2, 6.567, 0, 2, 6.6, 0, 2, 6.633, 0, 2, 6.667, 0, 2, 6.7, 0, 2, 6.733, 0, 2, 6.767, 0, 2, 6.8, 0, 2, 6.833, 0, 2, 6.9, 0, 2, 6.933, 0, 2, 6.967, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamYanZhuSuoFangR", "Segments": [0, 0, 2, 0.033, 0.003, 2, 0.067, 0.01, 2, 0.1, 0.019, 2, 0.133, 0.026, 2, 0.167, 0.029, 2, 0.2, 0.028, 2, 0.233, 0.028, 2, 0.267, 0.027, 2, 0.3, 0.025, 2, 0.333, 0.023, 2, 0.367, 0.021, 2, 0.4, 0.019, 2, 0.433, 0.016, 2, 0.467, 0.013, 2, 0.5, 0.011, 2, 0.533, 0.008, 2, 0.567, 0.004, 2, 0.6, 0.001, 2, 0.633, -0.002, 2, 0.667, -0.005, 2, 0.7, -0.008, 2, 0.733, -0.01, 2, 0.767, -0.013, 2, 0.8, -0.015, 2, 0.833, -0.018, 2, 0.867, -0.019, 2, 0.9, -0.021, 2, 0.933, -0.022, 2, 0.967, -0.023, 2, 1, -0.023, 2, 1.033, -0.02, 2, 1.067, -0.015, 2, 1.1, -0.007, 2, 1.133, 0.001, 2, 1.167, 0.007, 2, 1.2, 0.01, 2, 1.233, 0.009, 2, 1.267, 0.006, 2, 1.3, 0.003, 2, 1.333, -0.001, 2, 1.367, -0.003, 2, 1.4, -0.004, 2, 1.467, -0.004, 2, 1.5, -0.003, 2, 1.533, -0.001, 2, 1.567, 0, 2, 1.6, 0.001, 2, 1.633, 0.002, 2, 1.667, -0.011, 2, 1.7, -0.044, 2, 1.733, -0.093, 2, 1.767, -0.15, 2, 1.8, -0.209, 2, 1.833, -0.267, 2, 1.867, -0.315, 2, 1.9, -0.348, 2, 1.933, -0.361, 2, 1.967, -0.241, 2, 2, 0.045, 2, 2.033, 0.395, 2, 2.067, 0.681, 2, 2.1, 0.801, 2, 2.133, 0.652, 2, 2.167, 0.296, 2, 2.2, -0.137, 2, 2.233, -0.493, 2, 2.267, -0.641, 2, 2.3, -0.594, 2, 2.333, -0.473, 2, 2.367, -0.304, 2, 2.4, -0.126, 2, 2.433, 0.043, 2, 2.467, 0.165, 2, 2.5, 0.212, 2, 2.533, 0.189, 2, 2.567, 0.134, 2, 2.6, 0.062, 2, 2.633, -0.011, 2, 2.667, -0.066, 2, 2.7, -0.089, 2, 2.733, -0.079, 2, 2.767, -0.056, 2, 2.8, -0.025, 2, 2.833, 0.005, 2, 2.867, 0.028, 2, 2.9, 0.038, 2, 2.933, 0.006, 2, 2.967, -0.078, 2, 3, -0.198, 2, 3.033, -0.334, 2, 3.067, -0.471, 2, 3.1, -0.59, 2, 3.133, -0.675, 2, 3.167, -0.707, 2, 3.2, -0.567, 2, 3.233, -0.234, 2, 3.267, 0.173, 2, 3.3, 0.507, 2, 3.333, 0.646, 2, 3.367, 0.599, 2, 3.4, 0.477, 2, 3.433, 0.307, 2, 3.467, 0.127, 2, 3.5, -0.043, 2, 3.533, -0.165, 2, 3.567, -0.212, 2, 3.6, -0.19, 2, 3.633, -0.135, 2, 3.667, -0.062, 2, 3.7, 0.011, 2, 3.733, 0.066, 2, 3.767, 0.089, 2, 3.8, 0.079, 2, 3.833, 0.056, 2, 3.867, 0.025, 2, 3.9, -0.005, 2, 3.933, -0.028, 2, 3.967, -0.038, 2, 4, -0.034, 2, 4.033, -0.024, 2, 4.067, -0.011, 2, 4.1, 0.002, 2, 4.133, 0.012, 2, 4.167, 0.016, 2, 4.2, 0.015, 2, 4.233, 0.01, 2, 4.267, 0.005, 2, 4.3, -0.001, 2, 4.333, -0.005, 2, 4.367, -0.007, 2, 4.4, 0.053, 2, 4.433, 0.197, 2, 4.467, 0.389, 2, 4.5, 0.581, 2, 4.533, 0.725, 2, 4.567, 0.785, 2, 4.6, 0.612, 2, 4.633, 0.2, 2, 4.667, -0.302, 2, 4.7, -0.714, 2, 4.733, -0.887, 2, 4.767, -0.651, 2, 4.8, -0.133, 2, 4.833, 0.384, 2, 4.867, 0.62, 2, 4.9, 0.574, 2, 4.933, 0.456, 2, 4.967, 0.291, 2, 5, 0.117, 2, 5.033, -0.047, 2, 5.067, -0.165, 2, 5.1, -0.211, 2, 5.133, -0.189, 2, 5.167, -0.134, 2, 5.2, -0.062, 2, 5.233, 0.011, 2, 5.267, 0.066, 2, 5.3, 0.088, 2, 5.333, 0.079, 2, 5.367, 0.056, 2, 5.4, 0.025, 2, 5.433, -0.005, 2, 5.467, -0.028, 2, 5.5, -0.037, 2, 5.533, -0.033, 2, 5.567, -0.024, 2, 5.6, -0.011, 2, 5.633, 0.002, 2, 5.667, 0.012, 2, 5.7, 0.016, 2, 5.733, 0.014, 2, 5.767, 0.01, 2, 5.8, 0.005, 2, 5.833, -0.001, 2, 5.867, -0.005, 2, 5.9, -0.007, 2, 5.933, -0.006, 2, 5.967, -0.004, 2, 6, -0.002, 2, 6.033, 0, 2, 6.067, 0.002, 2, 6.1, 0.003, 2, 6.133, 0.003, 2, 6.167, 0.002, 2, 6.2, 0.001, 2, 6.233, 0, 2, 6.267, -0.001, 2, 6.3, -0.001, 2, 6.367, -0.001, 2, 6.4, -0.001, 2, 6.433, 0, 2, 6.467, 0, 2, 6.5, 0, 2, 6.567, 0, 2, 6.6, 0, 2, 6.633, 0, 2, 6.667, 0, 2, 6.7, 0, 2, 6.733, 0, 2, 6.767, 0, 2, 6.8, 0, 2, 6.833, 0, 2, 6.9, 0, 2, 6.933, 0, 2, 6.967, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamGaoGguangL", "Segments": [0, 0, 2, 0.033, 0.003, 2, 0.067, 0.01, 2, 0.1, 0.019, 2, 0.133, 0.026, 2, 0.167, 0.029, 2, 0.2, 0.028, 2, 0.233, 0.028, 2, 0.267, 0.027, 2, 0.3, 0.025, 2, 0.333, 0.023, 2, 0.367, 0.021, 2, 0.4, 0.019, 2, 0.433, 0.016, 2, 0.467, 0.013, 2, 0.5, 0.011, 2, 0.533, 0.008, 2, 0.567, 0.004, 2, 0.6, 0.001, 2, 0.633, -0.002, 2, 0.667, -0.005, 2, 0.7, -0.008, 2, 0.733, -0.01, 2, 0.767, -0.013, 2, 0.8, -0.015, 2, 0.833, -0.018, 2, 0.867, -0.019, 2, 0.9, -0.021, 2, 0.933, -0.022, 2, 0.967, -0.023, 2, 1, -0.023, 2, 1.033, -0.02, 2, 1.067, -0.015, 2, 1.1, -0.007, 2, 1.133, 0.001, 2, 1.167, 0.007, 2, 1.2, 0.01, 2, 1.233, 0.009, 2, 1.267, 0.006, 2, 1.3, 0.003, 2, 1.333, -0.001, 2, 1.367, -0.003, 2, 1.4, -0.004, 2, 1.467, -0.004, 2, 1.5, -0.003, 2, 1.533, -0.001, 2, 1.567, 0, 2, 1.6, 0.001, 2, 1.633, 0.002, 2, 1.667, -0.011, 2, 1.7, -0.044, 2, 1.733, -0.093, 2, 1.767, -0.15, 2, 1.8, -0.209, 2, 1.833, -0.267, 2, 1.867, -0.315, 2, 1.9, -0.348, 2, 1.933, -0.361, 2, 1.967, -0.241, 2, 2, 0.045, 2, 2.033, 0.395, 2, 2.067, 0.681, 2, 2.1, 0.801, 2, 2.133, 0.652, 2, 2.167, 0.296, 2, 2.2, -0.137, 2, 2.233, -0.493, 2, 2.267, -0.641, 2, 2.3, -0.594, 2, 2.333, -0.473, 2, 2.367, -0.304, 2, 2.4, -0.126, 2, 2.433, 0.043, 2, 2.467, 0.165, 2, 2.5, 0.212, 2, 2.533, 0.189, 2, 2.567, 0.134, 2, 2.6, 0.062, 2, 2.633, -0.011, 2, 2.667, -0.066, 2, 2.7, -0.089, 2, 2.733, -0.079, 2, 2.767, -0.056, 2, 2.8, -0.025, 2, 2.833, 0.005, 2, 2.867, 0.028, 2, 2.9, 0.038, 2, 2.933, 0.006, 2, 2.967, -0.078, 2, 3, -0.198, 2, 3.033, -0.334, 2, 3.067, -0.471, 2, 3.1, -0.59, 2, 3.133, -0.675, 2, 3.167, -0.707, 2, 3.2, -0.567, 2, 3.233, -0.234, 2, 3.267, 0.173, 2, 3.3, 0.507, 2, 3.333, 0.646, 2, 3.367, 0.599, 2, 3.4, 0.477, 2, 3.433, 0.307, 2, 3.467, 0.127, 2, 3.5, -0.043, 2, 3.533, -0.165, 2, 3.567, -0.212, 2, 3.6, -0.19, 2, 3.633, -0.135, 2, 3.667, -0.062, 2, 3.7, 0.011, 2, 3.733, 0.066, 2, 3.767, 0.089, 2, 3.8, 0.079, 2, 3.833, 0.056, 2, 3.867, 0.025, 2, 3.9, -0.005, 2, 3.933, -0.028, 2, 3.967, -0.038, 2, 4, -0.034, 2, 4.033, -0.024, 2, 4.067, -0.011, 2, 4.1, 0.002, 2, 4.133, 0.012, 2, 4.167, 0.016, 2, 4.2, 0.015, 2, 4.233, 0.01, 2, 4.267, 0.005, 2, 4.3, -0.001, 2, 4.333, -0.005, 2, 4.367, -0.007, 2, 4.4, 0.053, 2, 4.433, 0.197, 2, 4.467, 0.389, 2, 4.5, 0.581, 2, 4.533, 0.725, 2, 4.567, 0.785, 2, 4.6, 0.612, 2, 4.633, 0.2, 2, 4.667, -0.302, 2, 4.7, -0.714, 2, 4.733, -0.887, 2, 4.767, -0.651, 2, 4.8, -0.133, 2, 4.833, 0.384, 2, 4.867, 0.62, 2, 4.9, 0.574, 2, 4.933, 0.456, 2, 4.967, 0.291, 2, 5, 0.117, 2, 5.033, -0.047, 2, 5.067, -0.165, 2, 5.1, -0.211, 2, 5.133, -0.189, 2, 5.167, -0.134, 2, 5.2, -0.062, 2, 5.233, 0.011, 2, 5.267, 0.066, 2, 5.3, 0.088, 2, 5.333, 0.079, 2, 5.367, 0.056, 2, 5.4, 0.025, 2, 5.433, -0.005, 2, 5.467, -0.028, 2, 5.5, -0.037, 2, 5.533, -0.033, 2, 5.567, -0.024, 2, 5.6, -0.011, 2, 5.633, 0.002, 2, 5.667, 0.012, 2, 5.7, 0.016, 2, 5.733, 0.014, 2, 5.767, 0.01, 2, 5.8, 0.005, 2, 5.833, -0.001, 2, 5.867, -0.005, 2, 5.9, -0.007, 2, 5.933, -0.006, 2, 5.967, -0.004, 2, 6, -0.002, 2, 6.033, 0, 2, 6.067, 0.002, 2, 6.1, 0.003, 2, 6.133, 0.003, 2, 6.167, 0.002, 2, 6.2, 0.001, 2, 6.233, 0, 2, 6.267, -0.001, 2, 6.3, -0.001, 2, 6.367, -0.001, 2, 6.4, -0.001, 2, 6.433, 0, 2, 6.467, 0, 2, 6.5, 0, 2, 6.567, 0, 2, 6.6, 0, 2, 6.633, 0, 2, 6.667, 0, 2, 6.7, 0, 2, 6.733, 0, 2, 6.767, 0, 2, 6.8, 0, 2, 6.833, 0, 2, 6.9, 0, 2, 6.933, 0, 2, 6.967, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamGaoGuangR", "Segments": [0, 0, 2, 0.033, 0.003, 2, 0.067, 0.01, 2, 0.1, 0.019, 2, 0.133, 0.026, 2, 0.167, 0.029, 2, 0.2, 0.028, 2, 0.233, 0.028, 2, 0.267, 0.027, 2, 0.3, 0.025, 2, 0.333, 0.023, 2, 0.367, 0.021, 2, 0.4, 0.019, 2, 0.433, 0.016, 2, 0.467, 0.013, 2, 0.5, 0.011, 2, 0.533, 0.008, 2, 0.567, 0.004, 2, 0.6, 0.001, 2, 0.633, -0.002, 2, 0.667, -0.005, 2, 0.7, -0.008, 2, 0.733, -0.01, 2, 0.767, -0.013, 2, 0.8, -0.015, 2, 0.833, -0.018, 2, 0.867, -0.019, 2, 0.9, -0.021, 2, 0.933, -0.022, 2, 0.967, -0.023, 2, 1, -0.023, 2, 1.033, -0.02, 2, 1.067, -0.015, 2, 1.1, -0.007, 2, 1.133, 0.001, 2, 1.167, 0.007, 2, 1.2, 0.01, 2, 1.233, 0.009, 2, 1.267, 0.006, 2, 1.3, 0.003, 2, 1.333, -0.001, 2, 1.367, -0.003, 2, 1.4, -0.004, 2, 1.467, -0.004, 2, 1.5, -0.003, 2, 1.533, -0.001, 2, 1.567, 0, 2, 1.6, 0.001, 2, 1.633, 0.002, 2, 1.667, -0.011, 2, 1.7, -0.044, 2, 1.733, -0.093, 2, 1.767, -0.15, 2, 1.8, -0.209, 2, 1.833, -0.267, 2, 1.867, -0.315, 2, 1.9, -0.348, 2, 1.933, -0.361, 2, 1.967, -0.241, 2, 2, 0.045, 2, 2.033, 0.395, 2, 2.067, 0.681, 2, 2.1, 0.801, 2, 2.133, 0.652, 2, 2.167, 0.296, 2, 2.2, -0.137, 2, 2.233, -0.493, 2, 2.267, -0.641, 2, 2.3, -0.594, 2, 2.333, -0.473, 2, 2.367, -0.304, 2, 2.4, -0.126, 2, 2.433, 0.043, 2, 2.467, 0.165, 2, 2.5, 0.212, 2, 2.533, 0.189, 2, 2.567, 0.134, 2, 2.6, 0.062, 2, 2.633, -0.011, 2, 2.667, -0.066, 2, 2.7, -0.089, 2, 2.733, -0.079, 2, 2.767, -0.056, 2, 2.8, -0.025, 2, 2.833, 0.005, 2, 2.867, 0.028, 2, 2.9, 0.038, 2, 2.933, 0.006, 2, 2.967, -0.078, 2, 3, -0.198, 2, 3.033, -0.334, 2, 3.067, -0.471, 2, 3.1, -0.59, 2, 3.133, -0.675, 2, 3.167, -0.707, 2, 3.2, -0.567, 2, 3.233, -0.234, 2, 3.267, 0.173, 2, 3.3, 0.507, 2, 3.333, 0.646, 2, 3.367, 0.599, 2, 3.4, 0.477, 2, 3.433, 0.307, 2, 3.467, 0.127, 2, 3.5, -0.043, 2, 3.533, -0.165, 2, 3.567, -0.212, 2, 3.6, -0.19, 2, 3.633, -0.135, 2, 3.667, -0.062, 2, 3.7, 0.011, 2, 3.733, 0.066, 2, 3.767, 0.089, 2, 3.8, 0.079, 2, 3.833, 0.056, 2, 3.867, 0.025, 2, 3.9, -0.005, 2, 3.933, -0.028, 2, 3.967, -0.038, 2, 4, -0.034, 2, 4.033, -0.024, 2, 4.067, -0.011, 2, 4.1, 0.002, 2, 4.133, 0.012, 2, 4.167, 0.016, 2, 4.2, 0.015, 2, 4.233, 0.01, 2, 4.267, 0.005, 2, 4.3, -0.001, 2, 4.333, -0.005, 2, 4.367, -0.007, 2, 4.4, 0.053, 2, 4.433, 0.197, 2, 4.467, 0.389, 2, 4.5, 0.581, 2, 4.533, 0.725, 2, 4.567, 0.785, 2, 4.6, 0.612, 2, 4.633, 0.2, 2, 4.667, -0.302, 2, 4.7, -0.714, 2, 4.733, -0.887, 2, 4.767, -0.651, 2, 4.8, -0.133, 2, 4.833, 0.384, 2, 4.867, 0.62, 2, 4.9, 0.574, 2, 4.933, 0.456, 2, 4.967, 0.291, 2, 5, 0.117, 2, 5.033, -0.047, 2, 5.067, -0.165, 2, 5.1, -0.211, 2, 5.133, -0.189, 2, 5.167, -0.134, 2, 5.2, -0.062, 2, 5.233, 0.011, 2, 5.267, 0.066, 2, 5.3, 0.088, 2, 5.333, 0.079, 2, 5.367, 0.056, 2, 5.4, 0.025, 2, 5.433, -0.005, 2, 5.467, -0.028, 2, 5.5, -0.037, 2, 5.533, -0.033, 2, 5.567, -0.024, 2, 5.6, -0.011, 2, 5.633, 0.002, 2, 5.667, 0.012, 2, 5.7, 0.016, 2, 5.733, 0.014, 2, 5.767, 0.01, 2, 5.8, 0.005, 2, 5.833, -0.001, 2, 5.867, -0.005, 2, 5.9, -0.007, 2, 5.933, -0.006, 2, 5.967, -0.004, 2, 6, -0.002, 2, 6.033, 0, 2, 6.067, 0.002, 2, 6.1, 0.003, 2, 6.133, 0.003, 2, 6.167, 0.002, 2, 6.2, 0.001, 2, 6.233, 0, 2, 6.267, -0.001, 2, 6.3, -0.001, 2, 6.367, -0.001, 2, 6.4, -0.001, 2, 6.433, 0, 2, 6.467, 0, 2, 6.5, 0, 2, 6.567, 0, 2, 6.6, 0, 2, 6.633, 0, 2, 6.667, 0, 2, 6.7, 0, 2, 6.733, 0, 2, 6.767, 0, 2, 6.8, 0, 2, 6.833, 0, 2, 6.9, 0, 2, 6.933, 0, 2, 6.967, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamTeShuEyeChuXian", "Segments": [0, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamHeiHuaShadow", "Segments": [0, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamXianTiaoChuXian", "Segments": [0, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamTeShuZuiCX", "Segments": [0, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 2, 0.033, 0.003, 2, 0.067, 0.013, 2, 0.1, 0.028, 2, 0.133, 0.049, 2, 0.167, 0.074, 2, 0.2, 0.104, 2, 0.233, 0.138, 2, 0.267, 0.175, 2, 0.3, 0.216, 2, 0.333, 0.259, 2, 0.367, 0.305, 2, 0.4, 0.352, 2, 0.433, 0.401, 2, 0.467, 0.45, 2, 0.5, 0.5, 2, 0.533, 0.55, 2, 0.567, 0.599, 2, 0.6, 0.648, 2, 0.633, 0.695, 2, 0.667, 0.741, 2, 0.7, 0.784, 2, 0.733, 0.825, 2, 0.767, 0.862, 2, 0.8, 0.896, 2, 0.833, 0.926, 2, 0.867, 0.951, 2, 0.9, 0.972, 2, 0.933, 0.987, 2, 0.967, 0.997, 2, 1, 1, 2, 2.033, 0.925, 2, 2.067, 0.742, 2, 2.1, 0.5, 2, 2.133, 0.258, 2, 2.167, 0.075, 2, 2.2, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 2, 0.033, 0.003, 2, 0.067, 0.013, 2, 0.1, 0.028, 2, 0.133, 0.049, 2, 0.167, 0.074, 2, 0.2, 0.104, 2, 0.233, 0.138, 2, 0.267, 0.175, 2, 0.3, 0.216, 2, 0.333, 0.259, 2, 0.367, 0.305, 2, 0.4, 0.352, 2, 0.433, 0.401, 2, 0.467, 0.45, 2, 0.5, 0.5, 2, 0.533, 0.55, 2, 0.567, 0.599, 2, 0.6, 0.648, 2, 0.633, 0.695, 2, 0.667, 0.741, 2, 0.7, 0.784, 2, 0.733, 0.825, 2, 0.767, 0.862, 2, 0.8, 0.896, 2, 0.833, 0.926, 2, 0.867, 0.951, 2, 0.9, 0.972, 2, 0.933, 0.987, 2, 0.967, 0.997, 2, 1, 1, 2, 2.033, 0.925, 2, 2.067, 0.742, 2, 2.1, 0.5, 2, 2.133, 0.258, 2, 2.167, 0.075, 2, 2.2, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 2, 0.033, 0.003, 2, 0.067, 0.013, 2, 0.1, 0.028, 2, 0.133, 0.049, 2, 0.167, 0.074, 2, 0.2, 0.104, 2, 0.233, 0.138, 2, 0.267, 0.175, 2, 0.3, 0.216, 2, 0.333, 0.259, 2, 0.367, 0.305, 2, 0.4, 0.352, 2, 0.433, 0.401, 2, 0.467, 0.45, 2, 0.5, 0.5, 2, 0.533, 0.55, 2, 0.567, 0.599, 2, 0.6, 0.648, 2, 0.633, 0.695, 2, 0.667, 0.741, 2, 0.7, 0.784, 2, 0.733, 0.825, 2, 0.767, 0.862, 2, 0.8, 0.896, 2, 0.833, 0.926, 2, 0.867, 0.951, 2, 0.9, 0.972, 2, 0.933, 0.987, 2, 0.967, 0.997, 2, 1, 1, 2, 7, 1]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 2, 0.033, 0.003, 2, 0.067, 0.013, 2, 0.1, 0.028, 2, 0.133, 0.049, 2, 0.167, 0.074, 2, 0.2, 0.104, 2, 0.233, 0.138, 2, 0.267, 0.175, 2, 0.3, 0.216, 2, 0.333, 0.259, 2, 0.367, 0.305, 2, 0.4, 0.352, 2, 0.433, 0.401, 2, 0.467, 0.45, 2, 0.5, 0.5, 2, 0.533, 0.55, 2, 0.567, 0.599, 2, 0.6, 0.648, 2, 0.633, 0.695, 2, 0.667, 0.741, 2, 0.7, 0.784, 2, 0.733, 0.825, 2, 0.767, 0.862, 2, 0.8, 0.896, 2, 0.833, 0.926, 2, 0.867, 0.951, 2, 0.9, 0.972, 2, 0.933, 0.987, 2, 0.967, 0.997, 2, 1, 1, 2, 7, 1]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 2, 0.033, 0.003, 2, 0.067, 0.013, 2, 0.1, 0.028, 2, 0.133, 0.049, 2, 0.167, 0.074, 2, 0.2, 0.104, 2, 0.233, 0.138, 2, 0.267, 0.175, 2, 0.3, 0.216, 2, 0.333, 0.259, 2, 0.367, 0.305, 2, 0.4, 0.352, 2, 0.433, 0.401, 2, 0.467, 0.45, 2, 0.5, 0.5, 2, 0.533, 0.55, 2, 0.567, 0.599, 2, 0.6, 0.648, 2, 0.633, 0.695, 2, 0.667, 0.741, 2, 0.7, 0.784, 2, 0.733, 0.825, 2, 0.767, 0.862, 2, 0.8, 0.896, 2, 0.833, 0.926, 2, 0.867, 0.951, 2, 0.9, 0.972, 2, 0.933, 0.987, 2, 0.967, 0.997, 2, 1, 1, 2, 3.1, 0.999, 2, 3.133, 0.997, 2, 3.167, 0.994, 2, 3.2, 0.989, 2, 3.233, 0.983, 2, 3.267, 0.976, 2, 3.3, 0.969, 2, 3.333, 0.96, 2, 3.367, 0.951, 2, 3.4, 0.942, 2, 3.433, 0.932, 2, 3.467, 0.921, 2, 3.5, 0.911, 2, 3.533, 0.9, 2, 3.567, 0.889, 2, 3.6, 0.879, 2, 3.633, 0.868, 2, 3.667, 0.858, 2, 3.7, 0.849, 2, 3.733, 0.84, 2, 3.767, 0.831, 2, 3.8, 0.824, 2, 3.833, 0.817, 2, 3.867, 0.811, 2, 3.9, 0.806, 2, 3.933, 0.803, 2, 3.967, 0.801, 2, 4, 0.8, 2, 7, 0.8]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 2, 0.033, 0.003, 2, 0.067, 0.013, 2, 0.1, 0.028, 2, 0.133, 0.049, 2, 0.167, 0.074, 2, 0.2, 0.104, 2, 0.233, 0.138, 2, 0.267, 0.175, 2, 0.3, 0.216, 2, 0.333, 0.259, 2, 0.367, 0.305, 2, 0.4, 0.352, 2, 0.433, 0.401, 2, 0.467, 0.45, 2, 0.5, 0.5, 2, 0.533, 0.55, 2, 0.567, 0.599, 2, 0.6, 0.648, 2, 0.633, 0.695, 2, 0.667, 0.741, 2, 0.7, 0.784, 2, 0.733, 0.825, 2, 0.767, 0.862, 2, 0.8, 0.896, 2, 0.833, 0.926, 2, 0.867, 0.951, 2, 0.9, 0.972, 2, 0.933, 0.987, 2, 0.967, 0.997, 2, 1, 1, 2, 3.1, 0.999, 2, 3.133, 0.997, 2, 3.167, 0.994, 2, 3.2, 0.989, 2, 3.233, 0.983, 2, 3.267, 0.976, 2, 3.3, 0.969, 2, 3.333, 0.96, 2, 3.367, 0.951, 2, 3.4, 0.942, 2, 3.433, 0.932, 2, 3.467, 0.921, 2, 3.5, 0.911, 2, 3.533, 0.9, 2, 3.567, 0.889, 2, 3.6, 0.879, 2, 3.633, 0.868, 2, 3.667, 0.858, 2, 3.7, 0.849, 2, 3.733, 0.84, 2, 3.767, 0.831, 2, 3.8, 0.824, 2, 3.833, 0.817, 2, 3.867, 0.811, 2, 3.9, 0.806, 2, 3.933, 0.803, 2, 3.967, 0.801, 2, 4, 0.8, 2, 7, 0.8]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 2, 0.033, 0.003, 2, 0.067, 0.013, 2, 0.1, 0.028, 2, 0.133, 0.049, 2, 0.167, 0.074, 2, 0.2, 0.104, 2, 0.233, 0.138, 2, 0.267, 0.175, 2, 0.3, 0.216, 2, 0.333, 0.259, 2, 0.367, 0.305, 2, 0.4, 0.352, 2, 0.433, 0.401, 2, 0.467, 0.45, 2, 0.5, 0.5, 2, 0.533, 0.55, 2, 0.567, 0.599, 2, 0.6, 0.648, 2, 0.633, 0.695, 2, 0.667, 0.741, 2, 0.7, 0.784, 2, 0.733, 0.825, 2, 0.767, 0.862, 2, 0.8, 0.896, 2, 0.833, 0.926, 2, 0.867, 0.951, 2, 0.9, 0.972, 2, 0.933, 0.987, 2, 0.967, 0.997, 2, 1, 1, 2, 7, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 2, 0.433, 0.156, 2, 0.467, 0.5, 2, 0.5, 0.844, 2, 0.533, 1, 2, 0.733, 0.44, 2, 0.767, 0.615, 2, 0.8, 1, 2, 0.9, 0.921, 2, 0.933, 0.282, 2, 0.967, 0.545, 2, 1, 1, 2, 1.133, 0.432, 2, 1.167, 0.641, 2, 1.2, 1, 2, 1.333, 0.352, 2, 1.367, 0.627, 2, 1.4, 0.902, 2, 1.433, 0.714, 2, 1.467, 0.526, 2, 1.5, 0.674, 2, 1.533, 1, 2, 1.8, 0.306, 2, 1.833, 0.431, 2, 1.867, 0.556, 2, 1.9, 0.494, 2, 1.933, 0.356, 2, 1.967, 0.218, 2, 2, 0.156, 2, 2.033, 0.196, 2, 2.067, 0.236, 2, 2.1, 0.122, 2, 2.133, 0.008, 2, 2.167, 0.329, 2, 2.2, 0.65, 2, 2.233, 0.533, 2, 2.267, 0.416, 2, 2.3, 0.533, 2, 2.333, 0.782, 2, 2.367, 1, 2, 2.433, 0.994, 2, 2.467, 0.644, 2, 2.5, 0.302, 2, 2.533, 0.142, 2, 2.567, 0.337, 2, 2.6, 0.754, 2, 2.633, 1, 2, 2.733, 0.984, 2, 2.767, 0.717, 2, 2.8, 0.596, 2, 2.833, 1, 2, 2.967, 0.828, 2, 3, 0.442, 2, 3.033, 0.151, 2, 3.067, 0.032, 2, 3.1, 0.11, 2, 3.133, 0.188, 2, 3.167, 0.171, 2, 3.2, 0.133, 2, 3.233, 0.095, 2, 3.267, 0.078, 2, 3.3, 0.171, 2, 3.333, 0.405, 2, 3.367, 0.707, 2, 3.4, 1, 2, 3.5, 0.887, 2, 3.533, 0.44, 2, 3.567, 1, 2, 3.733, 0.848, 2, 3.767, 1, 2, 3.867, 0.376, 2, 3.9, 1, 2, 4, 0.376, 2, 4.033, 0.614, 2, 4.067, 1, 2, 4.233, 0.389, 2, 4.267, 0.11, 2, 4.3, 0.318, 2, 4.333, 0.526, 2, 4.367, 0.488, 2, 4.4, 0.392, 2, 4.433, 0.265, 2, 4.467, 0.136, 2, 4.5, 0.04, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 2, 0.033, 0.003, 2, 0.067, 0.013, 2, 0.1, 0.028, 2, 0.133, 0.049, 2, 0.167, 0.074, 2, 0.2, 0.104, 2, 0.233, 0.138, 2, 0.267, 0.175, 2, 0.3, 0.216, 2, 0.333, 0.259, 2, 0.367, 0.305, 2, 0.4, 0.352, 2, 0.433, 0.401, 2, 0.467, 0.45, 2, 0.5, 0.5, 2, 0.533, 0.55, 2, 0.567, 0.599, 2, 0.6, 0.648, 2, 0.633, 0.695, 2, 0.667, 0.741, 2, 0.7, 0.784, 2, 0.733, 0.825, 2, 0.767, 0.862, 2, 0.8, 0.896, 2, 0.833, 0.926, 2, 0.867, 0.951, 2, 0.9, 0.972, 2, 0.933, 0.987, 2, 0.967, 0.997, 2, 1, 1, 2, 1.033, 0.999, 2, 1.067, 0.995, 2, 1.1, 0.99, 2, 1.133, 0.982, 2, 1.167, 0.972, 2, 1.2, 0.96, 2, 1.233, 0.947, 2, 1.267, 0.931, 2, 1.3, 0.914, 2, 1.333, 0.896, 2, 1.367, 0.876, 2, 1.4, 0.855, 2, 1.433, 0.832, 2, 1.467, 0.809, 2, 1.5, 0.784, 2, 1.533, 0.759, 2, 1.567, 0.732, 2, 1.6, 0.705, 2, 1.633, 0.677, 2, 1.667, 0.648, 2, 1.7, 0.619, 2, 1.733, 0.59, 2, 1.767, 0.56, 2, 1.8, 0.53, 2, 1.833, 0.5, 2, 1.867, 0.47, 2, 1.9, 0.44, 2, 1.933, 0.41, 2, 1.967, 0.381, 2, 2, 0.352, 2, 2.033, 0.323, 2, 2.067, 0.295, 2, 2.1, 0.268, 2, 2.133, 0.241, 2, 2.167, 0.216, 2, 2.2, 0.191, 2, 2.233, 0.168, 2, 2.267, 0.145, 2, 2.3, 0.124, 2, 2.333, 0.104, 2, 2.367, 0.086, 2, 2.4, 0.069, 2, 2.433, 0.053, 2, 2.467, 0.04, 2, 2.5, 0.028, 2, 2.533, 0.018, 2, 2.567, 0.01, 2, 2.6, 0.005, 2, 2.633, 0.001, 2, 2.667, 0, 2, 2.7, 0.002, 2, 2.733, 0.007, 2, 2.767, 0.016, 2, 2.8, 0.028, 2, 2.833, 0.043, 2, 2.867, 0.061, 2, 2.9, 0.081, 2, 2.933, 0.104, 2, 2.967, 0.129, 2, 3, 0.156, 2, 3.033, 0.185, 2, 3.067, 0.216, 2, 3.1, 0.248, 2, 3.133, 0.282, 2, 3.167, 0.316, 2, 3.2, 0.352, 2, 3.233, 0.388, 2, 3.267, 0.426, 2, 3.3, 0.463, 2, 3.333, 0.5, 2, 3.367, 0.537, 2, 3.4, 0.574, 2, 3.433, 0.612, 2, 3.467, 0.648, 2, 3.5, 0.684, 2, 3.533, 0.718, 2, 3.567, 0.752, 2, 3.6, 0.784, 2, 3.633, 0.815, 2, 3.667, 0.844, 2, 3.7, 0.871, 2, 3.733, 0.896, 2, 3.767, 0.919, 2, 3.8, 0.939, 2, 3.833, 0.957, 2, 3.867, 0.972, 2, 3.9, 0.984, 2, 3.933, 0.993, 2, 3.967, 0.998, 2, 4, 1, 2, 4.033, 0.999, 2, 4.067, 0.997, 2, 4.1, 0.993, 2, 4.133, 0.987, 2, 4.167, 0.98, 2, 4.2, 0.972, 2, 4.233, 0.962, 2, 4.267, 0.951, 2, 4.3, 0.939, 2, 4.333, 0.926, 2, 4.367, 0.911, 2, 4.4, 0.896, 2, 4.433, 0.879, 2, 4.467, 0.862, 2, 4.5, 0.844, 2, 4.533, 0.825, 2, 4.567, 0.805, 2, 4.6, 0.784, 2, 4.633, 0.763, 2, 4.667, 0.741, 2, 4.7, 0.718, 2, 4.733, 0.695, 2, 4.767, 0.672, 2, 4.8, 0.648, 2, 4.833, 0.624, 2, 4.867, 0.599, 2, 4.9, 0.575, 2, 4.933, 0.55, 2, 4.967, 0.525, 2, 5, 0.5, 2, 5.033, 0.475, 2, 5.067, 0.45, 2, 5.1, 0.425, 2, 5.133, 0.401, 2, 5.167, 0.376, 2, 5.2, 0.352, 2, 5.233, 0.328, 2, 5.267, 0.305, 2, 5.3, 0.282, 2, 5.333, 0.259, 2, 5.367, 0.237, 2, 5.4, 0.216, 2, 5.433, 0.195, 2, 5.467, 0.175, 2, 5.5, 0.156, 2, 5.533, 0.138, 2, 5.567, 0.121, 2, 5.6, 0.104, 2, 5.633, 0.089, 2, 5.667, 0.074, 2, 5.7, 0.061, 2, 5.733, 0.049, 2, 5.767, 0.038, 2, 5.8, 0.028, 2, 5.833, 0.02, 2, 5.867, 0.013, 2, 5.9, 0.007, 2, 5.933, 0.003, 2, 5.967, 0.001, 2, 6, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 2, 3.1, 0.086, 2, 3.133, 0.312, 2, 3.167, 0.633, 2, 3.2, 1, 2, 3.233, 1.367, 2, 3.267, 1.688, 2, 3.3, 1.914, 2, 3.333, 2, 2, 3.367, 1.914, 2, 3.4, 1.667, 2, 3.433, 1.267, 2, 3.467, 0.75, 2, 3.5, 0.125, 2, 3.533, -0.589, 2, 3.567, -1.375, 2, 3.6, -2.231, 2, 3.633, -3.106, 2, 3.667, -4, 2, 3.7, -4.894, 2, 3.733, -5.769, 2, 3.767, -6.625, 2, 3.8, -7.411, 2, 3.833, -8.125, 2, 3.867, -8.75, 2, 3.9, -9.267, 2, 3.933, -9.667, 2, 3.967, -9.914, 2, 4, -10, 2, 4.033, -9.971, 2, 4.067, -9.89, 2, 4.1, -9.763, 2, 4.133, -9.605, 2, 4.167, -9.416, 2, 4.2, -9.212, 2, 4.233, -9, 2, 4.267, -8.788, 2, 4.3, -8.584, 2, 4.333, -8.395, 2, 4.367, -8.237, 2, 4.4, -8.11, 2, 4.433, -8.029, 2, 4.467, -8, 2, 7, -8]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 2, 3.1, -0.086, 2, 3.133, -0.312, 2, 3.167, -0.633, 2, 3.2, -1, 2, 3.233, -1.367, 2, 3.267, -1.688, 2, 3.3, -1.914, 2, 3.333, -2, 2, 3.367, -1.914, 2, 3.4, -1.667, 2, 3.433, -1.267, 2, 3.467, -0.75, 2, 3.5, -0.125, 2, 3.533, 0.589, 2, 3.567, 1.375, 2, 3.6, 2.231, 2, 3.633, 3.106, 2, 3.667, 4, 2, 3.7, 4.894, 2, 3.733, 5.769, 2, 3.767, 6.625, 2, 3.8, 7.411, 2, 3.833, 8.125, 2, 3.867, 8.75, 2, 3.9, 9.267, 2, 3.933, 9.667, 2, 3.967, 9.914, 2, 4, 10, 2, 4.033, 9.971, 2, 4.067, 9.89, 2, 4.1, 9.763, 2, 4.133, 9.605, 2, 4.167, 9.416, 2, 4.2, 9.212, 2, 4.233, 9, 2, 4.267, 8.788, 2, 4.3, 8.584, 2, 4.333, 8.395, 2, 4.367, 8.237, 2, 4.4, 8.11, 2, 4.433, 8.029, 2, 4.467, 8, 2, 7, 8]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamShenTiQianHou", "Segments": [0, 0, 2, 0.033, -0.207, 2, 0.067, -0.76, 2, 0.1, -1.561, 2, 0.133, -2.51, 2, 0.167, -3.49, 2, 0.2, -4.439, 2, 0.233, -5.24, 2, 0.267, -5.793, 2, 0.3, -6, 2, 0.333, -5.761, 2, 0.367, -5.074, 2, 0.4, -4.013, 2, 0.433, -2.581, 2, 0.467, -0.842, 2, 0.5, 1.155, 2, 0.533, 3.318, 2, 0.567, 5.688, 2, 0.6, 8.176, 2, 0.633, 10.735, 2, 0.667, 13.265, 2, 0.7, 15.824, 2, 0.733, 18.312, 2, 0.767, 20.682, 2, 0.8, 22.845, 2, 0.833, 24.842, 2, 0.867, 26.581, 2, 0.9, 28.013, 2, 0.933, 29.074, 2, 0.967, 29.761, 2, 1, 30, 2, 1.033, 29.917, 2, 1.067, 29.69, 2, 1.1, 29.349, 2, 1.133, 28.942, 2, 1.167, 28.5, 2, 1.2, 28.058, 2, 1.233, 27.651, 2, 1.267, 27.31, 2, 1.3, 27.083, 2, 1.333, 27, 2, 3.1, 27.129, 2, 3.133, 27.469, 2, 3.167, 27.949, 2, 3.2, 28.5, 2, 3.233, 29.051, 2, 3.267, 29.531, 2, 3.3, 29.871, 2, 3.333, 30, 2, 3.367, 29.784, 2, 3.4, 29.166, 2, 3.433, 28.169, 2, 3.467, 26.874, 2, 3.5, 25.312, 2, 3.533, 23.527, 2, 3.567, 21.563, 2, 3.6, 19.423, 2, 3.633, 17.234, 2, 3.667, 15, 2, 3.7, 12.766, 2, 3.733, 10.577, 2, 3.767, 8.437, 2, 3.8, 6.473, 2, 3.833, 4.688, 2, 3.867, 3.126, 2, 3.9, 1.831, 2, 3.933, 0.834, 2, 3.967, 0.216, 2, 4, 0, 2, 4.033, 0.044, 2, 4.067, 0.166, 2, 4.1, 0.356, 2, 4.133, 0.593, 2, 4.167, 0.876, 2, 4.2, 1.181, 2, 4.233, 1.5, 2, 4.267, 1.819, 2, 4.3, 2.124, 2, 4.333, 2.407, 2, 4.367, 2.644, 2, 4.4, 2.834, 2, 4.433, 2.956, 2, 4.467, 3, 2, 7, 3]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.033, 0.977, 2, 0.067, 3.348, 2, 0.1, 6.5, 2, 0.133, 9.652, 2, 0.167, 12.023, 2, 0.2, 13, 2, 0.233, 12.894, 2, 0.267, 12.594, 2, 0.3, 12.094, 2, 0.333, 11.41, 2, 0.367, 10.574, 2, 0.4, 9.555, 2, 0.433, 8.383, 2, 0.467, 7.098, 2, 0.5, 5.651, 2, 0.533, 4.079, 2, 0.567, 2.435, 2, 0.6, 0.648, 2, 0.633, -1.233, 2, 0.667, -3.15, 2, 0.7, -5.186, 2, 0.733, -7.286, 2, 0.767, -9.388, 2, 0.8, -11.584, 2, 0.833, -13.813, 2, 0.867, -16.012, 2, 0.9, -18.277, 2, 0.933, -20.546, 2, 0.967, -22.755, 2, 1, -25, 2, 1.033, -26.65, 2, 1.067, -27.891, 2, 1.1, -28.779, 2, 1.133, -29.375, 2, 1.167, -29.736, 2, 1.2, -29.922, 2, 1.233, -29.99, 2, 1.267, -30, 2, 1.3, -29.9, 2, 1.333, -29.632, 2, 1.367, -29.219, 2, 1.4, -28.699, 2, 1.433, -28.124, 2, 1.467, -27.5, 2, 1.5, -26.876, 2, 1.533, -26.301, 2, 1.567, -25.781, 2, 1.6, -25.368, 2, 1.633, -25.1, 2, 1.667, -25, 2, 1.7, -25.056, 2, 1.733, -25.215, 2, 1.767, -25.461, 2, 1.8, -25.781, 2, 1.833, -26.16, 2, 1.867, -26.582, 2, 1.9, -27.034, 2, 1.933, -27.5, 2, 1.967, -27.966, 2, 2, -28.418, 2, 2.033, -28.84, 2, 2.067, -29.219, 2, 2.1, -29.539, 2, 2.133, -29.785, 2, 2.167, -29.944, 2, 2.2, -30, 2, 2.233, -30, 2, 2.267, -29.999, 2, 2.3, -29.997, 2, 2.333, -29.994, 2, 2.367, -29.988, 2, 2.4, -29.98, 2, 2.433, -29.968, 2, 2.467, -29.952, 2, 2.5, -29.932, 2, 2.533, -29.907, 2, 2.567, -29.876, 2, 2.6, -29.84, 2, 2.633, -29.797, 2, 2.667, -29.746, 2, 2.7, -29.688, 2, 2.733, -29.622, 2, 2.767, -29.547, 2, 2.8, -29.463, 2, 2.833, -29.369, 2, 2.867, -29.264, 2, 2.9, -29.149, 2, 2.933, -29.022, 2, 2.967, -28.883, 2, 3, -28.731, 2, 3.033, -28.566, 2, 3.067, -28.388, 2, 3.1, -28.195, 2, 3.133, -27.988, 2, 3.167, -27.765, 2, 3.2, -27.527, 2, 3.233, -27.272, 2, 3.267, -27, 2, 3.3, -26.632, 2, 3.333, -26.115, 2, 3.367, -25.438, 2, 3.4, -24.642, 2, 3.433, -23.703, 2, 3.467, -22.672, 2, 3.5, -21.517, 2, 3.533, -20.298, 2, 3.567, -18.973, 2, 3.6, -17.61, 2, 3.633, -16.162, 2, 3.667, -14.668, 2, 3.7, -13.174, 2, 3.733, -11.627, 2, 3.767, -10.103, 2, 3.8, -8.548, 2, 3.833, -7.039, 2, 3.867, -5.523, 2, 3.9, -4.075, 2, 3.933, -2.643, 2, 3.967, -1.3, 2, 4, 0, 2, 4.033, 1.172, 2, 4.067, 2.14, 2, 4.1, 2.936, 2, 4.133, 3.521, 2, 4.167, 3.875, 2, 4.2, 4, 2, 4.233, 3.862, 2, 4.267, 3.493, 2, 4.3, 2.959, 2, 4.333, 2.327, 2, 4.367, 1.673, 2, 4.4, 1.041, 2, 4.433, 0.507, 2, 4.467, 0.138, 2, 4.5, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.033, 0.977, 2, 0.067, 3.348, 2, 0.1, 6.5, 2, 0.133, 9.652, 2, 0.167, 12.023, 2, 0.2, 13, 2, 0.233, 12.894, 2, 0.267, 12.594, 2, 0.3, 12.094, 2, 0.333, 11.41, 2, 0.367, 10.574, 2, 0.4, 9.555, 2, 0.433, 8.383, 2, 0.467, 7.098, 2, 0.5, 5.651, 2, 0.533, 4.079, 2, 0.567, 2.435, 2, 0.6, 0.648, 2, 0.633, -1.233, 2, 0.667, -3.15, 2, 0.7, -5.186, 2, 0.733, -7.286, 2, 0.767, -9.388, 2, 0.8, -11.584, 2, 0.833, -13.813, 2, 0.867, -16.012, 2, 0.9, -18.277, 2, 0.933, -20.546, 2, 0.967, -22.755, 2, 1, -25, 2, 1.033, -26.65, 2, 1.067, -27.891, 2, 1.1, -28.779, 2, 1.133, -29.375, 2, 1.167, -29.736, 2, 1.2, -29.922, 2, 1.233, -29.99, 2, 1.267, -30, 2, 1.3, -29.9, 2, 1.333, -29.632, 2, 1.367, -29.219, 2, 1.4, -28.699, 2, 1.433, -28.124, 2, 1.467, -27.5, 2, 1.5, -26.876, 2, 1.533, -26.301, 2, 1.567, -25.781, 2, 1.6, -25.368, 2, 1.633, -25.1, 2, 1.667, -25, 2, 1.7, -25.056, 2, 1.733, -25.215, 2, 1.767, -25.461, 2, 1.8, -25.781, 2, 1.833, -26.16, 2, 1.867, -26.582, 2, 1.9, -27.034, 2, 1.933, -27.5, 2, 1.967, -27.966, 2, 2, -28.418, 2, 2.033, -28.84, 2, 2.067, -29.219, 2, 2.1, -29.539, 2, 2.133, -29.785, 2, 2.167, -29.944, 2, 2.2, -30, 2, 2.233, -30, 2, 2.267, -29.999, 2, 2.3, -29.997, 2, 2.333, -29.994, 2, 2.367, -29.988, 2, 2.4, -29.98, 2, 2.433, -29.968, 2, 2.467, -29.952, 2, 2.5, -29.932, 2, 2.533, -29.907, 2, 2.567, -29.876, 2, 2.6, -29.84, 2, 2.633, -29.797, 2, 2.667, -29.746, 2, 2.7, -29.688, 2, 2.733, -29.622, 2, 2.767, -29.547, 2, 2.8, -29.463, 2, 2.833, -29.369, 2, 2.867, -29.264, 2, 2.9, -29.149, 2, 2.933, -29.022, 2, 2.967, -28.883, 2, 3, -28.731, 2, 3.033, -28.566, 2, 3.067, -28.388, 2, 3.1, -28.195, 2, 3.133, -27.988, 2, 3.167, -27.765, 2, 3.2, -27.527, 2, 3.233, -27.272, 2, 3.267, -27, 2, 3.3, -26.632, 2, 3.333, -26.115, 2, 3.367, -25.438, 2, 3.4, -24.642, 2, 3.433, -23.703, 2, 3.467, -22.672, 2, 3.5, -21.517, 2, 3.533, -20.298, 2, 3.567, -18.973, 2, 3.6, -17.61, 2, 3.633, -16.162, 2, 3.667, -14.668, 2, 3.7, -13.174, 2, 3.733, -11.627, 2, 3.767, -10.103, 2, 3.8, -8.548, 2, 3.833, -7.039, 2, 3.867, -5.523, 2, 3.9, -4.075, 2, 3.933, -2.643, 2, 3.967, -1.3, 2, 4, 0, 2, 4.033, 1.172, 2, 4.067, 2.14, 2, 4.1, 2.936, 2, 4.133, 3.521, 2, 4.167, 3.875, 2, 4.2, 4, 2, 4.233, 3.862, 2, 4.267, 3.493, 2, 4.3, 2.959, 2, 4.333, 2.327, 2, 4.367, 1.673, 2, 4.4, 1.041, 2, 4.433, 0.507, 2, 4.467, 0.138, 2, 4.5, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "Paramzuodatui", "Segments": [0, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 2, 0.033, 0.007, 2, 0.067, 0.023, 2, 0.1, 0.044, 2, 0.133, 0.065, 2, 0.167, 0.081, 2, 0.2, 0.088, 2, 0.233, 0.079, 2, 0.267, 0.054, 2, 0.3, 0.017, 2, 0.333, -0.027, 2, 0.367, -0.075, 2, 0.4, -0.123, 2, 0.433, -0.167, 2, 0.467, -0.205, 2, 0.5, -0.229, 2, 0.533, -0.238, 2, 0.567, -0.233, 2, 0.6, -0.219, 2, 0.633, -0.197, 2, 0.667, -0.169, 2, 0.7, -0.136, 2, 0.733, -0.1, 2, 0.767, -0.062, 2, 0.8, -0.023, 2, 0.833, 0.016, 2, 0.867, 0.052, 2, 0.9, 0.085, 2, 0.933, 0.113, 2, 0.967, 0.135, 2, 1, 0.149, 2, 1.033, 0.154, 2, 1.067, 0.148, 2, 1.1, 0.133, 2, 1.133, 0.109, 2, 1.167, 0.081, 2, 1.2, 0.048, 2, 1.233, 0.015, 2, 1.267, -0.017, 2, 1.3, -0.046, 2, 1.333, -0.069, 2, 1.367, -0.085, 2, 1.4, -0.091, 2, 1.433, -0.087, 2, 1.467, -0.077, 2, 1.5, -0.062, 2, 1.533, -0.044, 2, 1.567, -0.025, 2, 1.6, -0.005, 2, 1.633, 0.013, 2, 1.667, 0.028, 2, 1.7, 0.038, 2, 1.733, 0.042, 2, 1.767, 0.039, 2, 1.8, 0.033, 2, 1.833, 0.023, 2, 1.867, 0.012, 2, 1.9, 0.001, 2, 1.933, -0.008, 2, 1.967, -0.015, 2, 2, -0.017, 2, 2.033, 0.015, 2, 2.067, 0.091, 2, 2.1, 0.183, 2, 2.133, 0.259, 2, 2.167, 0.291, 2, 2.2, 0.254, 2, 2.233, 0.155, 2, 2.267, 0.016, 2, 2.3, -0.143, 2, 2.333, -0.302, 2, 2.367, -0.441, 2, 2.4, -0.539, 2, 2.433, -0.577, 2, 2.467, -0.543, 2, 2.5, -0.452, 2, 2.533, -0.315, 2, 2.567, -0.151, 2, 2.6, 0.026, 2, 2.633, 0.204, 2, 2.667, 0.368, 2, 2.7, 0.505, 2, 2.733, 0.596, 2, 2.767, 0.629, 2, 2.8, 0.597, 2, 2.833, 0.511, 2, 2.867, 0.379, 2, 2.9, 0.219, 2, 2.933, 0.038, 2, 2.967, -0.146, 2, 3, -0.328, 2, 3.033, -0.487, 2, 3.067, -0.619, 2, 3.1, -0.706, 2, 3.133, -0.738, 2, 3.167, -0.702, 2, 3.2, -0.606, 2, 3.233, -0.46, 2, 3.267, -0.286, 2, 3.3, -0.097, 2, 3.333, 0.092, 2, 3.367, 0.266, 2, 3.4, 0.411, 2, 3.433, 0.508, 2, 3.467, 0.544, 2, 3.5, 0.522, 2, 3.533, 0.464, 2, 3.567, 0.376, 2, 3.6, 0.272, 2, 3.633, 0.158, 2, 3.667, 0.045, 2, 3.7, -0.06, 2, 3.733, -0.147, 2, 3.767, -0.205, 2, 3.8, -0.227, 2, 3.833, -0.205, 2, 3.867, -0.149, 2, 3.9, -0.071, 2, 3.933, 0.012, 2, 3.967, 0.09, 2, 4, 0.146, 2, 4.033, 0.168, 2, 4.067, 0.153, 2, 4.1, 0.112, 2, 4.133, 0.053, 2, 4.167, -0.017, 2, 4.2, -0.09, 2, 4.233, -0.16, 2, 4.267, -0.219, 2, 4.3, -0.26, 2, 4.333, -0.275, 2, 4.367, -0.261, 2, 4.4, -0.222, 2, 4.433, -0.165, 2, 4.467, -0.098, 2, 4.5, -0.029, 2, 4.533, 0.039, 2, 4.567, 0.095, 2, 4.6, 0.134, 2, 4.633, 0.149, 2, 4.667, 0.143, 2, 4.7, 0.126, 2, 4.733, 0.102, 2, 4.767, 0.072, 2, 4.8, 0.04, 2, 4.833, 0.008, 2, 4.867, -0.021, 2, 4.9, -0.046, 2, 4.933, -0.062, 2, 4.967, -0.069, 2, 5, -0.065, 2, 5.033, -0.056, 2, 5.067, -0.043, 2, 5.1, -0.027, 2, 5.133, -0.01, 2, 5.167, 0.005, 2, 5.2, 0.019, 2, 5.233, 0.028, 2, 5.267, 0.031, 2, 5.333, 0.03, 2, 5.367, 0.025, 2, 5.4, 0.019, 2, 5.433, 0.012, 2, 5.467, 0.005, 2, 5.5, -0.003, 2, 5.533, -0.009, 2, 5.567, -0.013, 2, 5.6, -0.015, 2, 5.633, -0.014, 2, 5.667, -0.012, 2, 5.7, -0.01, 2, 5.733, -0.007, 2, 5.767, -0.004, 2, 5.8, -0.001, 2, 5.833, 0.002, 2, 5.867, 0.005, 2, 5.9, 0.006, 2, 5.933, 0.007, 2, 5.967, 0.006, 2, 6, 0.006, 2, 6.033, 0.004, 2, 6.067, 0.003, 2, 6.1, 0.001, 2, 6.133, -0.001, 2, 6.167, -0.002, 2, 6.2, -0.003, 2, 6.233, -0.003, 2, 6.3, -0.003, 2, 6.333, -0.003, 2, 6.367, -0.002, 2, 6.4, -0.001, 2, 6.433, 0, 2, 6.467, 0, 2, 6.5, 0.001, 2, 6.533, 0.001, 2, 6.567, 0.002, 2, 6.6, 0.001, 2, 6.633, 0.001, 2, 6.667, 0.001, 2, 6.7, 0.001, 2, 6.733, 0, 2, 6.767, 0, 2, 6.8, 0, 2, 6.833, 0, 2, 6.867, -0.001, 2, 6.9, -0.001, 2, 6.933, -0.001, 2, 6.967, -0.001, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 2, 0.033, 0.007, 2, 0.067, 0.025, 2, 0.1, 0.048, 2, 0.133, 0.072, 2, 0.167, 0.09, 2, 0.2, 0.097, 2, 0.233, 0.085, 2, 0.267, 0.052, 2, 0.3, 0.005, 2, 0.333, -0.051, 2, 0.367, -0.108, 2, 0.4, -0.164, 2, 0.433, -0.211, 2, 0.467, -0.244, 2, 0.5, -0.256, 2, 0.533, -0.252, 2, 0.567, -0.239, 2, 0.6, -0.219, 2, 0.633, -0.194, 2, 0.667, -0.164, 2, 0.7, -0.131, 2, 0.733, -0.095, 2, 0.767, -0.058, 2, 0.8, -0.021, 2, 0.833, 0.015, 2, 0.867, 0.049, 2, 0.9, 0.079, 2, 0.933, 0.104, 2, 0.967, 0.124, 2, 1, 0.136, 2, 1.033, 0.141, 2, 1.067, 0.135, 2, 1.1, 0.121, 2, 1.133, 0.099, 2, 1.167, 0.073, 2, 1.2, 0.043, 2, 1.233, 0.013, 2, 1.267, -0.017, 2, 1.3, -0.044, 2, 1.333, -0.065, 2, 1.367, -0.08, 2, 1.4, -0.085, 2, 1.433, -0.081, 2, 1.467, -0.069, 2, 1.5, -0.053, 2, 1.533, -0.033, 2, 1.567, -0.013, 2, 1.6, 0.006, 2, 1.633, 0.023, 2, 1.667, 0.034, 2, 1.7, 0.038, 2, 1.733, 0.036, 2, 1.767, 0.031, 2, 1.8, 0.024, 2, 1.833, 0.015, 2, 1.867, 0.006, 2, 1.9, -0.003, 2, 1.933, -0.01, 2, 1.967, -0.015, 2, 2, -0.017, 2, 2.033, 0.039, 2, 2.067, 0.164, 2, 2.1, 0.288, 2, 2.133, 0.345, 2, 2.167, 0.302, 2, 2.2, 0.188, 2, 2.233, 0.028, 2, 2.267, -0.155, 2, 2.3, -0.339, 2, 2.333, -0.499, 2, 2.367, -0.612, 2, 2.4, -0.655, 2, 2.433, -0.618, 2, 2.467, -0.517, 2, 2.5, -0.364, 2, 2.533, -0.182, 2, 2.567, 0.015, 2, 2.6, 0.213, 2, 2.633, 0.395, 2, 2.667, 0.547, 2, 2.7, 0.649, 2, 2.733, 0.686, 2, 2.767, 0.651, 2, 2.8, 0.558, 2, 2.833, 0.417, 2, 2.867, 0.246, 2, 2.9, 0.052, 2, 2.933, -0.145, 2, 2.967, -0.34, 2, 3, -0.51, 2, 3.033, -0.652, 2, 3.067, -0.744, 2, 3.1, -0.779, 2, 3.133, -0.732, 2, 3.167, -0.606, 2, 3.2, -0.424, 2, 3.233, -0.207, 2, 3.267, 0.016, 2, 3.3, 0.232, 2, 3.333, 0.415, 2, 3.367, 0.541, 2, 3.4, 0.588, 2, 3.433, 0.569, 2, 3.467, 0.517, 2, 3.5, 0.438, 2, 3.533, 0.343, 2, 3.567, 0.234, 2, 3.6, 0.124, 2, 3.633, 0.016, 2, 3.667, -0.079, 2, 3.7, -0.158, 2, 3.733, -0.21, 2, 3.767, -0.229, 2, 3.8, -0.203, 2, 3.833, -0.137, 2, 3.867, -0.044, 2, 3.9, 0.054, 2, 3.933, 0.146, 2, 3.967, 0.213, 2, 4, 0.239, 2, 4.033, 0.219, 2, 4.067, 0.165, 2, 4.1, 0.088, 2, 4.133, -0.004, 2, 4.167, -0.098, 2, 4.2, -0.19, 2, 4.233, -0.267, 2, 4.267, -0.32, 2, 4.3, -0.34, 2, 4.333, -0.322, 2, 4.367, -0.274, 2, 4.4, -0.204, 2, 4.433, -0.121, 2, 4.467, -0.035, 2, 4.5, 0.048, 2, 4.533, 0.119, 2, 4.567, 0.167, 2, 4.6, 0.185, 2, 4.633, 0.176, 2, 4.667, 0.151, 2, 4.7, 0.115, 2, 4.733, 0.073, 2, 4.767, 0.029, 2, 4.8, -0.013, 2, 4.833, -0.049, 2, 4.867, -0.074, 2, 4.9, -0.083, 2, 4.933, -0.078, 2, 4.967, -0.064, 2, 5, -0.045, 2, 5.033, -0.023, 2, 5.067, -0.001, 2, 5.1, 0.019, 2, 5.133, 0.032, 2, 5.167, 0.037, 2, 5.2, 0.036, 2, 5.233, 0.031, 2, 5.267, 0.023, 2, 5.3, 0.015, 2, 5.333, 0.006, 2, 5.367, -0.003, 2, 5.4, -0.01, 2, 5.433, -0.015, 2, 5.467, -0.017, 2, 5.5, -0.016, 2, 5.533, -0.014, 2, 5.567, -0.011, 2, 5.6, -0.007, 2, 5.633, -0.003, 2, 5.667, 0.001, 2, 5.7, 0.005, 2, 5.733, 0.007, 2, 5.767, 0.008, 2, 5.8, 0.007, 2, 5.833, 0.006, 2, 5.867, 0.005, 2, 5.9, 0.003, 2, 5.933, 0.001, 2, 5.967, -0.001, 2, 6, -0.002, 2, 6.033, -0.003, 2, 6.067, -0.004, 2, 6.1, -0.003, 2, 6.133, -0.003, 2, 6.167, -0.002, 2, 6.2, -0.001, 2, 6.233, -0.001, 2, 6.267, 0, 2, 6.3, 0.001, 2, 6.333, 0.001, 2, 6.367, 0.002, 2, 6.4, 0.002, 2, 6.433, 0.001, 2, 6.467, 0.001, 2, 6.5, 0.001, 2, 6.533, 0, 2, 6.567, 0, 2, 6.6, 0, 2, 6.633, -0.001, 2, 6.667, -0.001, 2, 6.733, -0.001, 2, 6.767, -0.001, 2, 6.8, 0, 2, 6.833, 0, 2, 6.867, 0, 2, 6.9, 0, 2, 6.933, 0, 2, 6.967, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.033, 0.005, 2, 0.067, 0.019, 2, 0.1, 0.036, 2, 0.133, 0.054, 2, 0.167, 0.067, 2, 0.2, 0.072, 2, 0.233, 0.065, 2, 0.267, 0.044, 2, 0.3, 0.012, 2, 0.333, -0.026, 2, 0.367, -0.067, 2, 0.4, -0.108, 2, 0.433, -0.146, 2, 0.467, -0.178, 2, 0.5, -0.199, 2, 0.533, -0.206, 2, 0.567, -0.202, 2, 0.6, -0.189, 2, 0.633, -0.169, 2, 0.667, -0.143, 2, 0.7, -0.112, 2, 0.733, -0.079, 2, 0.767, -0.044, 2, 0.8, -0.008, 2, 0.833, 0.028, 2, 0.867, 0.061, 2, 0.9, 0.092, 2, 0.933, 0.118, 2, 0.967, 0.138, 2, 1, 0.151, 2, 1.033, 0.155, 2, 1.067, 0.15, 2, 1.1, 0.137, 2, 1.133, 0.117, 2, 1.167, 0.091, 2, 1.2, 0.063, 2, 1.233, 0.033, 2, 1.267, 0.002, 2, 1.3, -0.026, 2, 1.333, -0.052, 2, 1.367, -0.072, 2, 1.4, -0.085, 2, 1.433, -0.09, 2, 1.467, -0.086, 2, 1.5, -0.076, 2, 1.533, -0.061, 2, 1.567, -0.043, 2, 1.6, -0.024, 2, 1.633, -0.004, 2, 1.667, 0.014, 2, 1.7, 0.029, 2, 1.733, 0.039, 2, 1.767, 0.043, 2, 1.8, 0.04, 2, 1.833, 0.033, 2, 1.867, 0.023, 2, 1.9, 0.012, 2, 1.933, 0.002, 2, 1.967, -0.005, 2, 2, -0.008, 2, 2.033, 0.016, 2, 2.067, 0.075, 2, 2.1, 0.146, 2, 2.133, 0.204, 2, 2.167, 0.229, 2, 2.2, 0.198, 2, 2.233, 0.116, 2, 2.267, 0, 2, 2.3, -0.134, 2, 2.333, -0.267, 2, 2.367, -0.383, 2, 2.4, -0.465, 2, 2.433, -0.496, 2, 2.467, -0.47, 2, 2.5, -0.401, 2, 2.533, -0.295, 2, 2.567, -0.167, 2, 2.6, -0.021, 2, 2.633, 0.126, 2, 2.667, 0.271, 2, 2.7, 0.399, 2, 2.733, 0.505, 2, 2.767, 0.574, 2, 2.8, 0.6, 2, 2.833, 0.564, 2, 2.867, 0.465, 2, 2.9, 0.317, 2, 2.933, 0.139, 2, 2.967, -0.053, 2, 3, -0.246, 2, 3.033, -0.423, 2, 3.067, -0.572, 2, 3.1, -0.67, 2, 3.133, -0.707, 2, 3.167, -0.677, 2, 3.2, -0.598, 2, 3.233, -0.477, 2, 3.267, -0.33, 2, 3.3, -0.164, 2, 3.333, 0.005, 2, 3.367, 0.172, 2, 3.4, 0.318, 2, 3.433, 0.439, 2, 3.467, 0.518, 2, 3.5, 0.548, 2, 3.533, 0.526, 2, 3.567, 0.468, 2, 3.6, 0.379, 2, 3.633, 0.274, 2, 3.667, 0.159, 2, 3.7, 0.045, 2, 3.733, -0.061, 2, 3.767, -0.149, 2, 3.8, -0.208, 2, 3.833, -0.23, 2, 3.867, -0.215, 2, 3.9, -0.176, 2, 3.933, -0.121, 2, 3.967, -0.058, 2, 4, 0.006, 2, 4.033, 0.061, 2, 4.067, 0.1, 2, 4.1, 0.115, 2, 4.133, 0.102, 2, 4.167, 0.067, 2, 4.2, 0.019, 2, 4.233, -0.036, 2, 4.267, -0.092, 2, 4.3, -0.14, 2, 4.333, -0.174, 2, 4.367, -0.187, 2, 4.4, -0.179, 2, 4.433, -0.158, 2, 4.467, -0.125, 2, 4.5, -0.086, 2, 4.533, -0.043, 2, 4.567, -0.001, 2, 4.6, 0.038, 2, 4.633, 0.071, 2, 4.667, 0.092, 2, 4.7, 0.1, 2, 4.733, 0.096, 2, 4.767, 0.085, 2, 4.8, 0.068, 2, 4.833, 0.048, 2, 4.867, 0.026, 2, 4.9, 0.005, 2, 4.933, -0.016, 2, 4.967, -0.032, 2, 5, -0.044, 2, 5.033, -0.048, 2, 5.067, -0.046, 2, 5.1, -0.04, 2, 5.133, -0.033, 2, 5.167, -0.023, 2, 5.2, -0.013, 2, 5.233, -0.002, 2, 5.267, 0.007, 2, 5.3, 0.015, 2, 5.333, 0.02, 2, 5.367, 0.022, 2, 5.4, 0.022, 2, 5.433, 0.019, 2, 5.467, 0.016, 2, 5.5, 0.012, 2, 5.533, 0.008, 2, 5.567, 0.004, 2, 5.6, -0.001, 2, 5.633, -0.005, 2, 5.667, -0.008, 2, 5.7, -0.01, 2, 5.733, -0.011, 2, 5.767, -0.01, 2, 5.8, -0.009, 2, 5.833, -0.007, 2, 5.867, -0.005, 2, 5.9, -0.003, 2, 5.933, 0, 2, 5.967, 0.002, 2, 6, 0.003, 2, 6.033, 0.005, 2, 6.067, 0.005, 2, 6.1, 0.005, 2, 6.133, 0.004, 2, 6.167, 0.004, 2, 6.2, 0.003, 2, 6.233, 0.002, 2, 6.267, 0.001, 2, 6.3, 0, 2, 6.333, -0.001, 2, 6.367, -0.002, 2, 6.4, -0.002, 2, 6.433, -0.002, 2, 6.467, -0.002, 2, 6.5, -0.002, 2, 6.533, -0.002, 2, 6.567, -0.001, 2, 6.6, -0.001, 2, 6.633, 0, 2, 6.667, 0, 2, 6.7, 0.001, 2, 6.733, 0.001, 2, 6.767, 0.001, 2, 6.833, 0.001, 2, 6.867, 0.001, 2, 6.9, 0.001, 2, 6.933, 0, 2, 6.967, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.033, 0.014, 2, 0.067, 0.048, 2, 0.1, 0.09, 2, 0.133, 0.124, 2, 0.167, 0.138, 2, 0.2, 0.121, 2, 0.233, 0.077, 2, 0.267, 0.014, 2, 0.3, -0.062, 2, 0.333, -0.14, 2, 0.367, -0.215, 2, 0.4, -0.279, 2, 0.433, -0.323, 2, 0.467, -0.339, 2, 0.5, -0.334, 2, 0.533, -0.319, 2, 0.567, -0.296, 2, 0.6, -0.267, 2, 0.633, -0.231, 2, 0.667, -0.191, 2, 0.7, -0.148, 2, 0.733, -0.103, 2, 0.767, -0.058, 2, 0.8, -0.013, 2, 0.833, 0.03, 2, 0.867, 0.07, 2, 0.9, 0.106, 2, 0.933, 0.135, 2, 0.967, 0.158, 2, 1, 0.173, 2, 1.033, 0.178, 2, 1.1, 0.168, 2, 1.133, 0.142, 2, 1.167, 0.103, 2, 1.2, 0.058, 2, 1.233, 0.011, 2, 1.267, -0.035, 2, 1.3, -0.073, 2, 1.333, -0.1, 2, 1.367, -0.11, 2, 1.4, -0.104, 2, 1.433, -0.09, 2, 1.467, -0.069, 2, 1.5, -0.044, 2, 1.533, -0.018, 2, 1.567, 0.007, 2, 1.6, 0.027, 2, 1.633, 0.042, 2, 1.667, 0.047, 2, 1.7, 0.044, 2, 1.733, 0.037, 2, 1.767, 0.026, 2, 1.8, 0.013, 2, 1.833, 0.001, 2, 1.867, -0.01, 2, 1.9, -0.018, 2, 1.933, -0.021, 2, 1.967, -0.02, 2, 2, -0.016, 2, 2.033, -0.011, 2, 2.067, -0.006, 2, 2.1, 0, 2, 2.133, 0.004, 2, 2.167, 0.008, 2, 2.2, 0.009, 2, 2.233, 0.009, 2, 2.267, 0.007, 2, 2.3, 0.005, 2, 2.333, 0.003, 2, 2.367, 0, 2, 2.4, -0.002, 2, 2.433, -0.003, 2, 2.467, -0.004, 2, 2.5, -0.004, 2, 2.533, -0.003, 2, 2.567, -0.002, 2, 2.6, -0.001, 2, 2.633, 0, 2, 2.667, 0.001, 2, 2.7, 0.001, 2, 2.733, 0.002, 2, 2.8, 0.002, 2, 2.833, 0.001, 2, 2.867, 0.001, 2, 2.9, 0, 2, 2.933, 0, 2, 2.967, 0, 2, 3, -0.001, 2, 3.033, -0.001, 2, 3.067, -0.001, 2, 3.1, -0.025, 2, 3.133, -0.083, 2, 3.167, -0.154, 2, 3.2, -0.212, 2, 3.233, -0.237, 2, 3.267, -0.206, 2, 3.3, -0.125, 2, 3.333, -0.007, 2, 3.367, 0.133, 2, 3.4, 0.277, 2, 3.433, 0.417, 2, 3.467, 0.535, 2, 3.5, 0.616, 2, 3.533, 0.647, 2, 3.567, 0.634, 2, 3.6, 0.598, 2, 3.633, 0.541, 2, 3.667, 0.469, 2, 3.7, 0.382, 2, 3.733, 0.288, 2, 3.767, 0.189, 2, 3.8, 0.088, 2, 3.833, -0.012, 2, 3.867, -0.106, 2, 3.9, -0.192, 2, 3.933, -0.264, 2, 3.967, -0.321, 2, 4, -0.358, 2, 4.033, -0.37, 2, 4.067, -0.357, 2, 4.1, -0.321, 2, 4.133, -0.266, 2, 4.167, -0.2, 2, 4.2, -0.129, 2, 4.233, -0.058, 2, 4.267, 0.007, 2, 4.3, 0.062, 2, 4.333, 0.099, 2, 4.367, 0.112, 2, 4.4, 0.108, 2, 4.433, 0.098, 2, 4.467, 0.083, 2, 4.5, 0.065, 2, 4.533, 0.044, 2, 4.567, 0.023, 2, 4.6, 0.002, 2, 4.633, -0.016, 2, 4.667, -0.032, 2, 4.7, -0.042, 2, 4.733, -0.045, 2, 4.767, -0.043, 2, 4.8, -0.035, 2, 4.833, -0.025, 2, 4.867, -0.013, 2, 4.9, -0.001, 2, 4.933, 0.009, 2, 4.967, 0.017, 2, 5, 0.02, 2, 5.033, 0.019, 2, 5.067, 0.016, 2, 5.1, 0.012, 2, 5.133, 0.008, 2, 5.167, 0.003, 2, 5.2, -0.001, 2, 5.233, -0.005, 2, 5.267, -0.008, 2, 5.3, -0.009, 2, 5.333, -0.008, 2, 5.367, -0.007, 2, 5.4, -0.005, 2, 5.433, -0.002, 2, 5.467, 0, 2, 5.5, 0.002, 2, 5.533, 0.003, 2, 5.567, 0.004, 2, 5.6, 0.004, 2, 5.633, 0.003, 2, 5.667, 0.002, 2, 5.7, 0.001, 2, 5.733, 0, 2, 5.767, -0.001, 2, 5.8, -0.001, 2, 5.833, -0.002, 2, 5.867, -0.002, 2, 5.9, -0.001, 2, 5.933, -0.001, 2, 5.967, 0, 2, 6, 0, 2, 6.033, 0, 2, 6.067, 0.001, 2, 6.1, 0.001, 2, 6.167, 0.001, 2, 6.2, 0, 2, 6.233, 0, 2, 6.267, 0, 2, 6.3, 0, 2, 6.333, 0, 2, 6.367, 0, 2, 6.4, 0, 2, 6.467, 0, 2, 6.5, 0, 2, 6.533, 0, 2, 6.6, 0, 2, 6.633, 0, 2, 6.667, 0, 2, 6.8, 0, 2, 6.9, 0, 2, 6.933, 0, 2, 6.967, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.033, -0.003, 2, 0.067, -0.01, 2, 0.1, -0.019, 2, 0.133, -0.028, 2, 0.167, -0.035, 2, 0.2, -0.037, 2, 0.233, -0.034, 2, 0.267, -0.024, 2, 0.3, -0.01, 2, 0.333, 0.008, 2, 0.367, 0.028, 2, 0.4, 0.048, 2, 0.433, 0.068, 2, 0.467, 0.085, 2, 0.5, 0.1, 2, 0.533, 0.109, 2, 0.567, 0.113, 2, 0.6, 0.11, 2, 0.633, 0.103, 2, 0.667, 0.091, 2, 0.7, 0.076, 2, 0.733, 0.058, 2, 0.767, 0.039, 2, 0.8, 0.018, 2, 0.833, -0.003, 2, 0.867, -0.023, 2, 0.9, -0.043, 2, 0.933, -0.061, 2, 0.967, -0.075, 2, 1, -0.087, 2, 1.033, -0.095, 2, 1.067, -0.097, 2, 1.1, -0.094, 2, 1.133, -0.086, 2, 1.167, -0.073, 2, 1.2, -0.057, 2, 1.233, -0.039, 2, 1.267, -0.02, 2, 1.3, -0.001, 2, 1.333, 0.017, 2, 1.367, 0.033, 2, 1.4, 0.046, 2, 1.433, 0.054, 2, 1.467, 0.057, 2, 1.5, 0.055, 2, 1.533, 0.05, 2, 1.567, 0.042, 2, 1.6, 0.032, 2, 1.633, 0.02, 2, 1.667, 0.009, 2, 1.7, -0.002, 2, 1.733, -0.012, 2, 1.767, -0.02, 2, 1.8, -0.025, 2, 1.833, -0.028, 2, 1.867, -0.027, 2, 1.9, -0.024, 2, 1.933, -0.02, 2, 1.967, -0.015, 2, 2, -0.01, 2, 2.033, -0.004, 2, 2.067, 0.001, 2, 2.1, 0.006, 2, 2.133, 0.01, 2, 2.167, 0.012, 2, 2.2, 0.013, 2, 2.233, 0.013, 2, 2.267, 0.012, 2, 2.3, 0.01, 2, 2.333, 0.008, 2, 2.367, 0.006, 2, 2.4, 0.003, 2, 2.433, 0.001, 2, 2.467, -0.001, 2, 2.5, -0.003, 2, 2.533, -0.005, 2, 2.567, -0.006, 2, 2.6, -0.006, 2, 2.633, -0.006, 2, 2.667, -0.005, 2, 2.7, -0.005, 2, 2.733, -0.004, 2, 2.767, -0.002, 2, 2.8, -0.001, 2, 2.833, 0, 2, 2.867, 0.001, 2, 2.9, 0.002, 2, 2.933, 0.003, 2, 2.967, 0.003, 2, 3, 0.003, 2, 3.033, 0.002, 2, 3.067, 0.002, 2, 3.1, 0.007, 2, 3.133, 0.017, 2, 3.167, 0.032, 2, 3.2, 0.046, 2, 3.233, 0.057, 2, 3.267, 0.061, 2, 3.3, 0.054, 2, 3.333, 0.033, 2, 3.367, 0.002, 2, 3.4, -0.035, 2, 3.433, -0.075, 2, 3.467, -0.115, 2, 3.5, -0.152, 2, 3.533, -0.183, 2, 3.567, -0.204, 2, 3.6, -0.211, 2, 3.633, -0.205, 2, 3.667, -0.189, 2, 3.7, -0.163, 2, 3.733, -0.13, 2, 3.767, -0.092, 2, 3.8, -0.05, 2, 3.833, -0.006, 2, 3.867, 0.037, 2, 3.9, 0.079, 2, 3.933, 0.118, 2, 3.967, 0.15, 2, 4, 0.176, 2, 4.033, 0.193, 2, 4.067, 0.199, 2, 4.1, 0.194, 2, 4.133, 0.179, 2, 4.167, 0.158, 2, 4.2, 0.131, 2, 4.233, 0.099, 2, 4.267, 0.065, 2, 4.3, 0.03, 2, 4.333, -0.004, 2, 4.367, -0.035, 2, 4.4, -0.063, 2, 4.433, -0.084, 2, 4.467, -0.098, 2, 4.5, -0.103, 2, 4.533, -0.1, 2, 4.567, -0.09, 2, 4.6, -0.075, 2, 4.633, -0.058, 2, 4.667, -0.037, 2, 4.7, -0.017, 2, 4.733, 0.003, 2, 4.767, 0.021, 2, 4.8, 0.036, 2, 4.833, 0.046, 2, 4.867, 0.049, 2, 4.9, 0.048, 2, 4.933, 0.044, 2, 4.967, 0.038, 2, 5, 0.03, 2, 5.033, 0.022, 2, 5.067, 0.013, 2, 5.1, 0.004, 2, 5.133, -0.005, 2, 5.167, -0.012, 2, 5.2, -0.018, 2, 5.233, -0.022, 2, 5.267, -0.024, 2, 5.3, -0.023, 2, 5.333, -0.02, 2, 5.367, -0.017, 2, 5.4, -0.013, 2, 5.433, -0.008, 2, 5.467, -0.004, 2, 5.5, 0.001, 2, 5.533, 0.005, 2, 5.567, 0.008, 2, 5.6, 0.01, 2, 5.633, 0.011, 2, 5.667, 0.011, 2, 5.7, 0.01, 2, 5.733, 0.008, 2, 5.767, 0.006, 2, 5.8, 0.004, 2, 5.833, 0.002, 2, 5.867, 0, 2, 5.9, -0.002, 2, 5.933, -0.004, 2, 5.967, -0.005, 2, 6, -0.005, 2, 6.067, -0.005, 2, 6.1, -0.005, 2, 6.133, -0.004, 2, 6.167, -0.003, 2, 6.2, -0.002, 2, 6.233, -0.001, 2, 6.267, 0, 2, 6.3, 0.001, 2, 6.333, 0.002, 2, 6.367, 0.002, 2, 6.4, 0.003, 2, 6.433, 0.002, 2, 6.467, 0.002, 2, 6.5, 0.002, 2, 6.533, 0.001, 2, 6.567, 0.001, 2, 6.6, 0, 2, 6.633, 0, 2, 6.667, -0.001, 2, 6.7, -0.001, 2, 6.733, -0.001, 2, 6.833, -0.001, 2, 6.867, -0.001, 2, 6.9, -0.001, 2, 6.933, -0.001, 2, 6.967, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh0", "Segments": [0, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh0", "Segments": [0, 0, 2, 0.033, 0.092, 2, 0.067, 0.314, 2, 0.1, 0.61, 2, 0.133, 0.906, 2, 0.167, 1.128, 2, 0.2, 1.22, 2, 0.233, 1.107, 2, 0.267, 0.8, 2, 0.3, 0.337, 2, 0.333, -0.215, 2, 0.367, -0.815, 2, 0.4, -1.414, 2, 0.433, -1.966, 2, 0.467, -2.429, 2, 0.5, -2.736, 2, 0.533, -2.849, 2, 0.567, -2.807, 2, 0.6, -2.688, 2, 0.633, -2.5, 2, 0.667, -2.257, 2, 0.7, -1.969, 2, 0.733, -1.639, 2, 0.767, -1.289, 2, 0.8, -0.923, 2, 0.833, -0.553, 2, 0.867, -0.187, 2, 0.9, 0.163, 2, 0.933, 0.493, 2, 0.967, 0.782, 2, 1, 1.024, 2, 1.033, 1.213, 2, 1.067, 1.331, 2, 1.1, 1.373, 2, 1.133, 1.32, 2, 1.167, 1.175, 2, 1.2, 0.957, 2, 1.233, 0.696, 2, 1.267, 0.413, 2, 1.3, 0.131, 2, 1.333, -0.13, 2, 1.367, -0.348, 2, 1.4, -0.493, 2, 1.433, -0.546, 2, 1.467, -0.516, 2, 1.5, -0.438, 2, 1.533, -0.327, 2, 1.567, -0.2, 2, 1.6, -0.073, 2, 1.633, 0.038, 2, 1.667, 0.116, 2, 1.7, 0.146, 2, 1.733, 0.139, 2, 1.767, 0.122, 2, 1.8, 0.098, 2, 1.833, 0.068, 2, 1.867, 0.038, 2, 1.9, 0.009, 2, 1.933, -0.016, 2, 1.967, -0.033, 2, 2, -0.039, 2, 2.033, 0.614, 2, 2.067, 2.053, 2, 2.1, 3.491, 2, 2.133, 4.145, 2, 2.167, 3.768, 2, 2.2, 2.759, 2, 2.233, 1.299, 2, 2.267, -0.431, 2, 2.3, -2.219, 2, 2.333, -3.95, 2, 2.367, -5.41, 2, 2.4, -6.419, 2, 2.433, -6.795, 2, 2.467, -6.484, 2, 2.5, -5.655, 2, 2.533, -4.391, 2, 2.567, -2.862, 2, 2.6, -1.122, 2, 2.633, 0.64, 2, 2.667, 2.38, 2, 2.7, 3.909, 2, 2.733, 5.173, 2, 2.767, 6.002, 2, 2.8, 6.314, 2, 2.833, 5.936, 2, 2.867, 4.911, 2, 2.9, 3.365, 2, 2.933, 1.523, 2, 2.967, -0.48, 2, 3, -2.483, 2, 3.033, -4.325, 2, 3.067, -5.87, 2, 3.1, -6.895, 2, 3.133, -7.273, 2, 3.167, -6.949, 2, 3.2, -6.071, 2, 3.233, -4.747, 2, 3.267, -3.17, 2, 3.3, -1.454, 2, 3.333, 0.262, 2, 3.367, 1.84, 2, 3.4, 3.164, 2, 3.433, 4.042, 2, 3.467, 4.365, 2, 3.5, 4.223, 2, 3.533, 3.84, 2, 3.567, 3.286, 2, 3.6, 2.629, 2, 3.633, 1.951, 2, 3.667, 1.294, 2, 3.7, 0.74, 2, 3.733, 0.358, 2, 3.767, 0.215, 2, 3.8, 0.405, 2, 3.833, 0.868, 2, 3.867, 1.483, 2, 3.9, 2.099, 2, 3.933, 2.561, 2, 3.967, 2.752, 2, 4, 2.606, 2, 4.033, 2.209, 2, 4.067, 1.61, 2, 4.1, 0.897, 2, 4.133, 0.121, 2, 4.167, -0.655, 2, 4.2, -1.368, 2, 4.233, -1.966, 2, 4.267, -2.363, 2, 4.3, -2.51, 2, 4.333, -2.392, 2, 4.367, -2.078, 2, 4.4, -1.623, 2, 4.433, -1.085, 2, 4.467, -0.528, 2, 4.5, 0.011, 2, 4.533, 0.466, 2, 4.567, 0.78, 2, 4.6, 0.897, 2, 4.633, 0.858, 2, 4.667, 0.753, 2, 4.7, 0.602, 2, 4.733, 0.422, 2, 4.767, 0.237, 2, 4.8, 0.057, 2, 4.833, -0.095, 2, 4.867, -0.199, 2, 4.9, -0.238, 2, 4.933, -0.225, 2, 4.967, -0.191, 2, 5, -0.143, 2, 5.033, -0.088, 2, 5.067, -0.032, 2, 5.1, 0.016, 2, 5.133, 0.05, 2, 5.167, 0.063, 2, 5.2, 0.061, 2, 5.233, 0.053, 2, 5.267, 0.042, 2, 5.3, 0.03, 2, 5.333, 0.017, 2, 5.367, 0.004, 2, 5.4, -0.007, 2, 5.433, -0.014, 2, 5.467, -0.017, 2, 5.5, -0.016, 2, 5.533, -0.014, 2, 5.567, -0.011, 2, 5.6, -0.008, 2, 5.633, -0.004, 2, 5.667, -0.001, 2, 5.7, 0.002, 2, 5.733, 0.004, 2, 5.767, 0.005, 2, 5.8, 0.004, 2, 5.833, 0.004, 2, 5.867, 0.003, 2, 5.9, 0.002, 2, 5.933, 0.001, 2, 5.967, 0, 2, 6, 0, 2, 6.033, -0.001, 2, 6.067, -0.001, 2, 6.133, -0.001, 2, 6.167, -0.001, 2, 6.2, -0.001, 2, 6.233, 0, 2, 6.267, 0, 2, 6.3, 0, 2, 6.333, 0, 2, 6.433, 0, 2, 6.5, 0, 2, 6.533, 0, 2, 6.6, 0, 2, 6.667, 0, 2, 6.7, 0, 2, 6.767, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh0", "Segments": [0, 0, 2, 0.033, -0.095, 2, 0.067, -0.303, 2, 0.1, -0.512, 2, 0.133, -0.607, 2, 0.167, -0.512, 2, 0.2, -0.263, 2, 0.233, 0.089, 2, 0.267, 0.493, 2, 0.3, 0.897, 2, 0.333, 1.249, 2, 0.367, 1.498, 2, 0.4, 1.593, 2, 0.433, 1.47, 2, 0.467, 1.145, 2, 0.5, 0.686, 2, 0.533, 0.16, 2, 0.567, -0.367, 2, 0.6, -0.826, 2, 0.633, -1.151, 2, 0.667, -1.274, 2, 0.7, -1.234, 2, 0.733, -1.127, 2, 0.767, -0.972, 2, 0.8, -0.788, 2, 0.833, -0.598, 2, 0.867, -0.415, 2, 0.9, -0.26, 2, 0.933, -0.153, 2, 0.967, -0.113, 2, 1, -0.153, 2, 1.033, -0.193, 2, 1.067, -0.137, 2, 1.1, 0.006, 2, 1.133, 0.205, 2, 1.167, 0.415, 2, 1.2, 0.614, 2, 1.233, 0.757, 2, 1.267, 0.812, 2, 1.3, 0.761, 2, 1.333, 0.624, 2, 1.367, 0.426, 2, 1.4, 0.191, 2, 1.433, -0.052, 2, 1.467, -0.287, 2, 1.5, -0.485, 2, 1.533, -0.622, 2, 1.567, -0.673, 2, 1.6, -0.63, 2, 1.633, -0.518, 2, 1.667, -0.359, 2, 1.7, -0.176, 2, 1.733, 0.006, 2, 1.767, 0.165, 2, 1.8, 0.278, 2, 1.833, 0.321, 2, 1.867, 0.226, 2, 1.9, -0.029, 2, 1.933, -0.397, 2, 1.967, -0.833, 2, 2, -1.284, 2, 2.033, -1.72, 2, 2.067, -2.088, 2, 2.1, -2.342, 2, 2.133, -2.437, 2, 2.167, -1.679, 2, 2.2, 0.131, 2, 2.233, 2.337, 2, 2.267, 4.147, 2, 2.3, 4.905, 2, 2.333, 4.521, 2, 2.367, 3.494, 2, 2.4, 2.007, 2, 2.433, 0.244, 2, 2.467, -1.576, 2, 2.5, -3.338, 2, 2.533, -4.825, 2, 2.567, -5.853, 2, 2.6, -6.236, 2, 2.633, -5.918, 2, 2.667, -5.052, 2, 2.7, -3.748, 2, 2.733, -2.193, 2, 2.767, -0.502, 2, 2.8, 1.189, 2, 2.833, 2.743, 2, 2.867, 4.048, 2, 2.9, 4.913, 2, 2.933, 5.232, 2, 2.967, 4.895, 2, 3, 3.981, 2, 3.033, 2.602, 2, 3.067, 0.96, 2, 3.1, -0.827, 2, 3.133, -2.614, 2, 3.167, -4.257, 2, 3.2, -5.635, 2, 3.233, -6.549, 2, 3.267, -6.886, 2, 3.3, -6.609, 2, 3.333, -5.856, 2, 3.367, -4.72, 2, 3.4, -3.367, 2, 3.433, -1.895, 2, 3.467, -0.424, 2, 3.5, 0.929, 2, 3.533, 2.065, 2, 3.567, 2.818, 2, 3.6, 3.095, 2, 3.633, 2.936, 2, 3.667, 2.503, 2, 3.7, 1.852, 2, 3.733, 1.074, 2, 3.767, 0.229, 2, 3.8, -0.616, 2, 3.833, -1.393, 2, 3.867, -2.045, 2, 3.9, -2.477, 2, 3.933, -2.636, 2, 3.967, -2.417, 2, 4, -1.838, 2, 4.033, -1.019, 2, 4.067, -0.08, 2, 4.1, 0.859, 2, 4.133, 1.678, 2, 4.167, 2.257, 2, 4.2, 2.477, 2, 4.233, 2.259, 2, 4.267, 1.687, 2, 4.3, 0.877, 2, 4.333, -0.052, 2, 4.367, -0.98, 2, 4.4, -1.79, 2, 4.433, -2.363, 2, 4.467, -2.58, 2, 4.5, -2.407, 2, 4.533, -1.95, 2, 4.567, -1.304, 2, 4.6, -0.564, 2, 4.633, 0.176, 2, 4.667, 0.821, 2, 4.7, 1.278, 2, 4.733, 1.451, 2, 4.767, 1.38, 2, 4.8, 1.191, 2, 4.833, 0.916, 2, 4.867, 0.59, 2, 4.9, 0.254, 2, 4.933, -0.072, 2, 4.967, -0.346, 2, 5, -0.536, 2, 5.033, -0.607, 2, 5.067, -0.571, 2, 5.1, -0.477, 2, 5.133, -0.345, 2, 5.167, -0.192, 2, 5.2, -0.04, 2, 5.233, 0.093, 2, 5.267, 0.187, 2, 5.3, 0.222, 2, 5.333, 0.212, 2, 5.367, 0.184, 2, 5.4, 0.144, 2, 5.433, 0.097, 2, 5.467, 0.048, 2, 5.5, 0.001, 2, 5.533, -0.039, 2, 5.567, -0.066, 2, 5.6, -0.076, 2, 5.633, -0.073, 2, 5.667, -0.064, 2, 5.7, -0.05, 2, 5.733, -0.034, 2, 5.767, -0.018, 2, 5.8, -0.001, 2, 5.833, 0.012, 2, 5.867, 0.021, 2, 5.9, 0.025, 2, 5.933, 0.024, 2, 5.967, 0.021, 2, 6, 0.016, 2, 6.033, 0.011, 2, 6.067, 0.006, 2, 6.1, 0.001, 2, 6.133, -0.004, 2, 6.167, -0.007, 2, 6.2, -0.008, 2, 6.233, -0.007, 2, 6.267, -0.006, 2, 6.3, -0.005, 2, 6.333, -0.003, 2, 6.367, -0.001, 2, 6.4, 0.001, 2, 6.433, 0.002, 2, 6.467, 0.002, 2, 6.533, 0.002, 2, 6.567, 0.002, 2, 6.6, 0.001, 2, 6.633, 0.001, 2, 6.667, 0, 2, 6.7, 0, 2, 6.733, -0.001, 2, 6.767, -0.001, 2, 6.833, -0.001, 2, 6.867, -0.001, 2, 6.9, 0, 2, 6.933, 0, 2, 6.967, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh0", "Segments": [0, 0, 2, 0.033, -0.027, 2, 0.067, -0.097, 2, 0.1, -0.195, 2, 0.133, -0.298, 2, 0.167, -0.396, 2, 0.2, -0.466, 2, 0.233, -0.493, 2, 0.267, -0.398, 2, 0.3, -0.148, 2, 0.333, 0.207, 2, 0.367, 0.613, 2, 0.4, 1.019, 2, 0.433, 1.373, 2, 0.467, 1.623, 2, 0.5, 1.718, 2, 0.533, 1.597, 2, 0.567, 1.272, 2, 0.6, 0.801, 2, 0.633, 0.243, 2, 0.667, -0.333, 2, 0.7, -0.891, 2, 0.733, -1.361, 2, 0.767, -1.687, 2, 0.8, -1.808, 2, 0.833, -1.779, 2, 0.867, -1.696, 2, 0.9, -1.567, 2, 0.933, -1.399, 2, 0.967, -1.201, 2, 1, -0.98, 2, 1.033, -0.743, 2, 1.067, -0.499, 2, 1.1, -0.255, 2, 1.133, -0.019, 2, 1.167, 0.202, 2, 1.2, 0.401, 2, 1.233, 0.568, 2, 1.267, 0.697, 2, 1.3, 0.78, 2, 1.333, 0.809, 2, 1.367, 0.762, 2, 1.4, 0.635, 2, 1.433, 0.442, 2, 1.467, 0.212, 2, 1.5, -0.037, 2, 1.533, -0.287, 2, 1.567, -0.517, 2, 1.6, -0.709, 2, 1.633, -0.837, 2, 1.667, -0.884, 2, 1.7, -0.835, 2, 1.733, -0.703, 2, 1.767, -0.512, 2, 1.8, -0.286, 2, 1.833, -0.052, 2, 1.867, 0.175, 2, 1.9, 0.366, 2, 1.933, 0.498, 2, 1.967, 0.547, 2, 2, 0.408, 2, 2.033, 0.048, 2, 2.067, -0.451, 2, 2.1, -0.981, 2, 2.133, -1.48, 2, 2.167, -1.84, 2, 2.2, -1.979, 2, 2.233, -1.467, 2, 2.267, -0.226, 2, 2.3, 1.425, 2, 2.333, 3.075, 2, 2.367, 4.317, 2, 2.4, 4.828, 2, 2.433, 4.416, 2, 2.467, 3.312, 2, 2.5, 1.715, 2, 2.533, -0.179, 2, 2.567, -2.135, 2, 2.6, -4.028, 2, 2.633, -5.626, 2, 2.667, -6.73, 2, 2.7, -7.142, 2, 2.733, -6.75, 2, 2.767, -5.684, 2, 2.8, -4.078, 2, 2.833, -2.164, 2, 2.867, -0.082, 2, 2.9, 2, 2, 2.933, 3.915, 2, 2.967, 5.521, 2, 3, 6.586, 2, 3.033, 6.979, 2, 3.067, 6.565, 2, 3.1, 5.44, 2, 3.133, 3.745, 2, 3.167, 1.724, 2, 3.2, -0.473, 2, 3.233, -2.671, 2, 3.267, -4.691, 2, 3.3, -6.386, 2, 3.333, -7.511, 2, 3.367, -7.925, 2, 3.4, -7.558, 2, 3.433, -6.561, 2, 3.467, -5.059, 2, 3.5, -3.268, 2, 3.533, -1.321, 2, 3.567, 0.627, 2, 3.6, 2.418, 2, 3.633, 3.92, 2, 3.667, 4.917, 2, 3.7, 5.284, 2, 3.733, 4.972, 2, 3.767, 4.137, 2, 3.8, 2.928, 2, 3.833, 1.496, 2, 3.867, 0.017, 2, 3.9, -1.415, 2, 3.933, -2.624, 2, 3.967, -3.459, 2, 4, -3.771, 2, 4.033, -3.517, 2, 4.067, -2.837, 2, 4.1, -1.853, 2, 4.133, -0.687, 2, 4.167, 0.518, 2, 4.2, 1.684, 2, 4.233, 2.668, 2, 4.267, 3.348, 2, 4.3, 3.602, 2, 4.333, 3.301, 2, 4.367, 2.509, 2, 4.4, 1.389, 2, 4.433, 0.105, 2, 4.467, -1.18, 2, 4.5, -2.3, 2, 4.533, -3.092, 2, 4.567, -3.393, 2, 4.6, -3.198, 2, 4.633, -2.676, 2, 4.667, -1.92, 2, 4.7, -1.024, 2, 4.733, -0.099, 2, 4.767, 0.797, 2, 4.8, 1.553, 2, 4.833, 2.075, 2, 4.867, 2.27, 2, 4.9, 2.122, 2, 4.933, 1.729, 2, 4.967, 1.175, 2, 5, 0.54, 2, 5.033, -0.096, 2, 5.067, -0.65, 2, 5.1, -1.042, 2, 5.133, -1.191, 2, 5.167, -1.131, 2, 5.2, -0.972, 2, 5.233, -0.741, 2, 5.267, -0.468, 2, 5.3, -0.186, 2, 5.333, 0.088, 2, 5.367, 0.319, 2, 5.4, 0.478, 2, 5.433, 0.537, 2, 5.467, 0.511, 2, 5.5, 0.442, 2, 5.533, 0.341, 2, 5.567, 0.222, 2, 5.6, 0.099, 2, 5.633, -0.021, 2, 5.667, -0.121, 2, 5.7, -0.191, 2, 5.733, -0.217, 2, 5.767, -0.204, 2, 5.8, -0.17, 2, 5.833, -0.123, 2, 5.867, -0.068, 2, 5.9, -0.013, 2, 5.933, 0.035, 2, 5.967, 0.068, 2, 6, 0.081, 2, 6.033, 0.077, 2, 6.067, 0.067, 2, 6.1, 0.052, 2, 6.133, 0.035, 2, 6.167, 0.017, 2, 6.2, -0.001, 2, 6.233, -0.016, 2, 6.267, -0.026, 2, 6.3, -0.03, 2, 6.333, -0.028, 2, 6.367, -0.025, 2, 6.4, -0.019, 2, 6.433, -0.013, 2, 6.467, -0.006, 2, 6.5, 0, 2, 6.533, 0.005, 2, 6.567, 0.009, 2, 6.6, 0.01, 2, 6.633, 0.01, 2, 6.667, 0.009, 2, 6.7, 0.007, 2, 6.733, 0.005, 2, 6.767, 0.002, 2, 6.8, 0, 2, 6.833, -0.002, 2, 6.867, -0.003, 2, 6.9, -0.004, 2, 6.933, -0.003, 2, 6.967, -0.003, 2, 7, -0.002]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh0", "Segments": [0, -0.003, 2, 0.033, -0.019, 2, 0.067, -0.061, 2, 0.1, -0.122, 2, 0.133, -0.194, 2, 0.167, -0.268, 2, 0.2, -0.34, 2, 0.233, -0.401, 2, 0.267, -0.443, 2, 0.3, -0.459, 2, 0.333, -0.358, 2, 0.367, -0.093, 2, 0.4, 0.283, 2, 0.433, 0.714, 2, 0.467, 1.144, 2, 0.5, 1.52, 2, 0.533, 1.786, 2, 0.567, 1.886, 2, 0.6, 1.767, 2, 0.633, 1.443, 2, 0.667, 0.954, 2, 0.7, 0.372, 2, 0.733, -0.262, 2, 0.767, -0.895, 2, 0.8, -1.477, 2, 0.833, -1.966, 2, 0.867, -2.29, 2, 0.9, -2.409, 2, 0.933, -2.314, 2, 0.967, -2.055, 2, 1, -1.664, 2, 1.033, -1.198, 2, 1.067, -0.692, 2, 1.1, -0.185, 2, 1.133, 0.281, 2, 1.167, 0.672, 2, 1.2, 0.931, 2, 1.233, 1.026, 2, 1.267, 1, 2, 1.3, 0.924, 2, 1.333, 0.806, 2, 1.367, 0.656, 2, 1.4, 0.477, 2, 1.433, 0.282, 2, 1.467, 0.075, 2, 1.5, -0.135, 2, 1.533, -0.341, 2, 1.567, -0.536, 2, 1.6, -0.715, 2, 1.633, -0.866, 2, 1.667, -0.984, 2, 1.7, -1.059, 2, 1.733, -1.086, 2, 1.767, -1.02, 2, 1.8, -0.843, 2, 1.833, -0.588, 2, 1.867, -0.286, 2, 1.9, 0.027, 2, 1.933, 0.33, 2, 1.967, 0.585, 2, 2, 0.762, 2, 2.033, 0.827, 2, 2.067, 0.686, 2, 2.1, 0.32, 2, 2.133, -0.187, 2, 2.167, -0.725, 2, 2.2, -1.232, 2, 2.233, -1.597, 2, 2.267, -1.739, 2, 2.3, -1.359, 2, 2.333, -0.38, 2, 2.367, 0.98, 2, 2.4, 2.422, 2, 2.433, 3.782, 2, 2.467, 4.761, 2, 2.5, 5.141, 2, 2.533, 4.564, 2, 2.567, 3.044, 2, 2.6, 0.895, 2, 2.633, -1.57, 2, 2.667, -4.034, 2, 2.7, -6.183, 2, 2.733, -7.704, 2, 2.767, -8.28, 2, 2.8, -7.802, 2, 2.833, -6.505, 2, 2.867, -4.549, 2, 2.9, -2.218, 2, 2.933, 0.318, 2, 2.967, 2.853, 2, 3, 5.184, 2, 3.033, 7.14, 2, 3.067, 8.438, 2, 3.1, 8.915, 2, 3.133, 8.404, 2, 3.167, 7.013, 2, 3.2, 4.918, 2, 3.233, 2.421, 2, 3.267, -0.295, 2, 3.3, -3.011, 2, 3.333, -5.509, 2, 3.367, -7.604, 2, 3.4, -8.994, 2, 3.433, -9.506, 2, 3.467, -9.035, 2, 3.5, -7.758, 2, 3.533, -5.832, 2, 3.567, -3.537, 2, 3.6, -1.041, 2, 3.633, 1.455, 2, 3.667, 3.751, 2, 3.7, 5.676, 2, 3.733, 6.954, 2, 3.767, 7.424, 2, 3.8, 7.066, 2, 3.833, 6.092, 2, 3.867, 4.625, 2, 3.9, 2.876, 2, 3.933, 0.975, 2, 3.967, -0.927, 2, 4, -2.676, 2, 4.033, -4.143, 2, 4.067, -5.117, 2, 4.1, -5.475, 2, 4.133, -5.115, 2, 4.167, -4.151, 2, 4.2, -2.755, 2, 4.233, -1.101, 2, 4.267, 0.608, 2, 4.3, 2.262, 2, 4.333, 3.658, 2, 4.367, 4.623, 2, 4.4, 4.983, 2, 4.433, 4.571, 2, 4.467, 3.485, 2, 4.5, 1.95, 2, 4.533, 0.191, 2, 4.567, -1.569, 2, 4.6, -3.104, 2, 4.633, -4.189, 2, 4.667, -4.601, 2, 4.7, -4.324, 2, 4.733, -3.583, 2, 4.767, -2.509, 2, 4.8, -1.237, 2, 4.833, 0.077, 2, 4.867, 1.349, 2, 4.9, 2.422, 2, 4.933, 3.164, 2, 4.967, 3.44, 2, 5, 3.251, 2, 5.033, 2.743, 2, 5.067, 2.009, 2, 5.1, 1.139, 2, 5.133, 0.24, 2, 5.167, -0.631, 2, 5.2, -1.365, 2, 5.233, -1.872, 2, 5.267, -2.062, 2, 5.3, -1.927, 2, 5.333, -1.572, 2, 5.367, -1.07, 2, 5.4, -0.495, 2, 5.433, 0.081, 2, 5.467, 0.583, 2, 5.5, 0.938, 2, 5.533, 1.073, 2, 5.567, 1.018, 2, 5.6, 0.873, 2, 5.633, 0.663, 2, 5.667, 0.414, 2, 5.7, 0.156, 2, 5.733, -0.093, 2, 5.767, -0.303, 2, 5.8, -0.449, 2, 5.833, -0.503, 2, 5.867, -0.478, 2, 5.9, -0.412, 2, 5.933, -0.316, 2, 5.967, -0.202, 2, 6, -0.085, 2, 6.033, 0.029, 2, 6.067, 0.125, 2, 6.1, 0.191, 2, 6.133, 0.216, 2, 6.167, 0.205, 2, 6.2, 0.177, 2, 6.233, 0.137, 2, 6.267, 0.089, 2, 6.3, 0.04, 2, 6.333, -0.008, 2, 6.367, -0.048, 2, 6.4, -0.076, 2, 6.433, -0.086, 2, 6.467, -0.082, 2, 6.5, -0.071, 2, 6.533, -0.055, 2, 6.567, -0.036, 2, 6.6, -0.017, 2, 6.633, 0.002, 2, 6.667, 0.018, 2, 6.7, 0.029, 2, 6.733, 0.033, 2, 6.767, 0.031, 2, 6.8, 0.026, 2, 6.833, 0.019, 2, 6.867, 0.01, 2, 6.9, 0.002, 2, 6.933, -0.005, 2, 6.967, -0.01, 2, 7, -0.012]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh1", "Segments": [0, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh1", "Segments": [0, 0, 2, 0.033, 0.092, 2, 0.067, 0.314, 2, 0.1, 0.61, 2, 0.133, 0.906, 2, 0.167, 1.128, 2, 0.2, 1.22, 2, 0.233, 1.107, 2, 0.267, 0.8, 2, 0.3, 0.337, 2, 0.333, -0.215, 2, 0.367, -0.815, 2, 0.4, -1.414, 2, 0.433, -1.966, 2, 0.467, -2.429, 2, 0.5, -2.736, 2, 0.533, -2.849, 2, 0.567, -2.807, 2, 0.6, -2.688, 2, 0.633, -2.5, 2, 0.667, -2.257, 2, 0.7, -1.969, 2, 0.733, -1.639, 2, 0.767, -1.289, 2, 0.8, -0.923, 2, 0.833, -0.553, 2, 0.867, -0.187, 2, 0.9, 0.163, 2, 0.933, 0.493, 2, 0.967, 0.782, 2, 1, 1.024, 2, 1.033, 1.213, 2, 1.067, 1.331, 2, 1.1, 1.373, 2, 1.133, 1.32, 2, 1.167, 1.175, 2, 1.2, 0.957, 2, 1.233, 0.696, 2, 1.267, 0.413, 2, 1.3, 0.131, 2, 1.333, -0.13, 2, 1.367, -0.348, 2, 1.4, -0.493, 2, 1.433, -0.546, 2, 1.467, -0.516, 2, 1.5, -0.438, 2, 1.533, -0.327, 2, 1.567, -0.2, 2, 1.6, -0.073, 2, 1.633, 0.038, 2, 1.667, 0.116, 2, 1.7, 0.146, 2, 1.733, 0.139, 2, 1.767, 0.122, 2, 1.8, 0.098, 2, 1.833, 0.068, 2, 1.867, 0.038, 2, 1.9, 0.009, 2, 1.933, -0.016, 2, 1.967, -0.033, 2, 2, -0.039, 2, 2.033, 0.614, 2, 2.067, 2.053, 2, 2.1, 3.491, 2, 2.133, 4.145, 2, 2.167, 3.768, 2, 2.2, 2.759, 2, 2.233, 1.299, 2, 2.267, -0.431, 2, 2.3, -2.219, 2, 2.333, -3.95, 2, 2.367, -5.41, 2, 2.4, -6.419, 2, 2.433, -6.795, 2, 2.467, -6.484, 2, 2.5, -5.655, 2, 2.533, -4.391, 2, 2.567, -2.862, 2, 2.6, -1.122, 2, 2.633, 0.64, 2, 2.667, 2.38, 2, 2.7, 3.909, 2, 2.733, 5.173, 2, 2.767, 6.002, 2, 2.8, 6.314, 2, 2.833, 5.936, 2, 2.867, 4.911, 2, 2.9, 3.365, 2, 2.933, 1.523, 2, 2.967, -0.48, 2, 3, -2.483, 2, 3.033, -4.325, 2, 3.067, -5.87, 2, 3.1, -6.895, 2, 3.133, -7.273, 2, 3.167, -6.949, 2, 3.2, -6.071, 2, 3.233, -4.747, 2, 3.267, -3.17, 2, 3.3, -1.454, 2, 3.333, 0.262, 2, 3.367, 1.84, 2, 3.4, 3.164, 2, 3.433, 4.042, 2, 3.467, 4.365, 2, 3.5, 4.223, 2, 3.533, 3.84, 2, 3.567, 3.286, 2, 3.6, 2.629, 2, 3.633, 1.951, 2, 3.667, 1.294, 2, 3.7, 0.74, 2, 3.733, 0.358, 2, 3.767, 0.215, 2, 3.8, 0.405, 2, 3.833, 0.868, 2, 3.867, 1.483, 2, 3.9, 2.099, 2, 3.933, 2.561, 2, 3.967, 2.752, 2, 4, 2.606, 2, 4.033, 2.209, 2, 4.067, 1.61, 2, 4.1, 0.897, 2, 4.133, 0.121, 2, 4.167, -0.655, 2, 4.2, -1.368, 2, 4.233, -1.966, 2, 4.267, -2.363, 2, 4.3, -2.51, 2, 4.333, -2.392, 2, 4.367, -2.078, 2, 4.4, -1.623, 2, 4.433, -1.085, 2, 4.467, -0.528, 2, 4.5, 0.011, 2, 4.533, 0.466, 2, 4.567, 0.78, 2, 4.6, 0.897, 2, 4.633, 0.858, 2, 4.667, 0.753, 2, 4.7, 0.602, 2, 4.733, 0.422, 2, 4.767, 0.237, 2, 4.8, 0.057, 2, 4.833, -0.095, 2, 4.867, -0.199, 2, 4.9, -0.238, 2, 4.933, -0.225, 2, 4.967, -0.191, 2, 5, -0.143, 2, 5.033, -0.088, 2, 5.067, -0.032, 2, 5.1, 0.016, 2, 5.133, 0.05, 2, 5.167, 0.063, 2, 5.2, 0.061, 2, 5.233, 0.053, 2, 5.267, 0.042, 2, 5.3, 0.03, 2, 5.333, 0.017, 2, 5.367, 0.004, 2, 5.4, -0.007, 2, 5.433, -0.014, 2, 5.467, -0.017, 2, 5.5, -0.016, 2, 5.533, -0.014, 2, 5.567, -0.011, 2, 5.6, -0.008, 2, 5.633, -0.004, 2, 5.667, -0.001, 2, 5.7, 0.002, 2, 5.733, 0.004, 2, 5.767, 0.005, 2, 5.8, 0.004, 2, 5.833, 0.004, 2, 5.867, 0.003, 2, 5.9, 0.002, 2, 5.933, 0.001, 2, 5.967, 0, 2, 6, 0, 2, 6.033, -0.001, 2, 6.067, -0.001, 2, 6.133, -0.001, 2, 6.167, -0.001, 2, 6.2, -0.001, 2, 6.233, 0, 2, 6.267, 0, 2, 6.3, 0, 2, 6.333, 0, 2, 6.433, 0, 2, 6.5, 0, 2, 6.533, 0, 2, 6.6, 0, 2, 6.667, 0, 2, 6.7, 0, 2, 6.767, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh1", "Segments": [0, 0, 2, 0.033, -0.095, 2, 0.067, -0.303, 2, 0.1, -0.512, 2, 0.133, -0.607, 2, 0.167, -0.512, 2, 0.2, -0.263, 2, 0.233, 0.089, 2, 0.267, 0.493, 2, 0.3, 0.897, 2, 0.333, 1.249, 2, 0.367, 1.498, 2, 0.4, 1.593, 2, 0.433, 1.47, 2, 0.467, 1.145, 2, 0.5, 0.686, 2, 0.533, 0.16, 2, 0.567, -0.367, 2, 0.6, -0.826, 2, 0.633, -1.151, 2, 0.667, -1.274, 2, 0.7, -1.234, 2, 0.733, -1.127, 2, 0.767, -0.972, 2, 0.8, -0.788, 2, 0.833, -0.598, 2, 0.867, -0.415, 2, 0.9, -0.26, 2, 0.933, -0.153, 2, 0.967, -0.113, 2, 1, -0.153, 2, 1.033, -0.193, 2, 1.067, -0.137, 2, 1.1, 0.006, 2, 1.133, 0.205, 2, 1.167, 0.415, 2, 1.2, 0.614, 2, 1.233, 0.757, 2, 1.267, 0.812, 2, 1.3, 0.761, 2, 1.333, 0.624, 2, 1.367, 0.426, 2, 1.4, 0.191, 2, 1.433, -0.052, 2, 1.467, -0.287, 2, 1.5, -0.485, 2, 1.533, -0.622, 2, 1.567, -0.673, 2, 1.6, -0.63, 2, 1.633, -0.518, 2, 1.667, -0.359, 2, 1.7, -0.176, 2, 1.733, 0.006, 2, 1.767, 0.165, 2, 1.8, 0.278, 2, 1.833, 0.321, 2, 1.867, 0.226, 2, 1.9, -0.029, 2, 1.933, -0.397, 2, 1.967, -0.833, 2, 2, -1.284, 2, 2.033, -1.72, 2, 2.067, -2.088, 2, 2.1, -2.342, 2, 2.133, -2.437, 2, 2.167, -1.679, 2, 2.2, 0.131, 2, 2.233, 2.337, 2, 2.267, 4.147, 2, 2.3, 4.905, 2, 2.333, 4.521, 2, 2.367, 3.494, 2, 2.4, 2.007, 2, 2.433, 0.244, 2, 2.467, -1.576, 2, 2.5, -3.338, 2, 2.533, -4.825, 2, 2.567, -5.853, 2, 2.6, -6.236, 2, 2.633, -5.918, 2, 2.667, -5.052, 2, 2.7, -3.748, 2, 2.733, -2.193, 2, 2.767, -0.502, 2, 2.8, 1.189, 2, 2.833, 2.743, 2, 2.867, 4.048, 2, 2.9, 4.913, 2, 2.933, 5.232, 2, 2.967, 4.895, 2, 3, 3.981, 2, 3.033, 2.602, 2, 3.067, 0.96, 2, 3.1, -0.827, 2, 3.133, -2.614, 2, 3.167, -4.257, 2, 3.2, -5.635, 2, 3.233, -6.549, 2, 3.267, -6.886, 2, 3.3, -6.609, 2, 3.333, -5.856, 2, 3.367, -4.72, 2, 3.4, -3.367, 2, 3.433, -1.895, 2, 3.467, -0.424, 2, 3.5, 0.929, 2, 3.533, 2.065, 2, 3.567, 2.818, 2, 3.6, 3.095, 2, 3.633, 2.936, 2, 3.667, 2.503, 2, 3.7, 1.852, 2, 3.733, 1.074, 2, 3.767, 0.229, 2, 3.8, -0.616, 2, 3.833, -1.393, 2, 3.867, -2.045, 2, 3.9, -2.477, 2, 3.933, -2.636, 2, 3.967, -2.417, 2, 4, -1.838, 2, 4.033, -1.019, 2, 4.067, -0.08, 2, 4.1, 0.859, 2, 4.133, 1.678, 2, 4.167, 2.257, 2, 4.2, 2.477, 2, 4.233, 2.259, 2, 4.267, 1.687, 2, 4.3, 0.877, 2, 4.333, -0.052, 2, 4.367, -0.98, 2, 4.4, -1.79, 2, 4.433, -2.363, 2, 4.467, -2.58, 2, 4.5, -2.407, 2, 4.533, -1.95, 2, 4.567, -1.304, 2, 4.6, -0.564, 2, 4.633, 0.176, 2, 4.667, 0.821, 2, 4.7, 1.278, 2, 4.733, 1.451, 2, 4.767, 1.38, 2, 4.8, 1.191, 2, 4.833, 0.916, 2, 4.867, 0.59, 2, 4.9, 0.254, 2, 4.933, -0.072, 2, 4.967, -0.346, 2, 5, -0.536, 2, 5.033, -0.607, 2, 5.067, -0.571, 2, 5.1, -0.477, 2, 5.133, -0.345, 2, 5.167, -0.192, 2, 5.2, -0.04, 2, 5.233, 0.093, 2, 5.267, 0.187, 2, 5.3, 0.222, 2, 5.333, 0.212, 2, 5.367, 0.184, 2, 5.4, 0.144, 2, 5.433, 0.097, 2, 5.467, 0.048, 2, 5.5, 0.001, 2, 5.533, -0.039, 2, 5.567, -0.066, 2, 5.6, -0.076, 2, 5.633, -0.073, 2, 5.667, -0.064, 2, 5.7, -0.05, 2, 5.733, -0.034, 2, 5.767, -0.018, 2, 5.8, -0.001, 2, 5.833, 0.012, 2, 5.867, 0.021, 2, 5.9, 0.025, 2, 5.933, 0.024, 2, 5.967, 0.021, 2, 6, 0.016, 2, 6.033, 0.011, 2, 6.067, 0.006, 2, 6.1, 0.001, 2, 6.133, -0.004, 2, 6.167, -0.007, 2, 6.2, -0.008, 2, 6.233, -0.007, 2, 6.267, -0.006, 2, 6.3, -0.005, 2, 6.333, -0.003, 2, 6.367, -0.001, 2, 6.4, 0.001, 2, 6.433, 0.002, 2, 6.467, 0.002, 2, 6.533, 0.002, 2, 6.567, 0.002, 2, 6.6, 0.001, 2, 6.633, 0.001, 2, 6.667, 0, 2, 6.7, 0, 2, 6.733, -0.001, 2, 6.767, -0.001, 2, 6.833, -0.001, 2, 6.867, -0.001, 2, 6.9, 0, 2, 6.933, 0, 2, 6.967, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh1", "Segments": [0, 0, 2, 0.033, -0.027, 2, 0.067, -0.097, 2, 0.1, -0.195, 2, 0.133, -0.298, 2, 0.167, -0.396, 2, 0.2, -0.466, 2, 0.233, -0.493, 2, 0.267, -0.398, 2, 0.3, -0.148, 2, 0.333, 0.207, 2, 0.367, 0.613, 2, 0.4, 1.019, 2, 0.433, 1.373, 2, 0.467, 1.623, 2, 0.5, 1.718, 2, 0.533, 1.597, 2, 0.567, 1.272, 2, 0.6, 0.801, 2, 0.633, 0.243, 2, 0.667, -0.333, 2, 0.7, -0.891, 2, 0.733, -1.361, 2, 0.767, -1.687, 2, 0.8, -1.808, 2, 0.833, -1.779, 2, 0.867, -1.696, 2, 0.9, -1.567, 2, 0.933, -1.399, 2, 0.967, -1.201, 2, 1, -0.98, 2, 1.033, -0.743, 2, 1.067, -0.499, 2, 1.1, -0.255, 2, 1.133, -0.019, 2, 1.167, 0.202, 2, 1.2, 0.401, 2, 1.233, 0.568, 2, 1.267, 0.697, 2, 1.3, 0.78, 2, 1.333, 0.809, 2, 1.367, 0.762, 2, 1.4, 0.635, 2, 1.433, 0.442, 2, 1.467, 0.212, 2, 1.5, -0.037, 2, 1.533, -0.287, 2, 1.567, -0.517, 2, 1.6, -0.709, 2, 1.633, -0.837, 2, 1.667, -0.884, 2, 1.7, -0.835, 2, 1.733, -0.703, 2, 1.767, -0.512, 2, 1.8, -0.286, 2, 1.833, -0.052, 2, 1.867, 0.175, 2, 1.9, 0.366, 2, 1.933, 0.498, 2, 1.967, 0.547, 2, 2, 0.408, 2, 2.033, 0.048, 2, 2.067, -0.451, 2, 2.1, -0.981, 2, 2.133, -1.48, 2, 2.167, -1.84, 2, 2.2, -1.979, 2, 2.233, -1.467, 2, 2.267, -0.226, 2, 2.3, 1.425, 2, 2.333, 3.075, 2, 2.367, 4.317, 2, 2.4, 4.828, 2, 2.433, 4.416, 2, 2.467, 3.312, 2, 2.5, 1.715, 2, 2.533, -0.179, 2, 2.567, -2.135, 2, 2.6, -4.028, 2, 2.633, -5.626, 2, 2.667, -6.73, 2, 2.7, -7.142, 2, 2.733, -6.75, 2, 2.767, -5.684, 2, 2.8, -4.078, 2, 2.833, -2.164, 2, 2.867, -0.082, 2, 2.9, 2, 2, 2.933, 3.915, 2, 2.967, 5.521, 2, 3, 6.586, 2, 3.033, 6.979, 2, 3.067, 6.565, 2, 3.1, 5.44, 2, 3.133, 3.745, 2, 3.167, 1.724, 2, 3.2, -0.473, 2, 3.233, -2.671, 2, 3.267, -4.691, 2, 3.3, -6.386, 2, 3.333, -7.511, 2, 3.367, -7.925, 2, 3.4, -7.558, 2, 3.433, -6.561, 2, 3.467, -5.059, 2, 3.5, -3.268, 2, 3.533, -1.321, 2, 3.567, 0.627, 2, 3.6, 2.418, 2, 3.633, 3.92, 2, 3.667, 4.917, 2, 3.7, 5.284, 2, 3.733, 4.972, 2, 3.767, 4.137, 2, 3.8, 2.928, 2, 3.833, 1.496, 2, 3.867, 0.017, 2, 3.9, -1.415, 2, 3.933, -2.624, 2, 3.967, -3.459, 2, 4, -3.771, 2, 4.033, -3.517, 2, 4.067, -2.837, 2, 4.1, -1.853, 2, 4.133, -0.687, 2, 4.167, 0.518, 2, 4.2, 1.684, 2, 4.233, 2.668, 2, 4.267, 3.348, 2, 4.3, 3.602, 2, 4.333, 3.301, 2, 4.367, 2.509, 2, 4.4, 1.389, 2, 4.433, 0.105, 2, 4.467, -1.18, 2, 4.5, -2.3, 2, 4.533, -3.092, 2, 4.567, -3.393, 2, 4.6, -3.198, 2, 4.633, -2.676, 2, 4.667, -1.92, 2, 4.7, -1.024, 2, 4.733, -0.099, 2, 4.767, 0.797, 2, 4.8, 1.553, 2, 4.833, 2.075, 2, 4.867, 2.27, 2, 4.9, 2.122, 2, 4.933, 1.729, 2, 4.967, 1.175, 2, 5, 0.54, 2, 5.033, -0.096, 2, 5.067, -0.65, 2, 5.1, -1.042, 2, 5.133, -1.191, 2, 5.167, -1.131, 2, 5.2, -0.972, 2, 5.233, -0.741, 2, 5.267, -0.468, 2, 5.3, -0.186, 2, 5.333, 0.088, 2, 5.367, 0.319, 2, 5.4, 0.478, 2, 5.433, 0.537, 2, 5.467, 0.511, 2, 5.5, 0.442, 2, 5.533, 0.341, 2, 5.567, 0.222, 2, 5.6, 0.099, 2, 5.633, -0.021, 2, 5.667, -0.121, 2, 5.7, -0.191, 2, 5.733, -0.217, 2, 5.767, -0.204, 2, 5.8, -0.17, 2, 5.833, -0.123, 2, 5.867, -0.068, 2, 5.9, -0.013, 2, 5.933, 0.035, 2, 5.967, 0.068, 2, 6, 0.081, 2, 6.033, 0.077, 2, 6.067, 0.067, 2, 6.1, 0.052, 2, 6.133, 0.035, 2, 6.167, 0.017, 2, 6.2, -0.001, 2, 6.233, -0.016, 2, 6.267, -0.026, 2, 6.3, -0.03, 2, 6.333, -0.028, 2, 6.367, -0.025, 2, 6.4, -0.019, 2, 6.433, -0.013, 2, 6.467, -0.006, 2, 6.5, 0, 2, 6.533, 0.005, 2, 6.567, 0.009, 2, 6.6, 0.01, 2, 6.633, 0.01, 2, 6.667, 0.009, 2, 6.7, 0.007, 2, 6.733, 0.005, 2, 6.767, 0.002, 2, 6.8, 0, 2, 6.833, -0.002, 2, 6.867, -0.003, 2, 6.9, -0.004, 2, 6.933, -0.003, 2, 6.967, -0.003, 2, 7, -0.002]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh1", "Segments": [0, -0.003, 2, 0.033, -0.019, 2, 0.067, -0.061, 2, 0.1, -0.122, 2, 0.133, -0.194, 2, 0.167, -0.268, 2, 0.2, -0.34, 2, 0.233, -0.401, 2, 0.267, -0.443, 2, 0.3, -0.459, 2, 0.333, -0.358, 2, 0.367, -0.093, 2, 0.4, 0.283, 2, 0.433, 0.714, 2, 0.467, 1.144, 2, 0.5, 1.52, 2, 0.533, 1.786, 2, 0.567, 1.886, 2, 0.6, 1.767, 2, 0.633, 1.443, 2, 0.667, 0.954, 2, 0.7, 0.372, 2, 0.733, -0.262, 2, 0.767, -0.895, 2, 0.8, -1.477, 2, 0.833, -1.966, 2, 0.867, -2.29, 2, 0.9, -2.409, 2, 0.933, -2.314, 2, 0.967, -2.055, 2, 1, -1.664, 2, 1.033, -1.198, 2, 1.067, -0.692, 2, 1.1, -0.185, 2, 1.133, 0.281, 2, 1.167, 0.672, 2, 1.2, 0.931, 2, 1.233, 1.026, 2, 1.267, 1, 2, 1.3, 0.924, 2, 1.333, 0.806, 2, 1.367, 0.656, 2, 1.4, 0.477, 2, 1.433, 0.282, 2, 1.467, 0.075, 2, 1.5, -0.135, 2, 1.533, -0.341, 2, 1.567, -0.536, 2, 1.6, -0.715, 2, 1.633, -0.866, 2, 1.667, -0.984, 2, 1.7, -1.059, 2, 1.733, -1.086, 2, 1.767, -1.02, 2, 1.8, -0.843, 2, 1.833, -0.588, 2, 1.867, -0.286, 2, 1.9, 0.027, 2, 1.933, 0.33, 2, 1.967, 0.585, 2, 2, 0.762, 2, 2.033, 0.827, 2, 2.067, 0.686, 2, 2.1, 0.32, 2, 2.133, -0.187, 2, 2.167, -0.725, 2, 2.2, -1.232, 2, 2.233, -1.597, 2, 2.267, -1.739, 2, 2.3, -1.359, 2, 2.333, -0.38, 2, 2.367, 0.98, 2, 2.4, 2.422, 2, 2.433, 3.782, 2, 2.467, 4.761, 2, 2.5, 5.141, 2, 2.533, 4.564, 2, 2.567, 3.044, 2, 2.6, 0.895, 2, 2.633, -1.57, 2, 2.667, -4.034, 2, 2.7, -6.183, 2, 2.733, -7.704, 2, 2.767, -8.28, 2, 2.8, -7.802, 2, 2.833, -6.505, 2, 2.867, -4.549, 2, 2.9, -2.218, 2, 2.933, 0.318, 2, 2.967, 2.853, 2, 3, 5.184, 2, 3.033, 7.14, 2, 3.067, 8.438, 2, 3.1, 8.915, 2, 3.133, 8.404, 2, 3.167, 7.013, 2, 3.2, 4.918, 2, 3.233, 2.421, 2, 3.267, -0.295, 2, 3.3, -3.011, 2, 3.333, -5.509, 2, 3.367, -7.604, 2, 3.4, -8.994, 2, 3.433, -9.506, 2, 3.467, -9.035, 2, 3.5, -7.758, 2, 3.533, -5.832, 2, 3.567, -3.537, 2, 3.6, -1.041, 2, 3.633, 1.455, 2, 3.667, 3.751, 2, 3.7, 5.676, 2, 3.733, 6.954, 2, 3.767, 7.424, 2, 3.8, 7.066, 2, 3.833, 6.092, 2, 3.867, 4.625, 2, 3.9, 2.876, 2, 3.933, 0.975, 2, 3.967, -0.927, 2, 4, -2.676, 2, 4.033, -4.143, 2, 4.067, -5.117, 2, 4.1, -5.475, 2, 4.133, -5.115, 2, 4.167, -4.151, 2, 4.2, -2.755, 2, 4.233, -1.101, 2, 4.267, 0.608, 2, 4.3, 2.262, 2, 4.333, 3.658, 2, 4.367, 4.623, 2, 4.4, 4.983, 2, 4.433, 4.571, 2, 4.467, 3.485, 2, 4.5, 1.95, 2, 4.533, 0.191, 2, 4.567, -1.569, 2, 4.6, -3.104, 2, 4.633, -4.189, 2, 4.667, -4.601, 2, 4.7, -4.324, 2, 4.733, -3.583, 2, 4.767, -2.509, 2, 4.8, -1.237, 2, 4.833, 0.077, 2, 4.867, 1.349, 2, 4.9, 2.422, 2, 4.933, 3.164, 2, 4.967, 3.44, 2, 5, 3.251, 2, 5.033, 2.743, 2, 5.067, 2.009, 2, 5.1, 1.139, 2, 5.133, 0.24, 2, 5.167, -0.631, 2, 5.2, -1.365, 2, 5.233, -1.872, 2, 5.267, -2.062, 2, 5.3, -1.927, 2, 5.333, -1.572, 2, 5.367, -1.07, 2, 5.4, -0.495, 2, 5.433, 0.081, 2, 5.467, 0.583, 2, 5.5, 0.938, 2, 5.533, 1.073, 2, 5.567, 1.018, 2, 5.6, 0.873, 2, 5.633, 0.663, 2, 5.667, 0.414, 2, 5.7, 0.156, 2, 5.733, -0.093, 2, 5.767, -0.303, 2, 5.8, -0.449, 2, 5.833, -0.503, 2, 5.867, -0.478, 2, 5.9, -0.412, 2, 5.933, -0.316, 2, 5.967, -0.202, 2, 6, -0.085, 2, 6.033, 0.029, 2, 6.067, 0.125, 2, 6.1, 0.191, 2, 6.133, 0.216, 2, 6.167, 0.205, 2, 6.2, 0.177, 2, 6.233, 0.137, 2, 6.267, 0.089, 2, 6.3, 0.04, 2, 6.333, -0.008, 2, 6.367, -0.048, 2, 6.4, -0.076, 2, 6.433, -0.086, 2, 6.467, -0.082, 2, 6.5, -0.071, 2, 6.533, -0.055, 2, 6.567, -0.036, 2, 6.6, -0.017, 2, 6.633, 0.002, 2, 6.667, 0.018, 2, 6.7, 0.029, 2, 6.733, 0.033, 2, 6.767, 0.031, 2, 6.8, 0.026, 2, 6.833, 0.019, 2, 6.867, 0.01, 2, 6.9, 0.002, 2, 6.933, -0.005, 2, 6.967, -0.01, 2, 7, -0.012]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh2", "Segments": [0, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh2", "Segments": [0, 0, 2, 0.033, 0.092, 2, 0.067, 0.315, 2, 0.1, 0.611, 2, 0.133, 0.907, 2, 0.167, 1.13, 2, 0.2, 1.221, 2, 0.233, 1.134, 2, 0.267, 0.9, 2, 0.3, 0.538, 2, 0.333, 0.084, 2, 0.367, -0.42, 2, 0.4, -0.966, 2, 0.433, -1.512, 2, 0.467, -2.015, 2, 0.5, -2.469, 2, 0.533, -2.831, 2, 0.567, -3.065, 2, 0.6, -3.153, 2, 0.633, -3.093, 2, 0.667, -2.925, 2, 0.7, -2.661, 2, 0.733, -2.326, 2, 0.767, -1.925, 2, 0.8, -1.489, 2, 0.833, -1.028, 2, 0.867, -0.559, 2, 0.9, -0.098, 2, 0.933, 0.338, 2, 0.967, 0.738, 2, 1, 1.074, 2, 1.033, 1.338, 2, 1.067, 1.505, 2, 1.1, 1.565, 2, 1.133, 1.514, 2, 1.167, 1.378, 2, 1.2, 1.17, 2, 1.233, 0.919, 2, 1.267, 0.633, 2, 1.3, 0.344, 2, 1.333, 0.058, 2, 1.367, -0.193, 2, 1.4, -0.401, 2, 1.433, -0.537, 2, 1.467, -0.588, 2, 1.5, -0.568, 2, 1.533, -0.512, 2, 1.567, -0.428, 2, 1.6, -0.327, 2, 1.633, -0.218, 2, 1.667, -0.109, 2, 1.7, -0.009, 2, 1.733, 0.076, 2, 1.767, 0.132, 2, 1.8, 0.152, 2, 1.833, 0.141, 2, 1.867, 0.116, 2, 1.9, 0.081, 2, 1.933, 0.047, 2, 1.967, 0.021, 2, 2, 0.01, 2, 2.033, 0.427, 2, 2.067, 1.421, 2, 2.1, 2.633, 2, 2.133, 3.627, 2, 2.167, 4.043, 2, 2.2, 3.571, 2, 2.233, 2.325, 2, 2.267, 0.565, 2, 2.3, -1.454, 2, 2.333, -3.473, 2, 2.367, -5.233, 2, 2.4, -6.479, 2, 2.433, -6.951, 2, 2.467, -6.667, 2, 2.5, -5.904, 2, 2.533, -4.726, 2, 2.567, -3.247, 2, 2.6, -1.608, 2, 2.633, 0.169, 2, 2.667, 1.946, 2, 2.7, 3.585, 2, 2.733, 5.064, 2, 2.767, 6.242, 2, 2.8, 7.005, 2, 2.833, 7.289, 2, 2.867, 6.87, 2, 2.9, 5.734, 2, 2.933, 4.02, 2, 2.967, 1.978, 2, 3, -0.243, 2, 3.033, -2.464, 2, 3.067, -4.507, 2, 3.1, -6.22, 2, 3.133, -7.357, 2, 3.167, -7.776, 2, 3.2, -7.467, 2, 3.233, -6.642, 2, 3.267, -5.386, 2, 3.3, -3.866, 2, 3.333, -2.136, 2, 3.367, -0.385, 2, 3.4, 1.345, 2, 3.433, 2.865, 2, 3.467, 4.121, 2, 3.5, 4.945, 2, 3.533, 5.255, 2, 3.567, 5.09, 2, 3.6, 4.648, 2, 3.633, 4.008, 2, 3.667, 3.25, 2, 3.7, 2.467, 2, 3.733, 1.709, 2, 3.767, 1.07, 2, 3.8, 0.628, 2, 3.833, 0.463, 2, 3.867, 0.659, 2, 3.9, 1.128, 2, 3.933, 1.7, 2, 3.967, 2.169, 2, 4, 2.365, 2, 4.033, 2.234, 2, 4.067, 1.877, 2, 4.1, 1.34, 2, 4.133, 0.699, 2, 4.167, 0.002, 2, 4.2, -0.695, 2, 4.233, -1.336, 2, 4.267, -1.873, 2, 4.3, -2.23, 2, 4.333, -2.361, 2, 4.367, -2.274, 2, 4.4, -2.039, 2, 4.433, -1.684, 2, 4.467, -1.261, 2, 4.5, -0.801, 2, 4.533, -0.341, 2, 4.567, 0.081, 2, 4.6, 0.436, 2, 4.633, 0.672, 2, 4.667, 0.758, 2, 4.7, 0.732, 2, 4.733, 0.66, 2, 4.767, 0.551, 2, 4.8, 0.422, 2, 4.833, 0.281, 2, 4.867, 0.14, 2, 4.9, 0.011, 2, 4.933, -0.098, 2, 4.967, -0.17, 2, 5, -0.197, 2, 5.033, -0.19, 2, 5.067, -0.171, 2, 5.1, -0.143, 2, 5.133, -0.109, 2, 5.167, -0.073, 2, 5.2, -0.036, 2, 5.233, -0.003, 2, 5.267, 0.025, 2, 5.3, 0.044, 2, 5.333, 0.051, 2, 5.367, 0.049, 2, 5.4, 0.044, 2, 5.433, 0.037, 2, 5.467, 0.028, 2, 5.5, 0.019, 2, 5.533, 0.009, 2, 5.567, 0.001, 2, 5.6, -0.006, 2, 5.633, -0.011, 2, 5.667, -0.013, 2, 5.7, -0.013, 2, 5.733, -0.011, 2, 5.767, -0.009, 2, 5.8, -0.007, 2, 5.833, -0.005, 2, 5.867, -0.002, 2, 5.9, 0, 2, 5.933, 0.002, 2, 5.967, 0.003, 2, 6, 0.003, 2, 6.067, 0.003, 2, 6.1, 0.003, 2, 6.133, 0.002, 2, 6.167, 0.002, 2, 6.2, 0.001, 2, 6.233, 0.001, 2, 6.267, 0, 2, 6.3, 0, 2, 6.333, -0.001, 2, 6.367, -0.001, 2, 6.4, -0.001, 2, 6.433, -0.001, 2, 6.467, -0.001, 2, 6.5, 0, 2, 6.533, 0, 2, 6.567, 0, 2, 6.6, 0, 2, 6.633, 0, 2, 6.7, 0, 2, 6.733, 0, 2, 6.8, 0, 2, 6.833, 0, 2, 6.867, 0, 2, 6.9, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh2", "Segments": [0, 0, 2, 0.033, -0.067, 2, 0.067, -0.228, 2, 0.1, -0.424, 2, 0.133, -0.585, 2, 0.167, -0.653, 2, 0.2, -0.544, 2, 0.233, -0.258, 2, 0.267, 0.146, 2, 0.3, 0.61, 2, 0.333, 1.073, 2, 0.367, 1.478, 2, 0.4, 1.764, 2, 0.433, 1.872, 2, 0.467, 1.776, 2, 0.5, 1.515, 2, 0.533, 1.121, 2, 0.567, 0.651, 2, 0.6, 0.141, 2, 0.633, -0.37, 2, 0.667, -0.839, 2, 0.7, -1.233, 2, 0.733, -1.494, 2, 0.767, -1.591, 2, 0.8, -1.559, 2, 0.833, -1.47, 2, 0.867, -1.332, 2, 0.9, -1.152, 2, 0.933, -0.939, 2, 0.967, -0.702, 2, 1, -0.448, 2, 1.033, -0.187, 2, 1.067, 0.075, 2, 1.1, 0.329, 2, 1.133, 0.566, 2, 1.167, 0.779, 2, 1.2, 0.958, 2, 1.233, 1.097, 2, 1.267, 1.186, 2, 1.3, 1.218, 2, 1.333, 1.161, 2, 1.367, 1.006, 2, 1.4, 0.773, 2, 1.433, 0.496, 2, 1.467, 0.194, 2, 1.5, -0.107, 2, 1.533, -0.385, 2, 1.567, -0.618, 2, 1.6, -0.772, 2, 1.633, -0.829, 2, 1.667, -0.796, 2, 1.7, -0.707, 2, 1.733, -0.572, 2, 1.767, -0.412, 2, 1.8, -0.237, 2, 1.833, -0.062, 2, 1.867, 0.098, 2, 1.9, 0.233, 2, 1.933, 0.322, 2, 1.967, 0.355, 2, 2, 0.057, 2, 2.033, -0.653, 2, 2.067, -1.52, 2, 2.1, -2.23, 2, 2.133, -2.528, 2, 2.167, -2.108, 2, 2.2, -1.023, 2, 2.233, 0.484, 2, 2.267, 2.08, 2, 2.3, 3.587, 2, 2.333, 4.672, 2, 2.367, 5.092, 2, 2.4, 4.575, 2, 2.433, 3.213, 2, 2.467, 1.286, 2, 2.5, -0.923, 2, 2.533, -3.131, 2, 2.567, -5.058, 2, 2.6, -6.42, 2, 2.633, -6.937, 2, 2.667, -6.652, 2, 2.7, -5.887, 2, 2.733, -4.706, 2, 2.767, -3.222, 2, 2.8, -1.578, 2, 2.833, 0.204, 2, 2.867, 1.986, 2, 2.9, 3.63, 2, 2.933, 5.113, 2, 2.967, 6.295, 2, 3, 7.06, 2, 3.033, 7.345, 2, 3.067, 6.817, 2, 3.1, 5.401, 2, 3.133, 3.352, 2, 3.167, 0.924, 2, 3.2, -1.585, 2, 3.233, -4.013, 2, 3.267, -6.062, 2, 3.3, -7.478, 2, 3.333, -8.006, 2, 3.367, -7.701, 2, 3.4, -6.887, 2, 3.433, -5.646, 2, 3.467, -4.145, 2, 3.5, -2.436, 2, 3.533, -0.706, 2, 3.567, 1.002, 2, 3.6, 2.503, 2, 3.633, 3.744, 2, 3.667, 4.558, 2, 3.7, 4.864, 2, 3.733, 4.532, 2, 3.767, 3.657, 2, 3.8, 2.421, 2, 3.833, 1.003, 2, 3.867, -0.415, 2, 3.9, -1.651, 2, 3.933, -2.526, 2, 3.967, -2.858, 2, 4, -2.662, 2, 4.033, -2.136, 2, 4.067, -1.376, 2, 4.1, -0.474, 2, 4.133, 0.457, 2, 4.167, 1.358, 2, 4.2, 2.119, 2, 4.233, 2.645, 2, 4.267, 2.841, 2, 4.3, 2.616, 2, 4.333, 2.024, 2, 4.367, 1.187, 2, 4.4, 0.227, 2, 4.433, -0.733, 2, 4.467, -1.57, 2, 4.5, -2.163, 2, 4.533, -2.387, 2, 4.567, -2.263, 2, 4.6, -1.93, 2, 4.633, -1.449, 2, 4.667, -0.878, 2, 4.7, -0.289, 2, 4.733, 0.282, 2, 4.767, 0.763, 2, 4.8, 1.096, 2, 4.833, 1.22, 2, 4.867, 1.173, 2, 4.9, 1.044, 2, 4.933, 0.849, 2, 4.967, 0.617, 2, 5, 0.365, 2, 5.033, 0.112, 2, 5.067, -0.12, 2, 5.1, -0.314, 2, 5.133, -0.443, 2, 5.167, -0.491, 2, 5.2, -0.472, 2, 5.233, -0.422, 2, 5.267, -0.347, 2, 5.3, -0.257, 2, 5.333, -0.159, 2, 5.367, -0.061, 2, 5.4, 0.029, 2, 5.433, 0.105, 2, 5.467, 0.155, 2, 5.5, 0.173, 2, 5.533, 0.167, 2, 5.567, 0.15, 2, 5.6, 0.123, 2, 5.633, 0.092, 2, 5.667, 0.058, 2, 5.7, 0.024, 2, 5.733, -0.007, 2, 5.767, -0.033, 2, 5.8, -0.051, 2, 5.833, -0.057, 2, 5.867, -0.055, 2, 5.9, -0.049, 2, 5.933, -0.041, 2, 5.967, -0.031, 2, 6, -0.02, 2, 6.033, -0.009, 2, 6.067, 0.002, 2, 6.1, 0.01, 2, 6.133, 0.016, 2, 6.167, 0.018, 2, 6.2, 0.017, 2, 6.233, 0.015, 2, 6.267, 0.013, 2, 6.3, 0.01, 2, 6.333, 0.006, 2, 6.367, 0.003, 2, 6.4, 0, 2, 6.433, -0.003, 2, 6.467, -0.005, 2, 6.5, -0.005, 2, 6.533, -0.005, 2, 6.567, -0.005, 2, 6.6, -0.004, 2, 6.633, -0.003, 2, 6.667, -0.002, 2, 6.7, -0.001, 2, 6.733, 0, 2, 6.767, 0.001, 2, 6.8, 0.001, 2, 6.833, 0.002, 2, 6.9, 0.002, 2, 6.933, 0.001, 2, 6.967, 0.001, 2, 7, 0.001]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh2", "Segments": [0, 0.002, 2, 0.033, -0.027, 2, 0.067, -0.101, 2, 0.1, -0.205, 2, 0.133, -0.315, 2, 0.167, -0.418, 2, 0.2, -0.493, 2, 0.233, -0.521, 2, 0.267, -0.437, 2, 0.3, -0.21, 2, 0.333, 0.118, 2, 0.367, 0.507, 2, 0.4, 0.908, 2, 0.433, 1.297, 2, 0.467, 1.625, 2, 0.5, 1.852, 2, 0.533, 1.937, 2, 0.567, 1.838, 2, 0.6, 1.574, 2, 0.633, 1.173, 2, 0.667, 0.687, 2, 0.7, 0.134, 2, 0.733, -0.426, 2, 0.767, -0.979, 2, 0.8, -1.464, 2, 0.833, -1.866, 2, 0.867, -2.129, 2, 0.9, -2.228, 2, 0.933, -2.173, 2, 0.967, -2.017, 2, 1, -1.773, 2, 1.033, -1.471, 2, 1.067, -1.109, 2, 1.1, -0.719, 2, 1.133, -0.311, 2, 1.167, 0.096, 2, 1.2, 0.486, 2, 1.233, 0.848, 2, 1.267, 1.151, 2, 1.3, 1.394, 2, 1.333, 1.55, 2, 1.367, 1.605, 2, 1.4, 1.538, 2, 1.433, 1.359, 2, 1.467, 1.087, 2, 1.5, 0.757, 2, 1.533, 0.382, 2, 1.567, 0.002, 2, 1.6, -0.374, 2, 1.633, -0.703, 2, 1.667, -0.976, 2, 1.7, -1.155, 2, 1.733, -1.222, 2, 1.767, -1.161, 2, 1.8, -0.997, 2, 1.833, -0.76, 2, 1.867, -0.479, 2, 1.9, -0.188, 2, 1.933, 0.093, 2, 1.967, 0.33, 2, 2, 0.494, 2, 2.033, 0.555, 2, 2.067, 0.406, 2, 2.1, 0.044, 2, 2.133, -0.438, 2, 2.167, -0.92, 2, 2.2, -1.282, 2, 2.233, -1.432, 2, 2.267, -1.08, 2, 2.3, -0.172, 2, 2.333, 1.088, 2, 2.367, 2.425, 2, 2.4, 3.685, 2, 2.433, 4.593, 2, 2.467, 4.945, 2, 2.5, 4.504, 2, 2.533, 3.325, 2, 2.567, 1.617, 2, 2.6, -0.406, 2, 2.633, -2.496, 2, 2.667, -4.519, 2, 2.7, -6.227, 2, 2.733, -7.406, 2, 2.767, -7.847, 2, 2.8, -7.438, 2, 2.833, -6.348, 2, 2.867, -4.686, 2, 2.9, -2.676, 2, 2.933, -0.388, 2, 2.967, 1.928, 2, 3, 4.217, 2, 3.033, 6.226, 2, 3.067, 7.888, 2, 3.1, 8.978, 2, 3.133, 9.387, 2, 3.167, 8.852, 2, 3.2, 7.398, 2, 3.233, 5.208, 2, 3.267, 2.596, 2, 3.3, -0.244, 2, 3.333, -3.085, 2, 3.367, -5.696, 2, 3.4, -7.887, 2, 3.433, -9.341, 2, 3.467, -9.876, 2, 3.5, -9.405, 2, 3.533, -8.124, 2, 3.567, -6.195, 2, 3.6, -3.894, 2, 3.633, -1.393, 2, 3.667, 1.109, 2, 3.7, 3.409, 2, 3.733, 5.339, 2, 3.767, 6.619, 2, 3.8, 7.09, 2, 3.833, 6.781, 2, 3.867, 5.942, 2, 3.9, 4.676, 2, 3.933, 3.168, 2, 3.967, 1.528, 2, 4, -0.112, 2, 4.033, -1.621, 2, 4.067, -2.886, 2, 4.1, -3.726, 2, 4.133, -4.035, 2, 4.167, -3.724, 2, 4.2, -2.906, 2, 4.233, -1.748, 2, 4.267, -0.422, 2, 4.3, 0.905, 2, 4.333, 2.062, 2, 4.367, 2.881, 2, 4.4, 3.191, 2, 4.433, 2.987, 2, 4.467, 2.441, 2, 4.5, 1.651, 2, 4.533, 0.714, 2, 4.567, -0.254, 2, 4.6, -1.191, 2, 4.633, -1.981, 2, 4.667, -2.527, 2, 4.7, -2.731, 2, 4.733, -2.575, 2, 4.767, -2.158, 2, 4.8, -1.554, 2, 4.833, -0.837, 2, 4.867, -0.098, 2, 4.9, 0.618, 2, 4.933, 1.223, 2, 4.967, 1.64, 2, 5, 1.796, 2, 5.033, 1.703, 2, 5.067, 1.454, 2, 5.1, 1.093, 2, 5.133, 0.665, 2, 5.167, 0.223, 2, 5.2, -0.204, 2, 5.233, -0.565, 2, 5.267, -0.815, 2, 5.3, -0.908, 2, 5.333, -0.872, 2, 5.367, -0.773, 2, 5.4, -0.624, 2, 5.433, -0.447, 2, 5.467, -0.255, 2, 5.5, -0.062, 2, 5.533, 0.115, 2, 5.567, 0.263, 2, 5.6, 0.362, 2, 5.633, 0.398, 2, 5.667, 0.383, 2, 5.7, 0.341, 2, 5.733, 0.278, 2, 5.767, 0.203, 2, 5.8, 0.121, 2, 5.833, 0.039, 2, 5.867, -0.036, 2, 5.9, -0.099, 2, 5.933, -0.141, 2, 5.967, -0.157, 2, 6, -0.151, 2, 6.033, -0.135, 2, 6.067, -0.11, 2, 6.1, -0.081, 2, 6.133, -0.05, 2, 6.167, -0.018, 2, 6.2, 0.011, 2, 6.233, 0.035, 2, 6.267, 0.051, 2, 6.3, 0.057, 2, 6.333, 0.055, 2, 6.367, 0.05, 2, 6.4, 0.043, 2, 6.433, 0.034, 2, 6.467, 0.024, 2, 6.5, 0.013, 2, 6.533, 0.003, 2, 6.567, -0.006, 2, 6.6, -0.013, 2, 6.633, -0.018, 2, 6.667, -0.02, 2, 6.7, -0.019, 2, 6.733, -0.017, 2, 6.767, -0.014, 2, 6.8, -0.011, 2, 6.833, -0.007, 2, 6.867, -0.003, 2, 6.9, 0.001, 2, 6.933, 0.004, 2, 6.967, 0.006, 2, 7, 0.007]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh2", "Segments": [0, 0.004, 2, 0.033, 0.006, 2, 0.067, -0.01, 2, 0.1, -0.054, 2, 0.133, -0.117, 2, 0.167, -0.192, 2, 0.2, -0.27, 2, 0.233, -0.345, 2, 0.267, -0.408, 2, 0.3, -0.452, 2, 0.333, -0.468, 2, 0.367, -0.397, 2, 0.4, -0.204, 2, 0.433, 0.086, 2, 0.467, 0.433, 2, 0.5, 0.81, 2, 0.533, 1.186, 2, 0.567, 1.533, 2, 0.6, 1.823, 2, 0.633, 2.016, 2, 0.667, 2.087, 2, 0.7, 1.949, 2, 0.733, 1.573, 2, 0.767, 1.006, 2, 0.8, 0.331, 2, 0.833, -0.403, 2, 0.867, -1.138, 2, 0.9, -1.813, 2, 0.933, -2.379, 2, 0.967, -2.755, 2, 1, -2.894, 2, 1.033, -2.808, 2, 1.067, -2.563, 2, 1.1, -2.198, 2, 1.133, -1.73, 2, 1.167, -1.191, 2, 1.2, -0.616, 2, 1.233, -0.013, 2, 1.267, 0.561, 2, 1.3, 1.1, 2, 1.333, 1.569, 2, 1.367, 1.934, 2, 1.4, 2.179, 2, 1.433, 2.264, 2, 1.467, 2.184, 2, 1.5, 1.968, 2, 1.533, 1.635, 2, 1.567, 1.217, 2, 1.6, 0.754, 2, 1.633, 0.252, 2, 1.667, -0.251, 2, 1.7, -0.714, 2, 1.733, -1.132, 2, 1.767, -1.465, 2, 1.8, -1.681, 2, 1.833, -1.761, 2, 1.867, -1.677, 2, 1.9, -1.453, 2, 1.933, -1.128, 2, 1.967, -0.743, 2, 2, -0.345, 2, 2.033, 0.04, 2, 2.067, 0.365, 2, 2.1, 0.59, 2, 2.133, 0.674, 2, 2.167, 0.573, 2, 2.2, 0.327, 2, 2.233, 0.001, 2, 2.267, -0.325, 2, 2.3, -0.571, 2, 2.333, -0.672, 2, 2.367, -0.381, 2, 2.4, 0.37, 2, 2.433, 1.411, 2, 2.467, 2.516, 2, 2.5, 3.558, 2, 2.533, 4.308, 2, 2.567, 4.599, 2, 2.6, 4.137, 2, 2.633, 2.898, 2, 2.667, 1.105, 2, 2.7, -1.019, 2, 2.733, -3.214, 2, 2.767, -5.338, 2, 2.8, -7.131, 2, 2.833, -8.37, 2, 2.867, -8.832, 2, 2.9, -8.346, 2, 2.933, -7.049, 2, 2.967, -5.073, 2, 3, -2.683, 2, 3.033, 0.038, 2, 3.067, 2.792, 2, 3.1, 5.513, 2, 3.133, 7.903, 2, 3.167, 9.879, 2, 3.2, 11.176, 2, 3.233, 11.662, 2, 3.267, 10.995, 2, 3.3, 9.184, 2, 3.333, 6.454, 2, 3.367, 3.2, 2, 3.4, -0.339, 2, 3.433, -3.878, 2, 3.467, -7.132, 2, 3.5, -9.862, 2, 3.533, -11.673, 2, 3.567, -12.34, 2, 3.6, -11.817, 2, 3.633, -10.424, 2, 3.667, -8.3, 2, 3.7, -5.731, 2, 3.733, -2.806, 2, 3.767, 0.154, 2, 3.8, 3.079, 2, 3.833, 5.648, 2, 3.867, 7.772, 2, 3.9, 9.166, 2, 3.933, 9.688, 2, 3.967, 9.153, 2, 4, 7.719, 2, 4.033, 5.644, 2, 4.067, 3.185, 2, 4.1, 0.644, 2, 4.133, -1.815, 2, 4.167, -3.89, 2, 4.2, -5.324, 2, 4.233, -5.859, 2, 4.267, -5.519, 2, 4.3, -4.608, 2, 4.333, -3.29, 2, 4.367, -1.729, 2, 4.4, -0.115, 2, 4.433, 1.447, 2, 4.467, 2.765, 2, 4.5, 3.675, 2, 4.533, 4.015, 2, 4.567, 3.761, 2, 4.6, 3.081, 2, 4.633, 2.096, 2, 4.667, 0.929, 2, 4.7, -0.277, 2, 4.733, -1.444, 2, 4.767, -2.429, 2, 4.8, -3.109, 2, 4.833, -3.363, 2, 4.867, -3.161, 2, 4.9, -2.62, 2, 4.933, -1.838, 2, 4.967, -0.91, 2, 5, 0.048, 2, 5.033, 0.976, 2, 5.067, 1.758, 2, 5.1, 2.299, 2, 5.133, 2.501, 2, 5.167, 2.39, 2, 5.2, 2.087, 2, 5.233, 1.631, 2, 5.267, 1.087, 2, 5.3, 0.496, 2, 5.333, -0.095, 2, 5.367, -0.639, 2, 5.4, -1.095, 2, 5.433, -1.398, 2, 5.467, -1.509, 2, 5.5, -1.446, 2, 5.533, -1.274, 2, 5.567, -1.015, 2, 5.6, -0.706, 2, 5.633, -0.37, 2, 5.667, -0.034, 2, 5.7, 0.274, 2, 5.733, 0.533, 2, 5.767, 0.705, 2, 5.8, 0.769, 2, 5.833, 0.738, 2, 5.867, 0.653, 2, 5.9, 0.526, 2, 5.933, 0.375, 2, 5.967, 0.21, 2, 6, 0.045, 2, 6.033, -0.106, 2, 6.067, -0.233, 2, 6.1, -0.318, 2, 6.133, -0.349, 2, 6.167, -0.335, 2, 6.2, -0.298, 2, 6.233, -0.241, 2, 6.267, -0.174, 2, 6.3, -0.102, 2, 6.333, -0.029, 2, 6.367, 0.038, 2, 6.4, 0.095, 2, 6.433, 0.132, 2, 6.467, 0.146, 2, 6.5, 0.14, 2, 6.533, 0.125, 2, 6.567, 0.102, 2, 6.6, 0.074, 2, 6.633, 0.044, 2, 6.667, 0.014, 2, 6.7, -0.013, 2, 6.733, -0.036, 2, 6.767, -0.051, 2, 6.8, -0.057, 2, 6.833, -0.055, 2, 6.867, -0.049, 2, 6.9, -0.04, 2, 6.933, -0.029, 2, 6.967, -0.018, 2, 7, -0.006]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh3", "Segments": [0, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh3", "Segments": [0, 0, 2, 0.033, 0.08, 2, 0.067, 0.274, 2, 0.1, 0.532, 2, 0.133, 0.79, 2, 0.167, 0.984, 2, 0.2, 1.064, 2, 0.233, 0.978, 2, 0.267, 0.75, 2, 0.3, 0.402, 2, 0.333, -0.018, 2, 0.367, -0.497, 2, 0.4, -0.982, 2, 0.433, -1.461, 2, 0.467, -1.882, 2, 0.5, -2.229, 2, 0.533, -2.458, 2, 0.567, -2.543, 2, 0.6, -2.501, 2, 0.633, -2.381, 2, 0.667, -2.195, 2, 0.7, -1.954, 2, 0.733, -1.669, 2, 0.767, -1.35, 2, 0.8, -1.009, 2, 0.833, -0.658, 2, 0.867, -0.306, 2, 0.9, 0.035, 2, 0.933, 0.353, 2, 0.967, 0.639, 2, 1, 0.88, 2, 1.033, 1.066, 2, 1.067, 1.186, 2, 1.1, 1.228, 2, 1.133, 1.18, 2, 1.167, 1.051, 2, 1.2, 0.856, 2, 1.233, 0.623, 2, 1.267, 0.37, 2, 1.3, 0.117, 2, 1.333, -0.116, 2, 1.367, -0.311, 2, 1.4, -0.44, 2, 1.433, -0.488, 2, 1.467, -0.467, 2, 1.5, -0.41, 2, 1.533, -0.327, 2, 1.567, -0.229, 2, 1.6, -0.128, 2, 1.633, -0.031, 2, 1.667, 0.052, 2, 1.7, 0.109, 2, 1.733, 0.13, 2, 1.767, 0.123, 2, 1.8, 0.105, 2, 1.833, 0.079, 2, 1.867, 0.049, 2, 1.9, 0.02, 2, 1.933, -0.006, 2, 1.967, -0.024, 2, 2, -0.031, 2, 2.033, 0.343, 2, 2.067, 1.237, 2, 2.1, 2.327, 2, 2.133, 3.221, 2, 2.167, 3.596, 2, 2.2, 3.179, 2, 2.233, 2.078, 2, 2.267, 0.523, 2, 2.3, -1.26, 2, 2.333, -3.043, 2, 2.367, -4.599, 2, 2.4, -5.699, 2, 2.433, -6.116, 2, 2.467, -5.833, 2, 2.5, -5.076, 2, 2.533, -3.923, 2, 2.567, -2.529, 2, 2.6, -0.941, 2, 2.633, 0.666, 2, 2.667, 2.254, 2, 2.7, 3.648, 2, 2.733, 4.801, 2, 2.767, 5.558, 2, 2.8, 5.841, 2, 2.833, 5.495, 2, 2.867, 4.555, 2, 2.9, 3.138, 2, 2.933, 1.449, 2, 2.967, -0.388, 2, 3, -2.225, 2, 3.033, -3.914, 2, 3.067, -5.331, 2, 3.1, -6.271, 2, 3.133, -6.617, 2, 3.167, -6.364, 2, 3.2, -5.689, 2, 3.233, -4.659, 2, 3.267, -3.414, 2, 3.3, -1.997, 2, 3.333, -0.562, 2, 3.367, 0.856, 2, 3.4, 2.101, 2, 3.433, 3.131, 2, 3.467, 3.806, 2, 3.5, 4.059, 2, 3.533, 3.925, 2, 3.567, 3.565, 2, 3.6, 3.044, 2, 3.633, 2.426, 2, 3.667, 1.788, 2, 3.7, 1.171, 2, 3.733, 0.649, 2, 3.767, 0.289, 2, 3.8, 0.155, 2, 3.833, 0.321, 2, 3.867, 0.722, 2, 3.9, 1.256, 2, 3.933, 1.791, 2, 3.967, 2.192, 2, 4, 2.358, 2, 4.033, 2.232, 2, 4.067, 1.891, 2, 4.1, 1.377, 2, 4.133, 0.765, 2, 4.167, 0.098, 2, 4.2, -0.568, 2, 4.233, -1.18, 2, 4.267, -1.694, 2, 4.3, -2.035, 2, 4.333, -2.161, 2, 4.367, -2.035, 2, 4.4, -1.703, 2, 4.433, -1.235, 2, 4.467, -0.697, 2, 4.5, -0.159, 2, 4.533, 0.31, 2, 4.567, 0.641, 2, 4.6, 0.767, 2, 4.633, 0.734, 2, 4.667, 0.644, 2, 4.7, 0.515, 2, 4.733, 0.361, 2, 4.767, 0.203, 2, 4.8, 0.049, 2, 4.833, -0.08, 2, 4.867, -0.17, 2, 4.9, -0.203, 2, 4.933, -0.194, 2, 4.967, -0.171, 2, 5, -0.136, 2, 5.033, -0.096, 2, 5.067, -0.054, 2, 5.1, -0.013, 2, 5.133, 0.021, 2, 5.167, 0.045, 2, 5.2, 0.054, 2, 5.233, 0.052, 2, 5.267, 0.047, 2, 5.3, 0.039, 2, 5.333, 0.03, 2, 5.367, 0.02, 2, 5.4, 0.01, 2, 5.433, 0, 2, 5.467, -0.007, 2, 5.5, -0.012, 2, 5.533, -0.014, 2, 5.567, -0.014, 2, 5.6, -0.012, 2, 5.633, -0.01, 2, 5.667, -0.007, 2, 5.7, -0.004, 2, 5.733, -0.001, 2, 5.767, 0.002, 2, 5.8, 0.003, 2, 5.833, 0.004, 2, 5.867, 0.004, 2, 5.9, 0.003, 2, 5.933, 0.002, 2, 5.967, 0.001, 2, 6, 0.001, 2, 6.033, 0, 2, 6.067, -0.001, 2, 6.1, -0.001, 2, 6.167, -0.001, 2, 6.2, -0.001, 2, 6.233, -0.001, 2, 6.267, 0, 2, 6.3, 0, 2, 6.333, 0, 2, 6.367, 0, 2, 6.4, 0, 2, 6.5, 0, 2, 6.567, 0, 2, 6.6, 0, 2, 6.7, 0, 2, 6.833, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh3", "Segments": [0, 0, 2, 0.033, -0.084, 2, 0.067, -0.268, 2, 0.1, -0.452, 2, 0.133, -0.536, 2, 0.167, -0.451, 2, 0.2, -0.226, 2, 0.233, 0.092, 2, 0.267, 0.456, 2, 0.3, 0.82, 2, 0.333, 1.137, 2, 0.367, 1.362, 2, 0.4, 1.447, 2, 0.433, 1.357, 2, 0.467, 1.115, 2, 0.5, 0.764, 2, 0.533, 0.349, 2, 0.567, -0.08, 2, 0.6, -0.495, 2, 0.633, -0.845, 2, 0.667, -1.088, 2, 0.7, -1.178, 2, 0.733, -1.133, 2, 0.767, -1.016, 2, 0.8, -0.851, 2, 0.833, -0.661, 2, 0.867, -0.471, 2, 0.9, -0.306, 2, 0.933, -0.189, 2, 0.967, -0.144, 2, 1, -0.155, 2, 1.033, -0.116, 2, 1.067, -0.012, 2, 1.1, 0.134, 2, 1.133, 0.302, 2, 1.167, 0.47, 2, 1.2, 0.616, 2, 1.233, 0.72, 2, 1.267, 0.759, 2, 1.3, 0.712, 2, 1.333, 0.585, 2, 1.367, 0.401, 2, 1.4, 0.184, 2, 1.433, -0.041, 2, 1.467, -0.258, 2, 1.5, -0.442, 2, 1.533, -0.568, 2, 1.567, -0.616, 2, 1.6, -0.585, 2, 1.633, -0.501, 2, 1.667, -0.381, 2, 1.7, -0.238, 2, 1.733, -0.09, 2, 1.767, 0.052, 2, 1.8, 0.173, 2, 1.833, 0.256, 2, 1.867, 0.287, 2, 1.9, 0.179, 2, 1.933, -0.105, 2, 1.967, -0.507, 2, 2, -0.967, 2, 2.033, -1.428, 2, 2.067, -1.83, 2, 2.1, -2.114, 2, 2.133, -2.221, 2, 2.167, -1.722, 2, 2.2, -0.509, 2, 2.233, 1.103, 2, 2.267, 2.715, 2, 2.3, 3.927, 2, 2.333, 4.427, 2, 2.367, 3.988, 2, 2.4, 2.831, 2, 2.433, 1.195, 2, 2.467, -0.68, 2, 2.5, -2.556, 2, 2.533, -4.192, 2, 2.567, -5.349, 2, 2.6, -5.788, 2, 2.633, -5.527, 2, 2.667, -4.833, 2, 2.7, -3.775, 2, 2.733, -2.495, 2, 2.767, -1.038, 2, 2.8, 0.437, 2, 2.833, 1.893, 2, 2.867, 3.173, 2, 2.9, 4.231, 2, 2.933, 4.926, 2, 2.967, 5.186, 2, 3, 4.865, 2, 3.033, 3.992, 2, 3.067, 2.677, 2, 3.1, 1.11, 2, 3.133, -0.595, 2, 3.167, -2.3, 2, 3.2, -3.867, 2, 3.233, -5.182, 2, 3.267, -6.055, 2, 3.3, -6.376, 2, 3.333, -6.109, 2, 3.367, -5.386, 2, 3.4, -4.295, 2, 3.433, -2.994, 2, 3.467, -1.58, 2, 3.5, -0.166, 2, 3.533, 1.134, 2, 3.567, 2.225, 2, 3.6, 2.949, 2, 3.633, 3.215, 2, 3.667, 3.016, 2, 3.7, 2.484, 2, 3.733, 1.714, 2, 3.767, 0.802, 2, 3.8, -0.141, 2, 3.833, -1.054, 2, 3.867, -1.824, 2, 3.9, -2.356, 2, 3.933, -2.555, 2, 3.967, -2.385, 2, 4, -1.93, 2, 4.033, -1.272, 2, 4.067, -0.492, 2, 4.1, 0.314, 2, 4.133, 1.094, 2, 4.167, 1.752, 2, 4.2, 2.207, 2, 4.233, 2.376, 2, 4.267, 2.177, 2, 4.3, 1.651, 2, 4.333, 0.907, 2, 4.367, 0.054, 2, 4.4, -0.799, 2, 4.433, -1.543, 2, 4.467, -2.069, 2, 4.5, -2.268, 2, 4.533, -2.117, 2, 4.567, -1.718, 2, 4.6, -1.154, 2, 4.633, -0.507, 2, 4.667, 0.14, 2, 4.7, 0.704, 2, 4.733, 1.103, 2, 4.767, 1.254, 2, 4.8, 1.193, 2, 4.833, 1.03, 2, 4.867, 0.794, 2, 4.9, 0.514, 2, 4.933, 0.225, 2, 4.967, -0.055, 2, 5, -0.292, 2, 5.033, -0.455, 2, 5.067, -0.516, 2, 5.1, -0.492, 2, 5.133, -0.427, 2, 5.167, -0.333, 2, 5.2, -0.222, 2, 5.233, -0.107, 2, 5.267, 0.004, 2, 5.3, 0.098, 2, 5.333, 0.163, 2, 5.367, 0.187, 2, 5.4, 0.179, 2, 5.433, 0.156, 2, 5.467, 0.122, 2, 5.5, 0.082, 2, 5.533, 0.041, 2, 5.567, 0.002, 2, 5.6, -0.032, 2, 5.633, -0.055, 2, 5.667, -0.064, 2, 5.7, -0.061, 2, 5.733, -0.053, 2, 5.767, -0.042, 2, 5.8, -0.028, 2, 5.833, -0.015, 2, 5.867, -0.001, 2, 5.9, 0.01, 2, 5.933, 0.018, 2, 5.967, 0.021, 2, 6, 0.02, 2, 6.033, 0.017, 2, 6.067, 0.014, 2, 6.1, 0.009, 2, 6.133, 0.005, 2, 6.167, 0.001, 2, 6.2, -0.003, 2, 6.233, -0.006, 2, 6.267, -0.006, 2, 6.3, -0.006, 2, 6.333, -0.005, 2, 6.367, -0.004, 2, 6.4, -0.003, 2, 6.433, -0.002, 2, 6.467, 0, 2, 6.5, 0.001, 2, 6.533, 0.002, 2, 6.567, 0.002, 2, 6.6, 0.002, 2, 6.633, 0.002, 2, 6.667, 0.001, 2, 6.7, 0.001, 2, 6.733, 0, 2, 6.767, 0, 2, 6.8, 0, 2, 6.833, -0.001, 2, 6.933, -0.001, 2, 6.967, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh3", "Segments": [0, -0.001, 2, 0.033, -0.025, 2, 0.067, -0.088, 2, 0.1, -0.175, 2, 0.133, -0.267, 2, 0.167, -0.354, 2, 0.2, -0.417, 2, 0.233, -0.441, 2, 0.267, -0.355, 2, 0.3, -0.128, 2, 0.333, 0.192, 2, 0.367, 0.56, 2, 0.4, 0.927, 2, 0.433, 1.248, 2, 0.467, 1.474, 2, 0.5, 1.56, 2, 0.533, 1.45, 2, 0.567, 1.153, 2, 0.6, 0.724, 2, 0.633, 0.215, 2, 0.667, -0.31, 2, 0.7, -0.818, 2, 0.733, -1.248, 2, 0.767, -1.544, 2, 0.8, -1.655, 2, 0.833, -1.627, 2, 0.867, -1.549, 2, 0.9, -1.427, 2, 0.933, -1.27, 2, 0.967, -1.083, 2, 1, -0.875, 2, 1.033, -0.652, 2, 1.067, -0.422, 2, 1.1, -0.192, 2, 1.133, 0.03, 2, 1.167, 0.239, 2, 1.2, 0.425, 2, 1.233, 0.583, 2, 1.267, 0.704, 2, 1.3, 0.783, 2, 1.333, 0.81, 2, 1.367, 0.765, 2, 1.4, 0.642, 2, 1.433, 0.456, 2, 1.467, 0.235, 2, 1.5, -0.005, 2, 1.533, -0.245, 2, 1.567, -0.466, 2, 1.6, -0.652, 2, 1.633, -0.775, 2, 1.667, -0.82, 2, 1.7, -0.775, 2, 1.733, -0.653, 2, 1.767, -0.477, 2, 1.8, -0.268, 2, 1.833, -0.053, 2, 1.867, 0.156, 2, 1.9, 0.332, 2, 1.933, 0.454, 2, 1.967, 0.499, 2, 2, 0.379, 2, 2.033, 0.069, 2, 2.067, -0.361, 2, 2.1, -0.817, 2, 2.133, -1.248, 2, 2.167, -1.557, 2, 2.2, -1.678, 2, 2.233, -1.339, 2, 2.267, -0.466, 2, 2.3, 0.746, 2, 2.333, 2.031, 2, 2.367, 3.243, 2, 2.4, 4.116, 2, 2.433, 4.454, 2, 2.467, 3.974, 2, 2.5, 2.707, 2, 2.533, 0.917, 2, 2.567, -1.136, 2, 2.6, -3.189, 2, 2.633, -4.98, 2, 2.667, -6.247, 2, 2.7, -6.727, 2, 2.733, -6.346, 2, 2.767, -5.313, 2, 2.8, -3.755, 2, 2.833, -1.899, 2, 2.867, 0.12, 2, 2.9, 2.14, 2, 2.933, 3.996, 2, 2.967, 5.554, 2, 3, 6.587, 2, 3.033, 6.968, 2, 3.067, 6.619, 2, 3.1, 5.688, 2, 3.133, 4.269, 2, 3.167, 2.553, 2, 3.2, 0.6, 2, 3.233, -1.378, 2, 3.267, -3.331, 2, 3.3, -5.047, 2, 3.333, -6.466, 2, 3.367, -7.397, 2, 3.4, -7.746, 2, 3.433, -7.294, 2, 3.467, -6.083, 2, 3.5, -4.33, 2, 3.533, -2.253, 2, 3.567, -0.107, 2, 3.6, 1.97, 2, 3.633, 3.722, 2, 3.667, 4.933, 2, 3.7, 5.385, 2, 3.733, 5.133, 2, 3.767, 4.447, 2, 3.8, 3.413, 2, 3.833, 2.181, 2, 3.867, 0.841, 2, 3.9, -0.499, 2, 3.933, -1.731, 2, 3.967, -2.765, 2, 4, -3.451, 2, 4.033, -3.704, 2, 4.067, -3.462, 2, 4.1, -2.815, 2, 4.133, -1.879, 2, 4.167, -0.77, 2, 4.2, 0.377, 2, 4.233, 1.486, 2, 4.267, 2.422, 2, 4.3, 3.069, 2, 4.333, 3.311, 2, 4.367, 3.038, 2, 4.4, 2.32, 2, 4.433, 1.304, 2, 4.467, 0.14, 2, 4.5, -1.025, 2, 4.533, -2.041, 2, 4.567, -2.759, 2, 4.6, -3.032, 2, 4.633, -2.86, 2, 4.667, -2.399, 2, 4.7, -1.732, 2, 4.733, -0.942, 2, 4.767, -0.126, 2, 4.8, 0.664, 2, 4.833, 1.331, 2, 4.867, 1.791, 2, 4.9, 1.963, 2, 4.933, 1.835, 2, 4.967, 1.497, 2, 5, 1.02, 2, 5.033, 0.472, 2, 5.067, -0.076, 2, 5.1, -0.554, 2, 5.133, -0.892, 2, 5.167, -1.02, 2, 5.2, -0.969, 2, 5.233, -0.833, 2, 5.267, -0.636, 2, 5.3, -0.403, 2, 5.333, -0.162, 2, 5.367, 0.071, 2, 5.4, 0.268, 2, 5.433, 0.403, 2, 5.467, 0.454, 2, 5.5, 0.432, 2, 5.533, 0.374, 2, 5.567, 0.289, 2, 5.6, 0.188, 2, 5.633, 0.084, 2, 5.667, -0.017, 2, 5.7, -0.102, 2, 5.733, -0.161, 2, 5.767, -0.183, 2, 5.8, -0.174, 2, 5.833, -0.151, 2, 5.867, -0.117, 2, 5.9, -0.078, 2, 5.933, -0.037, 2, 5.967, 0.003, 2, 6, 0.037, 2, 6.033, 0.06, 2, 6.067, 0.069, 2, 6.1, 0.065, 2, 6.133, 0.057, 2, 6.167, 0.044, 2, 6.2, 0.03, 2, 6.233, 0.014, 2, 6.267, 0, 2, 6.3, -0.013, 2, 6.333, -0.021, 2, 6.367, -0.024, 2, 6.4, -0.023, 2, 6.433, -0.021, 2, 6.467, -0.017, 2, 6.5, -0.013, 2, 6.533, -0.008, 2, 6.567, -0.003, 2, 6.6, 0.001, 2, 6.633, 0.005, 2, 6.667, 0.007, 2, 6.7, 0.008, 2, 6.733, 0.008, 2, 6.767, 0.007, 2, 6.8, 0.005, 2, 6.833, 0.003, 2, 6.867, 0.001, 2, 6.9, -0.001, 2, 6.933, -0.002, 2, 6.967, -0.003, 2, 7, -0.003]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh3", "Segments": [0, -0.004, 2, 0.033, -0.018, 2, 0.067, -0.054, 2, 0.1, -0.108, 2, 0.133, -0.171, 2, 0.167, -0.236, 2, 0.2, -0.299, 2, 0.233, -0.352, 2, 0.267, -0.389, 2, 0.3, -0.403, 2, 0.333, -0.33, 2, 0.367, -0.136, 2, 0.4, 0.145, 2, 0.433, 0.479, 2, 0.467, 0.823, 2, 0.5, 1.156, 2, 0.533, 1.437, 2, 0.567, 1.632, 2, 0.6, 1.704, 2, 0.633, 1.57, 2, 0.667, 1.21, 2, 0.7, 0.688, 2, 0.733, 0.071, 2, 0.767, -0.568, 2, 0.8, -1.185, 2, 0.833, -1.707, 2, 0.867, -2.067, 2, 0.9, -2.201, 2, 0.933, -2.135, 2, 0.967, -1.957, 2, 1, -1.682, 2, 1.033, -1.337, 2, 1.067, -0.954, 2, 1.1, -0.539, 2, 1.133, -0.124, 2, 1.167, 0.258, 2, 1.2, 0.603, 2, 1.233, 0.878, 2, 1.267, 1.056, 2, 1.3, 1.123, 2, 1.333, 1.091, 2, 1.367, 1.003, 2, 1.4, 0.866, 2, 1.433, 0.695, 2, 1.467, 0.49, 2, 1.5, 0.269, 2, 1.533, 0.039, 2, 1.567, -0.191, 2, 1.6, -0.412, 2, 1.633, -0.617, 2, 1.667, -0.788, 2, 1.7, -0.926, 2, 1.733, -1.014, 2, 1.767, -1.045, 2, 1.8, -0.984, 2, 1.833, -0.82, 2, 1.867, -0.583, 2, 1.9, -0.301, 2, 1.933, -0.011, 2, 1.967, 0.271, 2, 2, 0.508, 2, 2.033, 0.672, 2, 2.067, 0.733, 2, 2.1, 0.577, 2, 2.133, 0.197, 2, 2.167, -0.307, 2, 2.2, -0.812, 2, 2.233, -1.191, 2, 2.267, -1.348, 2, 2.3, -1.095, 2, 2.333, -0.43, 2, 2.367, 0.511, 2, 2.4, 1.589, 2, 2.433, 2.668, 2, 2.467, 3.609, 2, 2.5, 4.274, 2, 2.533, 4.526, 2, 2.567, 3.996, 2, 2.6, 2.596, 2, 2.633, 0.618, 2, 2.667, -1.65, 2, 2.7, -3.918, 2, 2.733, -5.896, 2, 2.767, -7.296, 2, 2.8, -7.827, 2, 2.833, -7.354, 2, 2.867, -6.071, 2, 2.9, -4.137, 2, 2.933, -1.832, 2, 2.967, 0.674, 2, 3, 3.181, 2, 3.033, 5.486, 2, 3.067, 7.42, 2, 3.1, 8.703, 2, 3.133, 9.175, 2, 3.167, 8.645, 2, 3.2, 7.204, 2, 3.233, 5.032, 2, 3.267, 2.442, 2, 3.3, -0.373, 2, 3.333, -3.189, 2, 3.367, -5.778, 2, 3.4, -7.95, 2, 3.433, -9.391, 2, 3.467, -9.922, 2, 3.5, -9.427, 2, 3.533, -8.084, 2, 3.567, -6.06, 2, 3.6, -3.647, 2, 3.633, -1.024, 2, 3.667, 1.6, 2, 3.7, 4.013, 2, 3.733, 6.037, 2, 3.767, 7.38, 2, 3.8, 7.875, 2, 3.833, 7.502, 2, 3.867, 6.489, 2, 3.9, 4.962, 2, 3.933, 3.142, 2, 3.967, 1.162, 2, 4, -0.817, 2, 4.033, -2.637, 2, 4.067, -4.164, 2, 4.1, -5.177, 2, 4.133, -5.55, 2, 4.167, -5.198, 2, 4.2, -4.254, 2, 4.233, -2.888, 2, 4.267, -1.269, 2, 4.3, 0.404, 2, 4.333, 2.023, 2, 4.367, 3.389, 2, 4.4, 4.333, 2, 4.433, 4.685, 2, 4.467, 4.306, 2, 4.5, 3.305, 2, 4.533, 1.891, 2, 4.567, 0.27, 2, 4.6, -1.351, 2, 4.633, -2.766, 2, 4.667, -3.766, 2, 4.7, -4.145, 2, 4.733, -3.898, 2, 4.767, -3.237, 2, 4.8, -2.279, 2, 4.833, -1.144, 2, 4.867, 0.028, 2, 4.9, 1.163, 2, 4.933, 2.12, 2, 4.967, 2.782, 2, 5, 3.029, 2, 5.033, 2.863, 2, 5.067, 2.417, 2, 5.1, 1.772, 2, 5.133, 1.007, 2, 5.167, 0.217, 2, 5.2, -0.547, 2, 5.233, -1.192, 2, 5.267, -1.638, 2, 5.3, -1.804, 2, 5.333, -1.71, 2, 5.367, -1.458, 2, 5.4, -1.094, 2, 5.433, -0.662, 2, 5.467, -0.216, 2, 5.5, 0.215, 2, 5.533, 0.58, 2, 5.567, 0.831, 2, 5.6, 0.925, 2, 5.633, 0.879, 2, 5.667, 0.754, 2, 5.7, 0.574, 2, 5.733, 0.36, 2, 5.767, 0.139, 2, 5.8, -0.074, 2, 5.833, -0.255, 2, 5.867, -0.379, 2, 5.9, -0.426, 2, 5.933, -0.405, 2, 5.967, -0.349, 2, 6, -0.268, 2, 6.033, -0.172, 2, 6.067, -0.073, 2, 6.1, 0.023, 2, 6.133, 0.104, 2, 6.167, 0.16, 2, 6.2, 0.181, 2, 6.233, 0.172, 2, 6.267, 0.149, 2, 6.3, 0.115, 2, 6.333, 0.075, 2, 6.367, 0.034, 2, 6.4, -0.006, 2, 6.433, -0.04, 2, 6.467, -0.064, 2, 6.5, -0.072, 2, 6.533, -0.069, 2, 6.567, -0.06, 2, 6.6, -0.046, 2, 6.633, -0.031, 2, 6.667, -0.014, 2, 6.7, 0.002, 2, 6.733, 0.015, 2, 6.767, 0.024, 2, 6.8, 0.028, 2, 6.833, 0.026, 2, 6.867, 0.023, 2, 6.9, 0.018, 2, 6.933, 0.012, 2, 6.967, 0.006, 2, 7, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh46", "Segments": [0, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh46", "Segments": [0, 0, 2, 0.033, 0.046, 2, 0.067, 0.163, 2, 0.1, 0.326, 2, 0.133, 0.499, 2, 0.167, 0.662, 2, 0.2, 0.78, 2, 0.233, 0.825, 2, 0.267, 0.763, 2, 0.3, 0.594, 2, 0.333, 0.335, 2, 0.367, 0.009, 2, 0.4, -0.353, 2, 0.433, -0.745, 2, 0.467, -1.136, 2, 0.5, -1.498, 2, 0.533, -1.824, 2, 0.567, -2.084, 2, 0.6, -2.252, 2, 0.633, -2.315, 2, 0.667, -2.27, 2, 0.7, -2.145, 2, 0.733, -1.949, 2, 0.767, -1.699, 2, 0.8, -1.401, 2, 0.833, -1.077, 2, 0.867, -0.734, 2, 0.9, -0.385, 2, 0.933, -0.042, 2, 0.967, 0.283, 2, 1, 0.58, 2, 1.033, 0.83, 2, 1.067, 1.026, 2, 1.1, 1.151, 2, 1.133, 1.196, 2, 1.167, 1.158, 2, 1.2, 1.056, 2, 1.233, 0.902, 2, 1.267, 0.715, 2, 1.3, 0.502, 2, 1.333, 0.287, 2, 1.367, 0.074, 2, 1.4, -0.113, 2, 1.433, -0.267, 2, 1.467, -0.369, 2, 1.5, -0.407, 2, 1.533, -0.396, 2, 1.567, -0.369, 2, 1.6, -0.328, 2, 1.633, -0.275, 2, 1.667, -0.217, 2, 1.7, -0.154, 2, 1.733, -0.09, 2, 1.767, -0.032, 2, 1.8, 0.021, 2, 1.833, 0.062, 2, 1.867, 0.089, 2, 1.9, 0.1, 2, 1.933, 0.09, 2, 1.967, 0.073, 2, 2, 0.064, 2, 2.033, 0.336, 2, 2.067, 0.987, 2, 2.1, 1.779, 2, 2.133, 2.43, 2, 2.167, 2.702, 2, 2.2, 2.449, 2, 2.233, 1.772, 2, 2.267, 0.793, 2, 2.3, -0.368, 2, 2.333, -1.568, 2, 2.367, -2.729, 2, 2.4, -3.708, 2, 2.433, -4.385, 2, 2.467, -4.638, 2, 2.5, -4.404, 2, 2.533, -3.782, 2, 2.567, -2.833, 2, 2.6, -1.686, 2, 2.633, -0.379, 2, 2.667, 0.943, 2, 2.7, 2.25, 2, 2.733, 3.397, 2, 2.767, 4.346, 2, 2.8, 4.968, 2, 2.833, 5.202, 2, 2.867, 4.914, 2, 2.9, 4.132, 2, 2.933, 2.953, 2, 2.967, 1.548, 2, 3, 0.02, 2, 3.033, -1.508, 2, 3.067, -2.914, 2, 3.1, -4.093, 2, 3.133, -4.875, 2, 3.167, -5.163, 2, 3.2, -4.982, 2, 3.233, -4.498, 2, 3.267, -3.75, 2, 3.3, -2.812, 2, 3.333, -1.771, 2, 3.367, -0.644, 2, 3.4, 0.484, 2, 3.433, 1.524, 2, 3.467, 2.463, 2, 3.5, 3.211, 2, 3.533, 3.695, 2, 3.567, 3.876, 2, 3.6, 3.769, 2, 3.633, 3.484, 2, 3.667, 3.072, 2, 3.7, 2.583, 2, 3.733, 2.078, 2, 3.767, 1.589, 2, 3.8, 1.177, 2, 3.833, 0.892, 2, 3.867, 0.785, 2, 3.9, 0.849, 2, 3.933, 1.001, 2, 3.967, 1.186, 2, 4, 1.338, 2, 4.033, 1.402, 2, 4.067, 1.321, 2, 4.1, 1.104, 2, 4.133, 0.775, 2, 4.167, 0.384, 2, 4.2, -0.042, 2, 4.233, -0.468, 2, 4.267, -0.86, 2, 4.3, -1.188, 2, 4.333, -1.406, 2, 4.367, -1.486, 2, 4.4, -1.433, 2, 4.433, -1.288, 2, 4.467, -1.07, 2, 4.5, -0.81, 2, 4.533, -0.527, 2, 4.567, -0.244, 2, 4.6, 0.016, 2, 4.633, 0.235, 2, 4.667, 0.379, 2, 4.7, 0.433, 2, 4.733, 0.422, 2, 4.767, 0.393, 2, 4.8, 0.348, 2, 4.833, 0.292, 2, 4.867, 0.23, 2, 4.9, 0.163, 2, 4.933, 0.096, 2, 4.967, 0.033, 2, 5, -0.023, 2, 5.033, -0.067, 2, 5.067, -0.096, 2, 5.1, -0.107, 2, 5.133, -0.104, 2, 5.167, -0.095, 2, 5.2, -0.082, 2, 5.233, -0.067, 2, 5.267, -0.049, 2, 5.3, -0.031, 2, 5.333, -0.014, 2, 5.367, 0.002, 2, 5.4, 0.015, 2, 5.433, 0.023, 2, 5.467, 0.026, 2, 5.5, 0.026, 2, 5.533, 0.024, 2, 5.567, 0.021, 2, 5.6, 0.018, 2, 5.633, 0.014, 2, 5.667, 0.01, 2, 5.7, 0.006, 2, 5.733, 0.002, 2, 5.767, -0.001, 2, 5.8, -0.004, 2, 5.833, -0.006, 2, 5.867, -0.006, 2, 5.9, -0.006, 2, 5.933, -0.006, 2, 5.967, -0.005, 2, 6, -0.004, 2, 6.033, -0.003, 2, 6.067, -0.002, 2, 6.1, -0.001, 2, 6.133, 0, 2, 6.167, 0.001, 2, 6.2, 0.001, 2, 6.233, 0.002, 2, 6.267, 0.002, 2, 6.3, 0.001, 2, 6.333, 0.001, 2, 6.367, 0.001, 2, 6.4, 0, 2, 6.433, 0, 2, 6.467, 0, 2, 6.5, 0, 2, 6.533, 0, 2, 6.6, 0, 2, 6.633, 0, 2, 6.667, 0, 2, 6.7, 0, 2, 6.733, 0, 2, 6.8, 0, 2, 6.833, 0, 2, 6.933, 0, 2, 6.967, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh46", "Segments": [0, 0, 2, 0.033, -0.05, 2, 0.067, -0.168, 2, 0.1, -0.312, 2, 0.133, -0.43, 2, 0.167, -0.48, 2, 0.2, -0.415, 2, 0.233, -0.24, 2, 0.267, 0.014, 2, 0.3, 0.314, 2, 0.333, 0.624, 2, 0.367, 0.924, 2, 0.4, 1.177, 2, 0.433, 1.352, 2, 0.467, 1.417, 2, 0.5, 1.363, 2, 0.533, 1.216, 2, 0.567, 0.99, 2, 0.6, 0.706, 2, 0.633, 0.391, 2, 0.667, 0.049, 2, 0.7, -0.292, 2, 0.733, -0.607, 2, 0.767, -0.891, 2, 0.8, -1.118, 2, 0.833, -1.264, 2, 0.867, -1.319, 2, 0.9, -1.283, 2, 0.933, -1.182, 2, 0.967, -1.025, 2, 1, -0.829, 2, 1.033, -0.595, 2, 1.067, -0.343, 2, 1.1, -0.079, 2, 1.133, 0.184, 2, 1.167, 0.437, 2, 1.2, 0.67, 2, 1.233, 0.866, 2, 1.267, 1.023, 2, 1.3, 1.124, 2, 1.333, 1.16, 2, 1.367, 1.117, 2, 1.4, 1.003, 2, 1.433, 0.829, 2, 1.467, 0.619, 2, 1.5, 0.379, 2, 1.533, 0.136, 2, 1.567, -0.104, 2, 1.6, -0.314, 2, 1.633, -0.488, 2, 1.667, -0.602, 2, 1.7, -0.645, 2, 1.733, -0.616, 2, 1.767, -0.537, 2, 1.8, -0.423, 2, 1.833, -0.287, 2, 1.867, -0.148, 2, 1.9, -0.012, 2, 1.933, 0.102, 2, 1.967, 0.181, 2, 2, 0.21, 2, 2.033, -0.072, 2, 2.067, -0.693, 2, 2.1, -1.314, 2, 2.133, -1.597, 2, 2.167, -1.369, 2, 2.2, -0.771, 2, 2.233, 0.075, 2, 2.267, 1.045, 2, 2.3, 2.015, 2, 2.333, 2.861, 2, 2.367, 3.46, 2, 2.4, 3.687, 2, 2.433, 3.388, 2, 2.467, 2.589, 2, 2.5, 1.432, 2, 2.533, 0.061, 2, 2.567, -1.355, 2, 2.6, -2.726, 2, 2.633, -3.883, 2, 2.667, -4.682, 2, 2.7, -4.98, 2, 2.733, -4.717, 2, 2.767, -4.016, 2, 2.8, -2.947, 2, 2.833, -1.654, 2, 2.867, -0.183, 2, 2.9, 1.307, 2, 2.933, 2.779, 2, 2.967, 4.072, 2, 3, 5.14, 2, 3.033, 5.842, 2, 3.067, 6.105, 2, 3.1, 5.779, 2, 3.133, 4.896, 2, 3.167, 3.564, 2, 3.2, 1.976, 2, 3.233, 0.25, 2, 3.267, -1.477, 2, 3.3, -3.065, 2, 3.333, -4.396, 2, 3.367, -5.28, 2, 3.4, -5.606, 2, 3.433, -5.387, 2, 3.467, -4.805, 2, 3.5, -3.918, 2, 3.533, -2.846, 2, 3.567, -1.624, 2, 3.6, -0.388, 2, 3.633, 0.833, 2, 3.667, 1.906, 2, 3.7, 2.793, 2, 3.733, 3.375, 2, 3.767, 3.593, 2, 3.8, 3.427, 2, 3.833, 2.981, 2, 3.867, 2.336, 2, 3.9, 1.571, 2, 3.933, 0.781, 2, 3.967, 0.017, 2, 4, -0.629, 2, 4.033, -1.074, 2, 4.067, -1.241, 2, 4.1, -1.097, 2, 4.133, -0.727, 2, 4.167, -0.212, 2, 4.2, 0.333, 2, 4.233, 0.847, 2, 4.267, 1.218, 2, 4.3, 1.361, 2, 4.333, 1.249, 2, 4.367, 0.952, 2, 4.4, 0.533, 2, 4.433, 0.052, 2, 4.467, -0.429, 2, 4.5, -0.849, 2, 4.533, -1.146, 2, 4.567, -1.258, 2, 4.6, -1.213, 2, 4.633, -1.092, 2, 4.667, -0.908, 2, 4.7, -0.685, 2, 4.733, -0.431, 2, 4.767, -0.175, 2, 4.8, 0.079, 2, 4.833, 0.302, 2, 4.867, 0.486, 2, 4.9, 0.607, 2, 4.933, 0.652, 2, 4.967, 0.631, 2, 5, 0.573, 2, 5.033, 0.486, 2, 5.067, 0.38, 2, 5.1, 0.26, 2, 5.133, 0.139, 2, 5.167, 0.018, 2, 5.2, -0.087, 2, 5.233, -0.174, 2, 5.267, -0.232, 2, 5.3, -0.253, 2, 5.333, -0.245, 2, 5.367, -0.224, 2, 5.4, -0.191, 2, 5.433, -0.151, 2, 5.467, -0.106, 2, 5.5, -0.061, 2, 5.533, -0.015, 2, 5.567, 0.024, 2, 5.6, 0.057, 2, 5.633, 0.078, 2, 5.667, 0.086, 2, 5.7, 0.084, 2, 5.733, 0.077, 2, 5.767, 0.066, 2, 5.8, 0.052, 2, 5.833, 0.037, 2, 5.867, 0.022, 2, 5.9, 0.007, 2, 5.933, -0.006, 2, 5.967, -0.017, 2, 6, -0.025, 2, 6.033, -0.027, 2, 6.067, -0.026, 2, 6.1, -0.024, 2, 6.133, -0.021, 2, 6.167, -0.017, 2, 6.2, -0.012, 2, 6.233, -0.007, 2, 6.267, -0.002, 2, 6.3, 0.002, 2, 6.333, 0.005, 2, 6.367, 0.007, 2, 6.4, 0.008, 2, 6.433, 0.008, 2, 6.467, 0.007, 2, 6.5, 0.007, 2, 6.533, 0.005, 2, 6.567, 0.004, 2, 6.6, 0.003, 2, 6.633, 0.002, 2, 6.667, 0, 2, 6.7, -0.001, 2, 6.733, -0.002, 2, 6.767, -0.002, 2, 6.8, -0.002, 2, 6.833, -0.002, 2, 6.867, -0.002, 2, 6.9, -0.002, 2, 6.933, -0.001, 2, 6.967, -0.001, 2, 7, -0.001]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh46", "Segments": [0, -0.003, 2, 0.033, -0.019, 2, 0.067, -0.06, 2, 0.1, -0.118, 2, 0.133, -0.185, 2, 0.167, -0.252, 2, 0.2, -0.311, 2, 0.233, -0.352, 2, 0.267, -0.368, 2, 0.3, -0.318, 2, 0.333, -0.181, 2, 0.367, 0.024, 2, 0.4, 0.268, 2, 0.433, 0.534, 2, 0.467, 0.8, 2, 0.5, 1.044, 2, 0.533, 1.249, 2, 0.567, 1.385, 2, 0.6, 1.435, 2, 0.633, 1.371, 2, 0.667, 1.198, 2, 0.7, 0.93, 2, 0.733, 0.595, 2, 0.767, 0.223, 2, 0.8, -0.18, 2, 0.833, -0.583, 2, 0.867, -0.955, 2, 0.9, -1.291, 2, 0.933, -1.558, 2, 0.967, -1.731, 2, 1, -1.795, 2, 1.033, -1.74, 2, 1.067, -1.582, 2, 1.1, -1.345, 2, 1.133, -1.042, 2, 1.167, -0.693, 2, 1.2, -0.321, 2, 1.233, 0.069, 2, 1.267, 0.44, 2, 1.3, 0.789, 2, 1.333, 1.093, 2, 1.367, 1.329, 2, 1.4, 1.488, 2, 1.433, 1.543, 2, 1.467, 1.492, 2, 1.5, 1.356, 2, 1.533, 1.145, 2, 1.567, 0.881, 2, 1.6, 0.588, 2, 1.633, 0.27, 2, 1.667, -0.047, 2, 1.7, -0.34, 2, 1.733, -0.604, 2, 1.767, -0.815, 2, 1.8, -0.951, 2, 1.833, -1.002, 2, 1.867, -0.949, 2, 1.9, -0.811, 2, 1.933, -0.62, 2, 1.967, -0.417, 2, 2, -0.226, 2, 2.033, -0.088, 2, 2.067, -0.034, 2, 2.1, -0.098, 2, 2.133, -0.25, 2, 2.167, -0.436, 2, 2.2, -0.588, 2, 2.233, -0.652, 2, 2.267, -0.479, 2, 2.3, -0.023, 2, 2.333, 0.621, 2, 2.367, 1.36, 2, 2.4, 2.099, 2, 2.433, 2.743, 2, 2.467, 3.199, 2, 2.5, 3.372, 2, 2.533, 3.124, 2, 2.567, 2.451, 2, 2.6, 1.436, 2, 2.633, 0.227, 2, 2.667, -1.089, 2, 2.7, -2.404, 2, 2.733, -3.613, 2, 2.767, -4.628, 2, 2.8, -5.301, 2, 2.833, -5.549, 2, 2.867, -5.247, 2, 2.9, -4.442, 2, 2.933, -3.216, 2, 2.967, -1.732, 2, 3, -0.043, 2, 3.033, 1.667, 2, 3.067, 3.356, 2, 3.1, 4.84, 2, 3.133, 6.066, 2, 3.167, 6.871, 2, 3.2, 7.173, 2, 3.233, 6.782, 2, 3.267, 5.719, 2, 3.3, 4.118, 2, 3.333, 2.209, 2, 3.367, 0.133, 2, 3.4, -1.943, 2, 3.433, -3.852, 2, 3.467, -5.453, 2, 3.5, -6.516, 2, 3.533, -6.907, 2, 3.567, -6.629, 2, 3.6, -5.888, 2, 3.633, -4.758, 2, 3.667, -3.392, 2, 3.7, -1.837, 2, 3.733, -0.263, 2, 3.767, 1.293, 2, 3.8, 2.659, 2, 3.833, 3.788, 2, 3.867, 4.529, 2, 3.9, 4.807, 2, 3.933, 4.624, 2, 3.967, 4.127, 2, 4, 3.378, 2, 4.033, 2.484, 2, 4.067, 1.513, 2, 4.1, 0.541, 2, 4.133, -0.352, 2, 4.167, -1.101, 2, 4.2, -1.598, 2, 4.233, -1.782, 2, 4.267, -1.671, 2, 4.3, -1.378, 2, 4.333, -0.964, 2, 4.367, -0.49, 2, 4.4, -0.015, 2, 4.433, 0.399, 2, 4.467, 0.691, 2, 4.5, 0.802, 2, 4.533, 0.722, 2, 4.567, 0.511, 2, 4.6, 0.211, 2, 4.633, -0.132, 2, 4.667, -0.474, 2, 4.7, -0.774, 2, 4.733, -0.985, 2, 4.767, -1.065, 2, 4.8, -1.013, 2, 4.833, -0.869, 2, 4.867, -0.654, 2, 4.9, -0.396, 2, 4.933, -0.116, 2, 4.967, 0.164, 2, 5, 0.421, 2, 5.033, 0.637, 2, 5.067, 0.78, 2, 5.1, 0.833, 2, 5.133, 0.803, 2, 5.167, 0.722, 2, 5.2, 0.6, 2, 5.233, 0.452, 2, 5.267, 0.283, 2, 5.3, 0.112, 2, 5.333, -0.057, 2, 5.367, -0.205, 2, 5.4, -0.327, 2, 5.433, -0.408, 2, 5.467, -0.438, 2, 5.5, -0.423, 2, 5.533, -0.383, 2, 5.567, -0.323, 2, 5.6, -0.25, 2, 5.633, -0.167, 2, 5.667, -0.082, 2, 5.7, 0.001, 2, 5.733, 0.074, 2, 5.767, 0.134, 2, 5.8, 0.174, 2, 5.833, 0.189, 2, 5.867, 0.183, 2, 5.9, 0.166, 2, 5.933, 0.141, 2, 5.967, 0.11, 2, 6, 0.076, 2, 6.033, 0.041, 2, 6.067, 0.006, 2, 6.1, -0.024, 2, 6.133, -0.05, 2, 6.167, -0.066, 2, 6.2, -0.072, 2, 6.233, -0.07, 2, 6.267, -0.064, 2, 6.3, -0.054, 2, 6.333, -0.043, 2, 6.367, -0.03, 2, 6.4, -0.017, 2, 6.433, -0.004, 2, 6.467, 0.007, 2, 6.5, 0.017, 2, 6.533, 0.023, 2, 6.567, 0.025, 2, 6.6, 0.025, 2, 6.633, 0.022, 2, 6.667, 0.019, 2, 6.7, 0.015, 2, 6.733, 0.011, 2, 6.767, 0.006, 2, 6.8, 0.002, 2, 6.833, -0.002, 2, 6.867, -0.005, 2, 6.9, -0.008, 2, 6.933, -0.008, 2, 7, -0.008]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh46", "Segments": [0, 0, 2, 0.033, -0.01, 2, 0.067, -0.036, 2, 0.1, -0.076, 2, 0.133, -0.125, 2, 0.167, -0.181, 2, 0.2, -0.237, 2, 0.233, -0.293, 2, 0.267, -0.341, 2, 0.3, -0.382, 2, 0.333, -0.408, 2, 0.367, -0.418, 2, 0.4, -0.361, 2, 0.433, -0.208, 2, 0.467, 0.025, 2, 0.5, 0.307, 2, 0.533, 0.627, 2, 0.567, 0.952, 2, 0.6, 1.272, 2, 0.633, 1.554, 2, 0.667, 1.787, 2, 0.7, 1.94, 2, 0.733, 1.997, 2, 0.767, 1.896, 2, 0.8, 1.627, 2, 0.833, 1.211, 2, 0.867, 0.688, 2, 0.9, 0.109, 2, 0.933, -0.519, 2, 0.967, -1.147, 2, 1, -1.726, 2, 1.033, -2.249, 2, 1.067, -2.665, 2, 1.1, -2.935, 2, 1.133, -3.035, 2, 1.167, -2.939, 2, 1.2, -2.661, 2, 1.233, -2.248, 2, 1.267, -1.719, 2, 1.3, -1.109, 2, 1.333, -0.46, 2, 1.367, 0.223, 2, 1.4, 0.872, 2, 1.433, 1.482, 2, 1.467, 2.011, 2, 1.5, 2.424, 2, 1.533, 2.702, 2, 1.567, 2.798, 2, 1.6, 2.703, 2, 1.633, 2.446, 2, 1.667, 2.05, 2, 1.7, 1.552, 2, 1.733, 1.001, 2, 1.767, 0.403, 2, 1.8, -0.195, 2, 1.833, -0.746, 2, 1.867, -1.244, 2, 1.9, -1.64, 2, 1.933, -1.897, 2, 1.967, -1.992, 2, 2, -1.944, 2, 2.033, -1.804, 2, 2.067, -1.584, 2, 2.1, -1.294, 2, 2.133, -0.946, 2, 2.167, -0.558, 2, 2.2, -0.125, 2, 2.233, 0.334, 2, 2.267, 0.81, 2, 2.3, 1.291, 2, 2.333, 1.767, 2, 2.367, 2.226, 2, 2.4, 2.659, 2, 2.433, 3.047, 2, 2.467, 3.395, 2, 2.5, 3.685, 2, 2.533, 3.905, 2, 2.567, 4.045, 2, 2.6, 4.094, 2, 2.633, 3.801, 2, 2.667, 3.023, 2, 2.7, 1.836, 2, 2.733, 0.401, 2, 2.767, -1.233, 2, 2.8, -2.888, 2, 2.833, -4.522, 2, 2.867, -5.957, 2, 2.9, -7.144, 2, 2.933, -7.922, 2, 2.967, -8.215, 2, 3, -7.76, 2, 3.033, -6.55, 2, 3.067, -4.705, 2, 3.1, -2.473, 2, 3.133, 0.067, 2, 3.167, 2.639, 2, 3.2, 5.18, 2, 3.233, 7.412, 2, 3.267, 9.257, 2, 3.3, 10.467, 2, 3.333, 10.921, 2, 3.367, 10.304, 2, 3.4, 8.626, 2, 3.433, 6.097, 2, 3.467, 3.082, 2, 3.5, -0.196, 2, 3.533, -3.474, 2, 3.567, -6.488, 2, 3.6, -9.017, 2, 3.633, -10.695, 2, 3.667, -11.313, 2, 3.7, -10.917, 2, 3.733, -9.856, 2, 3.767, -8.216, 2, 3.8, -6.157, 2, 3.833, -3.876, 2, 3.867, -1.403, 2, 3.9, 1.07, 2, 3.933, 3.351, 2, 3.967, 5.41, 2, 4, 7.049, 2, 4.033, 8.111, 2, 4.067, 8.507, 2, 4.1, 8.168, 2, 4.133, 7.25, 2, 4.167, 5.865, 2, 4.2, 4.215, 2, 4.233, 2.42, 2, 4.267, 0.625, 2, 4.3, -1.025, 2, 4.333, -2.41, 2, 4.367, -3.328, 2, 4.4, -3.667, 2, 4.433, -3.51, 2, 4.467, -3.091, 2, 4.5, -2.484, 2, 4.533, -1.764, 2, 4.567, -1.021, 2, 4.6, -0.302, 2, 4.633, 0.305, 2, 4.667, 0.725, 2, 4.7, 0.881, 2, 4.733, 0.78, 2, 4.767, 0.517, 2, 4.8, 0.152, 2, 4.833, -0.235, 2, 4.867, -0.6, 2, 4.9, -0.863, 2, 4.933, -0.964, 2, 4.967, -0.904, 2, 5, -0.74, 2, 5.033, -0.493, 2, 5.067, -0.199, 2, 5.1, 0.122, 2, 5.133, 0.442, 2, 5.167, 0.736, 2, 5.2, 0.984, 2, 5.233, 1.147, 2, 5.267, 1.208, 2, 5.3, 1.151, 2, 5.333, 0.996, 2, 5.367, 0.764, 2, 5.4, 0.486, 2, 5.433, 0.184, 2, 5.467, -0.118, 2, 5.5, -0.395, 2, 5.533, -0.628, 2, 5.567, -0.783, 2, 5.6, -0.839, 2, 5.633, -0.814, 2, 5.667, -0.745, 2, 5.7, -0.639, 2, 5.733, -0.506, 2, 5.767, -0.358, 2, 5.8, -0.198, 2, 5.833, -0.038, 2, 5.867, 0.11, 2, 5.9, 0.243, 2, 5.933, 0.35, 2, 5.967, 0.418, 2, 6, 0.444, 2, 6.033, 0.429, 2, 6.067, 0.388, 2, 6.1, 0.326, 2, 6.133, 0.25, 2, 6.167, 0.165, 2, 6.2, 0.078, 2, 6.233, -0.008, 2, 6.267, -0.083, 2, 6.3, -0.145, 2, 6.333, -0.186, 2, 6.367, -0.201, 2, 6.4, -0.194, 2, 6.433, -0.177, 2, 6.467, -0.149, 2, 6.5, -0.116, 2, 6.533, -0.079, 2, 6.567, -0.041, 2, 6.6, -0.003, 2, 6.633, 0.03, 2, 6.667, 0.057, 2, 6.7, 0.075, 2, 6.733, 0.082, 2, 6.767, 0.079, 2, 6.8, 0.072, 2, 6.833, 0.061, 2, 6.867, 0.048, 2, 6.9, 0.033, 2, 6.933, 0.018, 2, 6.967, 0.003, 2, 7, -0.01]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh46", "Segments": [0, 0.08, 2, 0.033, 0.074, 2, 0.067, 0.055, 2, 0.1, 0.025, 2, 0.133, -0.012, 2, 0.167, -0.056, 2, 0.2, -0.103, 2, 0.233, -0.153, 2, 0.267, -0.202, 2, 0.3, -0.25, 2, 0.333, -0.294, 2, 0.367, -0.331, 2, 0.4, -0.36, 2, 0.433, -0.379, 2, 0.467, -0.386, 2, 0.5, -0.327, 2, 0.533, -0.168, 2, 0.567, 0.073, 2, 0.6, 0.365, 2, 0.633, 0.697, 2, 0.667, 1.033, 2, 0.7, 1.365, 2, 0.733, 1.657, 2, 0.767, 1.898, 2, 0.8, 2.056, 2, 0.833, 2.116, 2, 0.867, 2.02, 2, 0.9, 1.745, 2, 0.933, 1.334, 2, 0.967, 0.808, 2, 1, 0.203, 2, 1.033, -0.442, 2, 1.067, -1.12, 2, 1.1, -1.765, 2, 1.133, -2.371, 2, 1.167, -2.897, 2, 1.2, -3.307, 2, 1.233, -3.583, 2, 1.267, -3.678, 2, 1.3, -3.531, 2, 1.333, -3.135, 2, 1.367, -2.524, 2, 1.4, -1.756, 2, 1.433, -0.906, 2, 1.467, 0.016, 2, 1.5, 0.938, 2, 1.533, 1.789, 2, 1.567, 2.556, 2, 1.6, 3.167, 2, 1.633, 3.563, 2, 1.667, 3.711, 2, 1.7, 3.602, 2, 1.733, 3.289, 2, 1.767, 2.823, 2, 1.8, 2.226, 2, 1.833, 1.538, 2, 1.867, 0.805, 2, 1.9, 0.035, 2, 1.933, -0.697, 2, 1.967, -1.385, 2, 2, -1.983, 2, 2.033, -2.449, 2, 2.067, -2.762, 2, 2.1, -2.87, 2, 2.133, -2.809, 2, 2.167, -2.634, 2, 2.2, -2.36, 2, 2.233, -2.001, 2, 2.267, -1.58, 2, 2.3, -1.095, 2, 2.333, -0.567, 2, 2.367, -0.011, 2, 2.4, 0.56, 2, 2.433, 1.13, 2, 2.467, 1.686, 2, 2.5, 2.214, 2, 2.533, 2.699, 2, 2.567, 3.121, 2, 2.6, 3.48, 2, 2.633, 3.753, 2, 2.667, 3.928, 2, 2.7, 3.99, 2, 2.733, 3.685, 2, 2.767, 2.874, 2, 2.8, 1.638, 2, 2.833, 0.142, 2, 2.867, -1.56, 2, 2.9, -3.284, 2, 2.933, -4.986, 2, 2.967, -6.481, 2, 3, -7.718, 2, 3.033, -8.529, 2, 3.067, -8.833, 2, 3.1, -8.329, 2, 3.133, -6.986, 2, 3.167, -4.938, 2, 3.2, -2.462, 2, 3.233, 0.357, 2, 3.267, 3.211, 2, 3.3, 6.03, 2, 3.333, 8.507, 2, 3.367, 10.554, 2, 3.4, 11.897, 2, 3.433, 12.401, 2, 3.467, 11.785, 2, 3.5, 10.141, 2, 3.533, 7.636, 2, 3.567, 4.606, 2, 3.6, 1.157, 2, 3.633, -2.335, 2, 3.667, -5.785, 2, 3.7, -8.815, 2, 3.733, -11.32, 2, 3.767, -12.964, 2, 3.8, -13.58, 2, 3.833, -13.087, 2, 3.867, -11.765, 2, 3.9, -9.723, 2, 3.933, -7.159, 2, 3.967, -4.318, 2, 4, -1.238, 2, 4.033, 1.843, 2, 4.067, 4.683, 2, 4.1, 7.248, 2, 4.133, 9.289, 2, 4.167, 10.612, 2, 4.2, 11.105, 2, 4.233, 10.706, 2, 4.267, 9.643, 2, 4.3, 8.024, 2, 4.333, 6.065, 2, 4.367, 3.835, 2, 4.4, 1.577, 2, 4.433, -0.653, 2, 4.467, -2.612, 2, 4.5, -4.232, 2, 4.533, -5.295, 2, 4.567, -5.693, 2, 4.6, -5.492, 2, 4.633, -4.946, 2, 4.667, -4.124, 2, 4.7, -3.143, 2, 4.733, -2.076, 2, 4.767, -1.01, 2, 4.8, -0.029, 2, 4.833, 0.794, 2, 4.867, 1.339, 2, 4.9, 1.54, 2, 4.933, 1.453, 2, 4.967, 1.224, 2, 5, 0.899, 2, 5.033, 0.527, 2, 5.067, 0.156, 2, 5.1, -0.169, 2, 5.133, -0.398, 2, 5.167, -0.485, 2, 5.2, -0.419, 2, 5.233, -0.242, 2, 5.267, 0.008, 2, 5.3, 0.294, 2, 5.333, 0.58, 2, 5.367, 0.83, 2, 5.4, 1.007, 2, 5.433, 1.074, 2, 5.467, 1.014, 2, 5.5, 0.853, 2, 5.533, 0.61, 2, 5.567, 0.321, 2, 5.6, 0.006, 2, 5.633, -0.309, 2, 5.667, -0.598, 2, 5.7, -0.841, 2, 5.733, -1.002, 2, 5.767, -1.062, 2, 5.8, -1.02, 2, 5.833, -0.909, 2, 5.867, -0.74, 2, 5.9, -0.536, 2, 5.933, -0.303, 2, 5.967, -0.067, 2, 6, 0.165, 2, 6.033, 0.37, 2, 6.067, 0.539, 2, 6.1, 0.65, 2, 6.133, 0.691, 2, 6.167, 0.666, 2, 6.2, 0.6, 2, 6.233, 0.498, 2, 6.267, 0.375, 2, 6.3, 0.235, 2, 6.333, 0.093, 2, 6.367, -0.046, 2, 6.4, -0.169, 2, 6.433, -0.271, 2, 6.467, -0.338, 2, 6.5, -0.363, 2, 6.533, -0.352, 2, 6.567, -0.324, 2, 6.6, -0.28, 2, 6.633, -0.225, 2, 6.667, -0.164, 2, 6.7, -0.098, 2, 6.733, -0.032, 2, 6.767, 0.029, 2, 6.8, 0.084, 2, 6.833, 0.128, 2, 6.867, 0.157, 2, 6.9, 0.167, 2, 6.933, 0.162, 2, 6.967, 0.147, 2, 7, 0.124]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh46", "Segments": [0, 0.204, 2, 0.033, 0.237, 2, 0.067, 0.269, 2, 0.1, 0.258, 2, 0.133, 0.226, 2, 0.167, 0.177, 2, 0.2, 0.116, 2, 0.233, 0.042, 2, 0.267, -0.037, 2, 0.3, -0.119, 2, 0.333, -0.202, 2, 0.367, -0.281, 2, 0.4, -0.354, 2, 0.433, -0.415, 2, 0.467, -0.464, 2, 0.5, -0.496, 2, 0.533, -0.507, 2, 0.567, -0.452, 2, 0.6, -0.294, 2, 0.633, -0.058, 2, 0.667, 0.245, 2, 0.7, 0.594, 2, 0.733, 0.965, 2, 0.767, 1.355, 2, 0.8, 1.727, 2, 0.833, 2.075, 2, 0.867, 2.378, 2, 0.9, 2.614, 2, 0.933, 2.773, 2, 0.967, 2.828, 2, 1, 2.661, 2, 1.033, 2.215, 2, 1.067, 1.527, 2, 1.1, 0.662, 2, 1.133, -0.297, 2, 1.167, -1.336, 2, 1.2, -2.375, 2, 1.233, -3.334, 2, 1.267, -4.199, 2, 1.3, -4.888, 2, 1.333, -5.334, 2, 1.367, -5.5, 2, 1.4, -5.309, 2, 1.433, -4.758, 2, 1.467, -3.939, 2, 1.5, -2.888, 2, 1.533, -1.679, 2, 1.567, -0.39, 2, 1.6, 0.963, 2, 1.633, 2.251, 2, 1.667, 3.461, 2, 1.7, 4.512, 2, 1.733, 5.331, 2, 1.767, 5.881, 2, 1.8, 6.073, 2, 1.833, 5.888, 2, 1.867, 5.358, 2, 1.9, 4.568, 2, 1.933, 3.554, 2, 1.967, 2.388, 2, 2, 1.145, 2, 2.033, -0.16, 2, 2.067, -1.402, 2, 2.1, -2.569, 2, 2.133, -3.582, 2, 2.167, -4.372, 2, 2.2, -4.903, 2, 2.233, -5.087, 2, 2.267, -4.985, 2, 2.3, -4.693, 2, 2.333, -4.23, 2, 2.367, -3.635, 2, 2.4, -2.926, 2, 2.433, -2.116, 2, 2.467, -1.256, 2, 2.5, -0.358, 2, 2.533, 0.552, 2, 2.567, 1.45, 2, 2.6, 2.31, 2, 2.633, 3.12, 2, 2.667, 3.828, 2, 2.7, 4.424, 2, 2.733, 4.887, 2, 2.767, 5.178, 2, 2.8, 5.281, 2, 2.833, 4.938, 2, 2.867, 4.02, 2, 2.9, 2.602, 2, 2.933, 0.821, 2, 2.967, -1.152, 2, 3, -3.292, 2, 3.033, -5.431, 2, 3.067, -7.404, 2, 3.1, -9.185, 2, 3.133, -10.604, 2, 3.167, -11.522, 2, 3.2, -11.864, 2, 3.233, -11.173, 2, 3.267, -9.332, 2, 3.3, -6.525, 2, 3.333, -3.131, 2, 3.367, 0.734, 2, 3.4, 4.646, 2, 3.433, 8.511, 2, 3.467, 11.906, 2, 3.5, 14.712, 2, 3.533, 16.554, 2, 3.567, 17.245, 2, 3.6, 16.367, 2, 3.633, 14.027, 2, 3.667, 10.46, 2, 3.7, 6.146, 2, 3.733, 1.235, 2, 3.767, -3.737, 2, 3.8, -8.648, 2, 3.833, -12.962, 2, 3.867, -16.528, 2, 3.9, -18.869, 2, 3.933, -19.747, 2, 3.967, -19.008, 2, 4, -17.027, 2, 4.033, -13.969, 2, 4.067, -10.127, 2, 4.1, -5.872, 2, 4.133, -1.257, 2, 4.167, 3.358, 2, 4.2, 7.613, 2, 4.233, 11.454, 2, 4.267, 14.513, 2, 4.3, 16.494, 2, 4.333, 17.232, 2, 4.367, 16.577, 2, 4.4, 14.831, 2, 4.433, 12.168, 2, 4.467, 8.949, 2, 4.5, 5.283, 2, 4.533, 1.573, 2, 4.567, -2.093, 2, 4.6, -5.313, 2, 4.633, -7.975, 2, 4.667, -9.721, 2, 4.7, -10.377, 2, 4.733, -10.039, 2, 4.767, -9.14, 2, 4.8, -7.77, 2, 4.833, -6.113, 2, 4.867, -4.226, 2, 4.9, -2.316, 2, 4.933, -0.43, 2, 4.967, 1.227, 2, 5, 2.598, 2, 5.033, 3.497, 2, 5.067, 3.834, 2, 5.1, 3.682, 2, 5.133, 3.277, 2, 5.167, 2.69, 2, 5.2, 1.994, 2, 5.233, 1.276, 2, 5.267, 0.58, 2, 5.3, -0.007, 2, 5.333, -0.413, 2, 5.367, -0.564, 2, 5.4, -0.488, 2, 5.433, -0.291, 2, 5.467, -0.018, 2, 5.5, 0.271, 2, 5.533, 0.544, 2, 5.567, 0.741, 2, 5.6, 0.817, 2, 5.633, 0.755, 2, 5.667, 0.586, 2, 5.7, 0.331, 2, 5.733, 0.027, 2, 5.767, -0.303, 2, 5.8, -0.634, 2, 5.833, -0.937, 2, 5.867, -1.192, 2, 5.9, -1.361, 2, 5.933, -1.424, 2, 5.967, -1.362, 2, 6, -1.197, 2, 6.033, -0.946, 2, 6.067, -0.642, 2, 6.1, -0.297, 2, 6.133, 0.054, 2, 6.167, 0.399, 2, 6.2, 0.703, 2, 6.233, 0.954, 2, 6.267, 1.119, 2, 6.3, 1.181, 2, 6.333, 1.135, 2, 6.367, 1.014, 2, 6.4, 0.83, 2, 6.433, 0.607, 2, 6.467, 0.353, 2, 6.5, 0.095, 2, 6.533, -0.159, 2, 6.567, -0.382, 2, 6.6, -0.566, 2, 6.633, -0.687, 2, 6.667, -0.733, 2, 6.7, -0.706, 2, 6.733, -0.636, 2, 6.767, -0.528, 2, 6.8, -0.398, 2, 6.833, -0.249, 2, 6.867, -0.099, 2, 6.9, 0.049, 2, 6.933, 0.179, 2, 6.967, 0.287, 2, 7, 0.358]}, {"Target": "PartOpacity", "Id": "Part", "Segments": [0, 1, 0, 7, 1]}, {"Target": "PartOpacity", "Id": "ArtMesh0_Skinning", "Segments": [0, 1, 0, 7, 1]}, {"Target": "PartOpacity", "Id": "Part8", "Segments": [0, 1, 0, 7, 1]}, {"Target": "PartOpacity", "Id": "ArtMesh1_Skinning", "Segments": [0, 1, 0, 7, 1]}, {"Target": "PartOpacity", "Id": "Part9", "Segments": [0, 1, 0, 7, 1]}, {"Target": "PartOpacity", "Id": "ArtMesh2_Skinning", "Segments": [0, 1, 0, 7, 1]}, {"Target": "PartOpacity", "Id": "Part10", "Segments": [0, 1, 0, 7, 1]}, {"Target": "PartOpacity", "Id": "ArtMesh3_Skinning", "Segments": [0, 1, 0, 7, 1]}, {"Target": "PartOpacity", "Id": "Part11", "Segments": [0, 1, 0, 7, 1]}, {"Target": "PartOpacity", "Id": "Part2", "Segments": [0, 1, 0, 7, 1]}, {"Target": "PartOpacity", "Id": "Part3", "Segments": [0, 1, 0, 7, 1]}, {"Target": "PartOpacity", "Id": "face", "Segments": [0, 1, 0, 7, 1]}, {"Target": "PartOpacity", "Id": "mouth", "Segments": [0, 1, 0, 7, 1]}, {"Target": "PartOpacity", "Id": "Part4", "Segments": [0, 1, 0, 7, 1]}, {"Target": "PartOpacity", "Id": "Part5", "Segments": [0, 1, 0, 7, 1]}, {"Target": "PartOpacity", "Id": "Part6", "Segments": [0, 1, 0, 7, 1]}, {"Target": "PartOpacity", "Id": "Part7", "Segments": [0, 1, 0, 7, 1]}, {"Target": "PartOpacity", "Id": "ArtMesh46_Skinning", "Segments": [0, 1, 0, 7, 1]}, {"Target": "PartOpacity", "Id": "Part12", "Segments": [0, 1, 0, 7, 1]}]}