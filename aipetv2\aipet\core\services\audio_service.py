"""
音频服务

提供统一的音频播放功能，支持多种音频格式和播放方式
"""

import asyncio
import logging
import tempfile
import os
from typing import Optional, Union
from pathlib import Path

try:
    from pydub import AudioSegment
    PYDUB_AVAILABLE = True
except ImportError:
    PYDUB_AVAILABLE = False
    AudioSegment = None

from aipet.core.foundation import SystemFoundation
from .base import BaseService, ServiceResult

logger = logging.getLogger(__name__)


class AudioService(BaseService):
    """
    音频服务
    
    负责音频播放、格式转换和音频文件管理
    """
    
    def __init__(self, foundation: SystemFoundation):
        super().__init__()
        self.foundation = foundation
        self.audio_player = None
        self._temp_files = []
        
        logger.info("✅ 音频服务初始化完成")
    
    async def _initialize_impl(self):
        """实现BaseService的抽象方法"""
        # 获取音频播放器
        self.audio_player = self.foundation.get_service("audio_player")

        if not self.audio_player:
            # 如果没有专用的音频播放器，创建一个
            from aipet.infrastructure.audio import AudioPlayer
            self.audio_player = AudioPlayer()
            await self.audio_player.initialize()

            # 注册到SystemFoundation
            service_registry = self.foundation.get_service("service_registry")
            service_registry.register_service("audio_player", self.audio_player)
    
    async def play_audio(self, audio_data: Union[bytes, str, Path]) -> Optional[float]:
        """
        播放音频
        
        Args:
            audio_data: 音频数据（字节流、文件路径或Path对象）
            
        Returns:
            Optional[float]: 播放时长（秒），如果播放失败返回None
        """
        try:
            if isinstance(audio_data, bytes):
                # 字节流数据，保存为临时文件
                audio_file = await self._save_temp_audio(audio_data)
            elif isinstance(audio_data, (str, Path)):
                # 文件路径
                audio_file = Path(audio_data)
                if not audio_file.exists():
                    raise FileNotFoundError(f"音频文件不存在: {audio_file}")
            else:
                raise ValueError(f"不支持的音频数据类型: {type(audio_data)}")
            
            # 使用音频播放器播放
            duration = await self.audio_player.play(audio_file)
            
            logger.info(f"✅ 音频播放完成，时长: {duration:.2f}秒")
            return duration
            
        except Exception as e:
            logger.error(f"❌ 音频播放失败: {e}")
            return None

    async def play_for_live2d(self, audio_data: bytes) -> tuple[Optional[float], Optional[str]]:
        """
        为Live2D播放音频（支持口型同步）

        Args:
            audio_data: 音频数据字节

        Returns:
            tuple[Optional[float], Optional[str]]: (播放时长, WAV文件路径用于口型同步)
        """
        try:
            if not self.audio_player:
                logger.error("音频播放器未初始化")
                return None, None

            # 使用音频播放器的Live2D播放功能
            if hasattr(self.audio_player, 'play_for_live2d'):
                return await self.audio_player.play_for_live2d(audio_data)
            else:
                # 降级到普通播放
                logger.warning("音频播放器不支持Live2D播放，降级到普通播放")
                duration = await self.play_audio(audio_data)
                return duration, None

        except Exception as e:
            logger.error(f"Live2D音频播放失败: {e}")
            return None, None

    async def play_audio_data(self, audio_data: bytes, optimize_for: str = "tts") -> Optional[float]:
        """
        播放音频数据（支持格式检测和转换）

        Args:
            audio_data: 音频数据字节
            optimize_for: 优化目标 ("tts", "live2d", "general")

        Returns:
            Optional[float]: 播放时长（秒），如果播放失败返回None
        """
        try:
            if not self.audio_player:
                logger.error("音频播放器未初始化")
                return None

            # 使用音频播放器的增强播放功能
            if hasattr(self.audio_player, 'play_audio_data'):
                return await self.audio_player.play_audio_data(audio_data, optimize_for)
            else:
                # 降级到普通播放
                return await self.play_audio(audio_data)

        except Exception as e:
            logger.error(f"音频数据播放失败: {e}")
            return None

    async def _save_temp_audio(self, audio_data: bytes, format: str = "mp3") -> Path:
        """
        保存临时音频文件，并转换为WAV格式以提高兼容性

        Args:
            audio_data: 音频字节流
            format: 原始音频格式

        Returns:
            Path: 临时文件路径（WAV格式）
        """
        try:
            # 先保存原始格式的临时文件
            temp_fd, temp_path = tempfile.mkstemp(suffix=f".{format}", prefix="tts_audio_")

            # 写入音频数据
            with os.fdopen(temp_fd, 'wb') as f:
                f.write(audio_data)

            original_file = Path(temp_path)

            # 如果pydub可用且原始格式不是WAV，则转换为WAV
            if PYDUB_AVAILABLE and format.lower() != "wav":
                try:
                    # 转换为WAV格式
                    wav_file = await self._convert_to_wav(original_file)

                    # 删除原始文件
                    original_file.unlink(missing_ok=True)

                    # 使用转换后的WAV文件
                    temp_file = wav_file
                    logger.debug(f"音频已转换为WAV格式: {temp_file}")

                except Exception as e:
                    logger.warning(f"音频格式转换失败，使用原始格式: {e}")
                    temp_file = original_file
            else:
                temp_file = original_file

            self._temp_files.append(temp_file)

            # 清理旧的临时文件
            await self._cleanup_temp_files()

            logger.debug(f"保存临时音频文件: {temp_file}")
            return temp_file

        except Exception as e:
            logger.error(f"保存临时音频文件失败: {e}")
            raise

    async def _convert_to_wav(self, audio_file: Path) -> Path:
        """
        将音频文件转换为WAV格式

        Args:
            audio_file: 原始音频文件路径

        Returns:
            Path: 转换后的WAV文件路径
        """
        try:
            # 加载音频文件
            audio = AudioSegment.from_file(str(audio_file))

            # 创建WAV格式的临时文件
            wav_fd, wav_path = tempfile.mkstemp(suffix=".wav", prefix="tts_audio_")
            os.close(wav_fd)  # 关闭文件描述符，让pydub写入

            # 导出为WAV格式，设置标准参数
            audio.export(
                wav_path,
                format="wav",
                parameters=[
                    "-acodec", "pcm_s16le",  # 16位PCM编码
                    "-ac", "1",              # 单声道
                    "-ar", "22050"           # 22.05kHz采样率
                ]
            )

            wav_file = Path(wav_path)
            logger.debug(f"音频转换完成: {audio_file} -> {wav_file}")
            return wav_file

        except Exception as e:
            logger.error(f"音频格式转换失败: {e}")
            raise

    async def _cleanup_temp_files(self):
        """清理临时文件"""
        try:
            max_temp_files = int(self.foundation.get_config("TTS_MAX_TEMP_FILES") or "10")
            
            # 如果临时文件过多，删除最旧的文件
            while len(self._temp_files) > max_temp_files:
                old_file = self._temp_files.pop(0)
                try:
                    if old_file.exists():
                        old_file.unlink()
                        logger.debug(f"删除临时音频文件: {old_file}")
                except Exception as e:
                    logger.warning(f"删除临时文件失败: {e}")
                    
        except Exception as e:
            logger.error(f"清理临时文件失败: {e}")
    
    async def stop_playback(self):
        """停止当前播放"""
        try:
            if self.audio_player:
                await self.audio_player.stop()
                logger.info("✅ 音频播放已停止")
        except Exception as e:
            logger.error(f"停止音频播放失败: {e}")
    
    async def is_playing(self) -> bool:
        """检查是否正在播放"""
        try:
            if self.audio_player:
                return await self.audio_player.is_playing()
            return False
        except Exception as e:
            logger.error(f"检查播放状态失败: {e}")
            return False
    
    async def get_volume(self) -> float:
        """获取音量"""
        try:
            if self.audio_player:
                return await self.audio_player.get_volume()
            return 1.0
        except Exception as e:
            logger.error(f"获取音量失败: {e}")
            return 1.0
    
    async def set_volume(self, volume: float):
        """设置音量"""
        try:
            if self.audio_player:
                await self.audio_player.set_volume(volume)
                logger.info(f"设置音量: {volume}")
        except Exception as e:
            logger.error(f"设置音量失败: {e}")
    
    async def cleanup(self):
        """清理资源"""
        try:
            # 停止播放
            await self.stop_playback()
            
            # 清理所有临时文件
            for temp_file in self._temp_files:
                try:
                    if temp_file.exists():
                        temp_file.unlink()
                except Exception as e:
                    logger.warning(f"删除临时文件失败: {e}")
            
            self._temp_files.clear()
            logger.info("✅ 音频服务清理完成")
            
        except Exception as e:
            logger.error(f"音频服务清理失败: {e}")
    
    def __del__(self):
        """析构函数"""
        # 注意：在析构函数中不能使用async，这里只做简单清理
        try:
            for temp_file in self._temp_files:
                if temp_file.exists():
                    temp_file.unlink()
        except:
            pass
