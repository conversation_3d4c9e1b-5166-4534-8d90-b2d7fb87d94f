"""
Live2D集成服务

提供TTS与Live2D的联动功能，包括口型同步和表情控制
基于WebEngine + Live2D Web SDK实现
"""

import asyncio
import logging
from typing import Optional, Dict, Any, List
from pathlib import Path
from PySide6.QtCore import QObject, Signal, Slot

from .base import BaseService, ServiceResult
from ..events import EventBus

logger = logging.getLogger(__name__)


class Live2DService(BaseService):
    """Live2D集成服务 - 基于WebEngine + Live2D Web SDK"""

    # 信号定义
    expression_changed = Signal(str, float)
    motion_started = Signal(str)
    model_loaded = Signal(str)

    def __init__(self, foundation=None):
        super().__init__("Live2DService")
        self.foundation = foundation
        self.event_bus = foundation.get_service("EventBus") if foundation else None

        # Live2D组件引用
        self.live2d_bridge = None
        self.live2d_widget = None

        # 状态管理
        self.is_ready = False
        self.current_model = None
        self.current_expression = "默认"
        self.current_motion = "待机"
        self.is_lipsync_active = False
        self.current_lipsync_file = None

        # 支持的功能
        self.supported_expressions = [
            "默认", "开心", "难过", "生气", "惊讶", "眨眼", "微笑"
        ]
        self.supported_motions = [
            "待机", "挥手", "点头", "摇头", "鞠躬", "跳舞"
        ]

        # Live2D配置
        self.lipsync_sensitivity = 3.0
        self.auto_blink = True
        self.auto_breath = True
        self.model_scale = 1.0
        self.model_position = (0.5, 0.5)
        
    async def _initialize_impl(self):
        """初始化Live2D服务实现"""
        try:
            # 尝试获取Live2D组件
            self.live2d_widget = await self._get_live2d_widget()
            
            if self.live2d_widget:
                # 获取支持的表情和动作列表
                await self._load_supported_features()
                self.logger.info("✅ Live2D服务初始化成功")
            else:
                self.logger.warning("⚠️ Live2D组件未找到，服务将以降级模式运行")
                
        except Exception as e:
            self.logger.error(f"❌ Live2D服务初始化失败: {e}")
            raise
    
    async def _shutdown_impl(self):
        """关闭Live2D服务实现"""
        try:
            # 停止当前的口型同步
            if self.is_lipsync_active:
                await self.stop_lipsync()
            
            self.live2d_widget = None
            self.logger.info("✅ Live2D服务已关闭")
            
        except Exception as e:
            self.logger.error(f"❌ Live2D服务关闭失败: {e}")
            raise
    
    async def _get_live2d_widget(self):
        """获取Live2D组件"""
        try:
            # 尝试从foundation获取Live2D组件
            if self.foundation and hasattr(self.foundation, 'get_live2d_widget'):
                widget = self.foundation.get_live2d_widget()
                if widget:
                    self.logger.debug("从foundation获取Live2D组件")
                    return widget
            
            # 尝试从全局查找Live2D组件
            # 这里需要根据实际的Live2D组件集成方式来实现
            # 暂时返回None，表示Live2D组件不可用
            self.logger.debug("Live2D组件不可用")
            return None
            
        except Exception as e:
            self.logger.error(f"获取Live2D组件失败: {e}")
            return None
    
    async def _load_supported_features(self):
        """加载支持的表情和动作列表"""
        try:
            if not self.live2d_widget:
                return
            
            # 获取支持的表情列表
            if hasattr(self.live2d_widget, 'get_expressions'):
                self.supported_expressions = self.live2d_widget.get_expressions()
                self.logger.debug(f"支持的表情: {self.supported_expressions}")
            
            # 获取支持的动作列表
            if hasattr(self.live2d_widget, 'get_motions'):
                self.supported_motions = self.live2d_widget.get_motions()
                self.logger.debug(f"支持的动作: {self.supported_motions}")
                
        except Exception as e:
            self.logger.warning(f"加载Live2D特性列表失败: {e}")
    
    async def start_lipsync(self, wav_file_path: str, sensitivity: float = None) -> ServiceResult[bool]:
        """
        启动口型同步
        
        Args:
            wav_file_path: WAV音频文件路径
            sensitivity: 口型同步敏感度（可选）
            
        Returns:
            ServiceResult[bool]: 启动结果
        """
        try:
            if not self.live2d_widget:
                return ServiceResult.error_result(
                    "Live2D组件不可用，无法启动口型同步",
                    "LIVE2D_NOT_AVAILABLE"
                )
            
            # 检查文件是否存在
            wav_path = Path(wav_file_path)
            if not wav_path.exists():
                return ServiceResult.error_result(
                    f"音频文件不存在: {wav_file_path}",
                    "AUDIO_FILE_NOT_FOUND"
                )
            
            # 停止当前的口型同步
            if self.is_lipsync_active:
                await self.stop_lipsync()
            
            # 使用指定的敏感度或默认值
            sync_sensitivity = sensitivity if sensitivity is not None else self.lipsync_sensitivity
            
            # 尝试启动口型同步
            success = False
            
            # 方法1：使用start_lipsync_from_file
            if hasattr(self.live2d_widget, 'start_lipsync_from_file'):
                try:
                    success = self.live2d_widget.start_lipsync_from_file(wav_file_path)
                    if success:
                        self.logger.debug("使用start_lipsync_from_file启动口型同步")
                except Exception as e:
                    self.logger.debug(f"start_lipsync_from_file失败: {e}")
            
            # 方法2：使用start_lipsync
            if not success and hasattr(self.live2d_widget, 'start_lipsync'):
                try:
                    success = self.live2d_widget.start_lipsync(wav_file_path, sync_sensitivity)
                    if success:
                        self.logger.debug("使用start_lipsync启动口型同步")
                except Exception as e:
                    self.logger.debug(f"start_lipsync失败: {e}")
            
            if success:
                self.is_lipsync_active = True
                self.current_lipsync_file = wav_file_path
                self.logger.info(f"✅ 口型同步已启动: {wav_file_path}")
                
                return ServiceResult.success_result(
                    data=True,
                    metadata={
                        "wav_file": wav_file_path,
                        "sensitivity": sync_sensitivity
                    }
                )
            else:
                return ServiceResult.error_result(
                    "Live2D组件不支持口型同步方法",
                    "LIPSYNC_METHOD_NOT_SUPPORTED"
                )
                
        except Exception as e:
            self.logger.error(f"启动口型同步时发生异常: {e}")
            return ServiceResult.error_result(
                f"启动口型同步异常: {str(e)}",
                "LIPSYNC_START_ERROR"
            )
    
    async def stop_lipsync(self) -> ServiceResult[bool]:
        """
        停止口型同步
        
        Returns:
            ServiceResult[bool]: 停止结果
        """
        try:
            if not self.live2d_widget or not self.is_lipsync_active:
                return ServiceResult.success_result(data=True)
            
            # 尝试停止口型同步
            if hasattr(self.live2d_widget, 'stop_lipsync'):
                try:
                    self.live2d_widget.stop_lipsync()
                    self.is_lipsync_active = False
                    self.current_lipsync_file = None
                    self.logger.info("✅ 口型同步已停止")
                    
                    return ServiceResult.success_result(data=True)
                except Exception as e:
                    self.logger.error(f"停止口型同步失败: {e}")
                    return ServiceResult.error_result(
                        f"停止口型同步失败: {str(e)}",
                        "LIPSYNC_STOP_ERROR"
                    )
            else:
                # 如果没有stop_lipsync方法，标记为已停止
                self.is_lipsync_active = False
                self.current_lipsync_file = None
                self.logger.warning("Live2D组件没有stop_lipsync方法，标记为已停止")
                return ServiceResult.success_result(data=True)
                
        except Exception as e:
            self.logger.error(f"停止口型同步时发生异常: {e}")
            return ServiceResult.error_result(
                f"停止口型同步异常: {str(e)}",
                "LIPSYNC_STOP_ERROR"
            )
    
    async def set_expression(self, expression_name: str) -> ServiceResult[bool]:
        """
        设置表情
        
        Args:
            expression_name: 表情名称
            
        Returns:
            ServiceResult[bool]: 设置结果
        """
        try:
            if not self.live2d_widget:
                return ServiceResult.error_result(
                    "Live2D组件不可用，无法设置表情",
                    "LIVE2D_NOT_AVAILABLE"
                )
            
            # 检查表情是否支持
            if self.supported_expressions and expression_name not in self.supported_expressions:
                return ServiceResult.error_result(
                    f"不支持的表情: {expression_name}",
                    "EXPRESSION_NOT_SUPPORTED",
                    metadata={"supported_expressions": self.supported_expressions}
                )
            
            # 尝试设置表情
            if hasattr(self.live2d_widget, 'set_expression'):
                try:
                    success = self.live2d_widget.set_expression(expression_name)
                    if success:
                        self.logger.info(f"✅ 表情已设置: {expression_name}")
                        return ServiceResult.success_result(
                            data=True,
                            metadata={"expression": expression_name}
                        )
                    else:
                        return ServiceResult.error_result(
                            f"设置表情失败: {expression_name}",
                            "EXPRESSION_SET_FAILED"
                        )
                except Exception as e:
                    return ServiceResult.error_result(
                        f"设置表情异常: {str(e)}",
                        "EXPRESSION_SET_ERROR"
                    )
            else:
                return ServiceResult.error_result(
                    "Live2D组件不支持表情设置",
                    "EXPRESSION_METHOD_NOT_SUPPORTED"
                )
                
        except Exception as e:
            self.logger.error(f"设置表情时发生异常: {e}")
            return ServiceResult.error_result(
                f"设置表情异常: {str(e)}",
                "EXPRESSION_ERROR"
            )
    
    async def play_motion(self, motion_name: str, priority: int = 1) -> ServiceResult[bool]:
        """
        播放动作
        
        Args:
            motion_name: 动作名称
            priority: 优先级
            
        Returns:
            ServiceResult[bool]: 播放结果
        """
        try:
            if not self.live2d_widget:
                return ServiceResult.error_result(
                    "Live2D组件不可用，无法播放动作",
                    "LIVE2D_NOT_AVAILABLE"
                )
            
            # 检查动作是否支持
            if self.supported_motions and motion_name not in self.supported_motions:
                return ServiceResult.error_result(
                    f"不支持的动作: {motion_name}",
                    "MOTION_NOT_SUPPORTED",
                    metadata={"supported_motions": self.supported_motions}
                )
            
            # 尝试播放动作
            if hasattr(self.live2d_widget, 'play_motion'):
                try:
                    success = self.live2d_widget.play_motion(motion_name, priority)
                    if success:
                        self.logger.info(f"✅ 动作已播放: {motion_name}")
                        return ServiceResult.success_result(
                            data=True,
                            metadata={"motion": motion_name, "priority": priority}
                        )
                    else:
                        return ServiceResult.error_result(
                            f"播放动作失败: {motion_name}",
                            "MOTION_PLAY_FAILED"
                        )
                except Exception as e:
                    return ServiceResult.error_result(
                        f"播放动作异常: {str(e)}",
                        "MOTION_PLAY_ERROR"
                    )
            else:
                return ServiceResult.error_result(
                    "Live2D组件不支持动作播放",
                    "MOTION_METHOD_NOT_SUPPORTED"
                )
                
        except Exception as e:
            self.logger.error(f"播放动作时发生异常: {e}")
            return ServiceResult.error_result(
                f"播放动作异常: {str(e)}",
                "MOTION_ERROR"
            )
    
    async def get_status(self) -> ServiceResult[Dict[str, Any]]:
        """
        获取Live2D服务状态
        
        Returns:
            ServiceResult[Dict]: 服务状态信息
        """
        try:
            status = {
                "service_name": self.name,
                "initialized": self.is_initialized(),
                "live2d_available": self.live2d_widget is not None,
                "lipsync_active": self.is_lipsync_active,
                "current_lipsync_file": self.current_lipsync_file,
                "supported_expressions": self.supported_expressions,
                "supported_motions": self.supported_motions,
                "config": {
                    "lipsync_sensitivity": self.lipsync_sensitivity,
                    "auto_blink": self.auto_blink,
                    "auto_breath": self.auto_breath
                }
            }
            
            return ServiceResult.success_result(data=status)
            
        except Exception as e:
            self.logger.error(f"获取Live2D服务状态失败: {e}")
            return ServiceResult.error_result(
                f"获取状态异常: {str(e)}",
                "STATUS_ERROR"
            )
    
    async def configure(self, config: Dict[str, Any]) -> ServiceResult[bool]:
        """
        配置Live2D服务
        
        Args:
            config: 配置字典
            
        Returns:
            ServiceResult[bool]: 配置结果
        """
        try:
            if "lipsync_sensitivity" in config:
                self.lipsync_sensitivity = float(config["lipsync_sensitivity"])
                self.logger.debug(f"口型同步敏感度设置为: {self.lipsync_sensitivity}")
            
            if "auto_blink" in config:
                self.auto_blink = bool(config["auto_blink"])
                self.logger.debug(f"自动眨眼设置为: {self.auto_blink}")
            
            if "auto_breath" in config:
                self.auto_breath = bool(config["auto_breath"])
                self.logger.debug(f"自动呼吸设置为: {self.auto_breath}")
            
            # 如果Live2D组件可用，应用配置
            if self.live2d_widget:
                if hasattr(self.live2d_widget, 'set_auto_blink'):
                    self.live2d_widget.set_auto_blink(self.auto_blink)
                
                if hasattr(self.live2d_widget, 'set_auto_breath'):
                    self.live2d_widget.set_auto_breath(self.auto_breath)
            
            self.logger.info("✅ Live2D服务配置已更新")
            return ServiceResult.success_result(data=True)
            
        except Exception as e:
            self.logger.error(f"配置Live2D服务失败: {e}")
            return ServiceResult.error_result(
                f"配置异常: {str(e)}",
                "CONFIG_ERROR"
            )
