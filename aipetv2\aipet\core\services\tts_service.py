"""
TTS核心服务

提供统一的文本转语音服务接口，集成到SystemFoundation架构中
"""

import asyncio
import logging
from typing import Optional, Dict, Any, List
from pathlib import Path

from aipet.core.events.base import EventBus, Event
from aipet.core.foundation import SystemFoundation
from .base import BaseService, ServiceResult
from .live2d_service import Live2DService
from aipet.infrastructure.text.cleaner import TTSTextCleaner
from aipet.infrastructure.text.segmenter import TTSTextSegmenter, SegmentationConfig

logger = logging.getLogger(__name__)


class TTSSynthesisRequestedEvent(Event):
    """TTS合成请求事件"""
    event_type = "tts_synthesis_requested"

    def __init__(self, text: str, service: str = None, speaker: str = None):
        super().__init__()
        from datetime import datetime
        self.data = {
            "text": text,
            "service": service,
            "speaker": speaker,
            "timestamp": datetime.now().isoformat()
        }


class TTSPlaybackStartedEvent(Event):
    """TTS播放开始事件"""
    event_type = "tts_playback_started"

    def __init__(self, text: str, service: str, speaker: str):
        super().__init__()
        from datetime import datetime
        self.data = {
            "text": text,
            "service": service,
            "speaker": speaker,
            "timestamp": datetime.now().isoformat()
        }


class TTSPlaybackFinishedEvent(Event):
    """TTS播放完成事件"""
    event_type = "tts_playback_finished"
    
    def __init__(self, success: bool, duration: float = None, error: str = None):
        super().__init__()
        from datetime import datetime
        self.data = {
            "success": success,
            "duration": duration,
            "error": error,
            "timestamp": datetime.now().isoformat()
        }


class TTSService(BaseService):
    """
    TTS核心服务

    负责协调TTS管理器、音频服务和事件系统
    """

    def __init__(self, foundation: SystemFoundation):
        super().__init__()
        self.foundation = foundation
        self.event_bus = None  # 将在initialize时获取
        self.tts_manager = None
        self.audio_service = None
        self.live2d_service = None  # Live2D服务
        self.text_cleaner = None    # 文本清理器
        self.text_segmenter = None  # 文本分段器
        self._is_enabled = True
        self._live2d_enabled = True  # Live2D联动开关
        self._text_processing_enabled = True  # 文本处理开关
        self._last_processed_message_id = None  # 防止重复处理同一消息
        self._event_handlers_registered = False  # 防止重复注册事件处理器

        logger.info("✅ TTS核心服务构造完成")
    
    async def _initialize_impl(self):
        """实现BaseService的抽象方法"""
        # 获取事件总线
        self.event_bus = self.foundation.get_service("event_bridge")
        if not self.event_bus:
            raise RuntimeError("事件桥接器未找到")

        # 检查TTS是否启用
        self._is_enabled = self.foundation.get_config("tts.enabled") != "false"
        if not self._is_enabled:
            logger.info("TTS服务已禁用")
            return

        # 获取TTS管理器和音频服务
        self.tts_manager = self.foundation.get_service("tts_manager")
        self.audio_service = self.foundation.get_service("audio_service")

        if not self.tts_manager:
            raise RuntimeError("TTS管理器未找到")
        if not self.audio_service:
            raise RuntimeError("音频服务未找到")

        # 初始化Live2D服务
        await self._initialize_live2d_service()

        # 初始化文本处理组件
        self._initialize_text_processing()

        # 注册事件处理器
        self._register_event_handlers()
        logger.info("✅ TTS核心服务初始化完成")
    
    def _register_event_handlers(self):
        """注册事件处理器"""
        if self._event_handlers_registered:
            logger.warning("⚠️ TTS事件处理器已注册，跳过重复注册")
            return

        logger.info("🎧 注册TTS事件处理器...")

        # 先尝试移除可能存在的旧监听器
        try:
            self.event_bus.off("tts_request", self._on_tts_request)
            self.event_bus.off("ai_response_finished", self._on_ai_response_finished)
            self.event_bus.off("tts_synthesis_requested", self._on_synthesis_requested)
            self.event_bus.off("tts_config_updated", self._on_config_updated)
        except:
            pass  # 忽略移除失败的错误

        # 注册新的监听器
        self.event_bus.on("tts_request", self._on_tts_request)  # 监听新的TTS请求事件
        self.event_bus.on("ai_response_finished", self._on_ai_response_finished)  # 保留兼容性
        self.event_bus.on("tts_synthesis_requested", self._on_synthesis_requested)
        self.event_bus.on("tts_config_updated", self._on_config_updated)
        self._event_handlers_registered = True
        logger.info("✅ TTS事件处理器注册完成")

    async def _initialize_live2d_service(self):
        """初始化Live2D服务"""
        try:
            # 检查Live2D联动是否启用
            self._live2d_enabled = self.foundation.get_config("tts.live2d_enabled") != "false"

            if not self._live2d_enabled:
                logger.info("Live2D联动已禁用")
                return

            # 创建Live2D服务实例
            self.live2d_service = Live2DService(self.foundation)

            # 初始化Live2D服务
            result = await self.live2d_service.initialize()
            if result.success:
                logger.info("✅ Live2D服务初始化成功")
            else:
                logger.warning(f"⚠️ Live2D服务初始化失败: {result.error}")
                self.live2d_service = None

        except Exception as e:
            logger.error(f"❌ Live2D服务初始化异常: {e}")
            self.live2d_service = None

    async def _on_tts_request(self, event: Event):
        """处理TTS请求事件（新的事件处理方式）"""
        logger.info(f"🎤 收到TTS请求事件: {event}")

        if not self._is_enabled:
            logger.info("⚠️ TTS服务已禁用，跳过TTS播放")
            return

        try:
            # 检查是否启用自动TTS
            raw_config = self.foundation.get_config("TTS_AUTO_PLAY_AI_RESPONSE")

            # 正确处理布尔值和字符串值
            if raw_config is None:
                auto_tts = True  # 默认启用
            elif isinstance(raw_config, bool):
                auto_tts = raw_config
            elif isinstance(raw_config, str):
                auto_tts = raw_config.lower() not in ["false", "0", "no", "off", "disabled"]
            else:
                auto_tts = bool(raw_config)

            logger.info(f"🔧 自动TTS原始配置值: '{raw_config}' ({type(raw_config)}) -> 解析结果: {auto_tts}")

            if not auto_tts:
                logger.info("⚠️ 自动TTS已禁用，跳过播放")
                return

            text = event.data.get("text", "") if event.data else ""
            logger.info(f"📝 提取的文本: '{text[:50]}...' (长度: {len(text)})")

            if text.strip():
                # 直接调用合成和播放方法
                await self._synthesize_and_play(text)
                logger.info(f"✅ TTS播放完成")
            else:
                logger.warning("⚠️ 文本为空，跳过TTS播放")

        except Exception as e:
            logger.error(f"❌ 处理TTS请求失败: {e}")
            import traceback
            traceback.print_exc()

    def _initialize_text_processing(self):
        """初始化文本处理组件"""
        try:
            # 检查文本处理是否启用
            self._text_processing_enabled = self.foundation.get_config("tts.text_processing_enabled") != "false"

            if not self._text_processing_enabled:
                logger.info("文本处理已禁用")
                return

            # 初始化文本清理器
            self.text_cleaner = TTSTextCleaner()

            # 初始化文本分段器
            segmentation_config = SegmentationConfig(
                max_length=int(self.foundation.get_config("tts.segment_max_length", "100")),
                min_length=int(self.foundation.get_config("tts.segment_min_length", "10"))
            )
            self.text_segmenter = TTSTextSegmenter(segmentation_config)

            logger.info("✅ 文本处理组件初始化成功")

        except Exception as e:
            logger.error(f"❌ 文本处理组件初始化异常: {e}")
            self.text_cleaner = None
            self.text_segmenter = None

    async def _on_ai_response_finished(self, event: Event):
        """AI回复完成后自动播放TTS"""
        logger.info(f"🎤 收到AI回复完成事件: {event}")

        if not self._is_enabled:
            logger.info("⚠️ TTS服务已禁用，跳过自动播放")
            return

        try:
            # 检查是否启用自动TTS
            raw_config = self.foundation.get_config("TTS_AUTO_PLAY_AI_RESPONSE")

            # 正确处理布尔值和字符串值
            if raw_config is None:
                auto_tts = True  # 默认启用
            elif isinstance(raw_config, bool):
                auto_tts = raw_config
            elif isinstance(raw_config, str):
                auto_tts = raw_config.lower() not in ["false", "0", "no", "off", "disabled"]
            else:
                auto_tts = bool(raw_config)

            logger.info(f"🔧 自动TTS原始配置值: '{raw_config}' ({type(raw_config)}) -> 解析结果: {auto_tts}")

            if not auto_tts:
                logger.info("⚠️ 自动TTS已禁用，跳过播放")
                return

            text = event.data.get("text", "") if event.data else ""
            logger.info(f"📝 提取的文本: '{text[:50]}...' (长度: {len(text)})")

            if text.strip():
                # 直接调用合成和播放方法（包含文本处理）
                await self._synthesize_and_play(text)
                logger.info(f"✅ TTS播放完成")
            else:
                logger.warning("⚠️ 文本为空，跳过TTS播放")

        except Exception as e:
            logger.error(f"❌ 处理AI回复TTS失败: {e}")
            import traceback
            traceback.print_exc()
    
    async def _on_synthesis_requested(self, event: Event):
        """处理TTS合成请求"""
        logger.info(f"🎵 收到TTS合成请求: {event}")

        if not self._is_enabled:
            logger.warning("⚠️ TTS服务已禁用，跳过合成")
            return

        if not self.tts_manager:
            logger.error("❌ TTS管理器未找到，跳过合成")
            return

        try:
            text = event.data.get("text", "") if event.data else ""
            service = event.data.get("service") if event.data else None
            speaker = event.data.get("speaker") if event.data else None

            logger.info(f"📝 合成参数: text='{text[:50]}...', service={service}, speaker={speaker}")

            if not text.strip():
                logger.warning("⚠️ 合成文本为空，跳过")
                return

            # 执行TTS合成和播放
            logger.info("🎤 开始执行TTS合成和播放...")
            await self._synthesize_and_play(text, service, speaker)
            logger.info("✅ TTS合成和播放完成")

        except Exception as e:
            logger.error(f"❌ TTS合成失败: {e}")
            import traceback
            traceback.print_exc()
            # 发布播放失败事件
            error_event = TTSPlaybackFinishedEvent(success=False, error=str(e))
            self.event_bus.emit(error_event.event_type, error_event.data, "tts_service")
    
    async def _on_config_updated(self, event: Event):
        """处理配置更新"""
        config_key = event.data.get("config_key", "")
        if config_key.startswith("TTS_"):
            logger.info(f"🔄 TTS配置更新事件被触发: {config_key}")

            # 重新加载TTS配置
            if self.tts_manager:
                await self.tts_manager.reload_config()
    
    async def _synthesize_and_play(self, text: str, service: str = None, speaker: str = None):
        """合成语音并播放（支持Live2D联动）"""
        try:
            # 文本预处理
            processed_text = await self._process_text(text)
            if not processed_text:
                logger.warning("文本处理后为空，跳过TTS合成")
                return

            # 发布播放开始事件
            current_service = service or self.foundation.get_config("tts.current_service") or "edge_tts_online"
            current_speaker = speaker or self._get_current_speaker(current_service)

            start_event = TTSPlaybackStartedEvent(processed_text, current_service, current_speaker)
            self.event_bus.emit(start_event.event_type, start_event.data, "tts_service")

            # 检查是否需要分段处理
            if self._text_processing_enabled and self.text_segmenter:
                segments = self.text_segmenter.segment_text(processed_text)
                if len(segments) > 1:
                    logger.info(f"文本分段处理: {len(segments)} 个分段")
                    await self._synthesize_and_play_segments(segments, service, speaker)
                    return

            # 执行TTS合成
            audio_data = await self.tts_manager.synthesize(processed_text, service, speaker)

            if audio_data:
                # 检查是否启用Live2D联动
                if self._live2d_enabled and self.live2d_service:
                    duration, wav_file = await self._play_with_live2d(audio_data, text)
                else:
                    # 普通播放
                    duration = await self.audio_service.play_audio(audio_data)
                    wav_file = None

                # 发布播放完成事件
                finish_event = TTSPlaybackFinishedEvent(success=True, duration=duration)
                finish_event.data["live2d_enabled"] = self._live2d_enabled
                finish_event.data["wav_file"] = wav_file
                self.event_bus.emit(finish_event.event_type, finish_event.data, "tts_service")
            else:
                raise RuntimeError("TTS合成失败，未生成音频数据")

        except Exception as e:
            logger.error(f"TTS合成和播放失败: {e}")
            # 发布播放失败事件
            error_event = TTSPlaybackFinishedEvent(success=False, error=str(e))
            self.event_bus.emit(error_event.event_type, error_event.data, "tts_service")

    async def _play_with_live2d(self, audio_data: bytes, text: str) -> tuple[Optional[float], Optional[str]]:
        """
        使用Live2D联动播放音频

        Args:
            audio_data: 音频数据
            text: 对应的文本（用于表情分析）

        Returns:
            tuple[Optional[float], Optional[str]]: (播放时长, WAV文件路径)
        """
        try:
            # 使用音频服务的Live2D播放功能
            if hasattr(self.audio_service, 'play_for_live2d'):
                duration, wav_file = await self.audio_service.play_for_live2d(audio_data)

                if duration is not None and wav_file:
                    # 启动Live2D口型同步
                    lipsync_result = await self.live2d_service.start_lipsync(wav_file)

                    if lipsync_result.success:
                        logger.info(f"✅ Live2D口型同步已启动: {wav_file}")

                        # 可选：根据文本内容设置表情
                        await self._set_expression_by_text(text)

                        return duration, wav_file
                    else:
                        logger.warning(f"⚠️ Live2D口型同步启动失败: {lipsync_result.error}")
                        # 即使口型同步失败，音频仍然播放了
                        return duration, wav_file
                else:
                    logger.error("Live2D音频播放失败")
                    # 降级到普通播放
                    duration = await self.audio_service.play_audio(audio_data)
                    return duration, None
            else:
                logger.warning("音频服务不支持Live2D播放，降级到普通播放")
                duration = await self.audio_service.play_audio(audio_data)
                return duration, None

        except Exception as e:
            logger.error(f"Live2D播放失败: {e}")
            # 降级到普通播放
            try:
                duration = await self.audio_service.play_audio(audio_data)
                return duration, None
            except Exception as fallback_error:
                logger.error(f"降级播放也失败: {fallback_error}")
                return None, None

    async def _set_expression_by_text(self, text: str):
        """
        根据文本内容智能设置表情

        Args:
            text: 文本内容
        """
        try:
            if not self.live2d_service:
                return

            # 简单的情感分析和表情映射
            text_lower = text.lower()

            # 获取支持的表情列表
            status_result = await self.live2d_service.get_status()
            if not status_result.success:
                return

            supported_expressions = status_result.data.get("supported_expressions", [])
            if not supported_expressions:
                return

            # 表情映射规则
            expression_mapping = {
                # 开心相关
                ("开心", "高兴", "哈哈", "笑", "😊", "😄", "😁"): "happy",
                # 悲伤相关
                ("难过", "伤心", "哭", "😢", "😭", "😔"): "sad",
                # 惊讶相关
                ("惊讶", "震惊", "哇", "😲", "😮", "😯"): "surprised",
                # 生气相关
                ("生气", "愤怒", "气", "😠", "😡", "💢"): "angry",
                # 默认表情
                (): "normal"
            }

            # 查找匹配的表情
            target_expression = "normal"  # 默认表情

            for keywords, expression in expression_mapping.items():
                if expression in supported_expressions:
                    for keyword in keywords:
                        if keyword in text_lower:
                            target_expression = expression
                            break
                    if target_expression != "normal":
                        break

            # 设置表情
            if target_expression in supported_expressions:
                result = await self.live2d_service.set_expression(target_expression)
                if result.success:
                    logger.debug(f"根据文本设置表情: {target_expression}")
                else:
                    logger.debug(f"设置表情失败: {result.error}")

        except Exception as e:
            logger.debug(f"智能表情设置失败: {e}")
    
    def _get_current_speaker(self, service: str) -> str:
        """获取当前服务的音色"""
        if service == "edge_tts_online":
            return self.foundation.get_config("tts.edge_current_speaker") or "zh-CN-XiaoyiNeural"
        elif service == "local_vits":
            return self.foundation.get_config("tts.local_vits_default_speaker") or "0"
        elif service == "gpt_sovits":
            return self.foundation.get_config("tts.gpt_sovits_default_speaker") or "default"
        else:
            return "default"
    
    async def speak(self, text: str, service: str = None, speaker: str = None) -> bool:
        """
        公共接口：合成并播放语音
        
        Args:
            text: 要合成的文本
            service: TTS服务名称（可选）
            speaker: 音色名称（可选）
            
        Returns:
            bool: 是否成功开始播放
        """
        if not self._is_enabled:
            logger.warning("TTS服务已禁用")
            return False
        
        try:
            # 发布TTS合成请求事件
            event = TTSSynthesisRequestedEvent(text, service, speaker)
            self.event_bus.emit(event.event_type, event.data, "tts_service")
            return True
            
        except Exception as e:
            logger.error(f"TTS speak失败: {e}")
            return False
    
    def is_enabled(self) -> bool:
        """检查TTS是否启用"""
        return self._is_enabled
    
    def set_enabled(self, enabled: bool):
        """设置TTS启用状态"""
        self._is_enabled = enabled
        self.foundation.set_config("TTS_ENABLED", enabled)
        logger.info(f"TTS服务{'启用' if enabled else '禁用'}")

    def is_live2d_enabled(self) -> bool:
        """检查Live2D联动是否启用"""
        return self._live2d_enabled and self.live2d_service is not None

    def set_live2d_enabled(self, enabled: bool):
        """设置Live2D联动启用状态"""
        self._live2d_enabled = enabled
        self.foundation.set_config("tts.live2d_enabled", enabled)
        logger.info(f"Live2D联动{'启用' if enabled else '禁用'}")

    async def get_live2d_status(self) -> ServiceResult[Dict[str, Any]]:
        """
        获取Live2D服务状态

        Returns:
            ServiceResult[Dict]: Live2D状态信息
        """
        try:
            if not self.live2d_service:
                return ServiceResult.success_result(data={
                    "enabled": False,
                    "available": False,
                    "message": "Live2D服务未初始化"
                })

            status_result = await self.live2d_service.get_status()
            if status_result.success:
                status_data = status_result.data
                status_data["enabled"] = self._live2d_enabled
                return ServiceResult.success_result(data=status_data)
            else:
                return ServiceResult.error_result(
                    f"获取Live2D状态失败: {status_result.error}",
                    "LIVE2D_STATUS_ERROR"
                )

        except Exception as e:
            logger.error(f"获取Live2D状态异常: {e}")
            return ServiceResult.error_result(
                f"获取Live2D状态异常: {str(e)}",
                "LIVE2D_STATUS_EXCEPTION"
            )

    async def set_live2d_expression(self, expression_name: str) -> ServiceResult[bool]:
        """
        设置Live2D表情

        Args:
            expression_name: 表情名称

        Returns:
            ServiceResult[bool]: 设置结果
        """
        try:
            if not self.live2d_service:
                return ServiceResult.error_result(
                    "Live2D服务不可用",
                    "LIVE2D_NOT_AVAILABLE"
                )

            return await self.live2d_service.set_expression(expression_name)

        except Exception as e:
            logger.error(f"设置Live2D表情异常: {e}")
            return ServiceResult.error_result(
                f"设置Live2D表情异常: {str(e)}",
                "LIVE2D_EXPRESSION_EXCEPTION"
            )

    async def play_live2d_motion(self, motion_name: str, priority: int = 1) -> ServiceResult[bool]:
        """
        播放Live2D动作

        Args:
            motion_name: 动作名称
            priority: 优先级

        Returns:
            ServiceResult[bool]: 播放结果
        """
        try:
            if not self.live2d_service:
                return ServiceResult.error_result(
                    "Live2D服务不可用",
                    "LIVE2D_NOT_AVAILABLE"
                )

            return await self.live2d_service.play_motion(motion_name, priority)

        except Exception as e:
            logger.error(f"播放Live2D动作异常: {e}")
            return ServiceResult.error_result(
                f"播放Live2D动作异常: {str(e)}",
                "LIVE2D_MOTION_EXCEPTION"
            )

    async def _process_text(self, text: str) -> str:
        """
        处理文本内容

        Args:
            text: 原始文本

        Returns:
            处理后的文本
        """
        if not text or not text.strip():
            return ""

        processed_text = text.strip()

        # 如果启用了文本处理且文本清理器可用
        if self._text_processing_enabled and self.text_cleaner:
            try:
                processed_text = self.text_cleaner.clean_text(processed_text)
                logger.debug(f"文本清理: {len(text)} -> {len(processed_text)} 字符")
            except Exception as e:
                logger.warning(f"文本清理失败，使用原始文本: {e}")
                processed_text = text.strip()

        return processed_text

    async def _synthesize_and_play_segments(self, segments: List[str], service: str = None, speaker: str = None):
        """
        分段合成和播放

        Args:
            segments: 文本分段列表
            service: TTS服务名称
            speaker: 说话人
        """
        try:
            current_service = service or self.foundation.get_config("tts.current_service") or "edge_tts_online"
            current_speaker = speaker or self._get_current_speaker(current_service)

            for i, segment in enumerate(segments):
                if not segment.strip():
                    continue

                logger.info(f"处理分段 {i+1}/{len(segments)}: {segment[:50]}...")

                try:
                    # 执行TTS合成
                    audio_data = await self.tts_manager.synthesize(segment, service, speaker)

                    if audio_data:
                        # 检查是否启用Live2D联动
                        if self._live2d_enabled and self.live2d_service:
                            duration, wav_file = await self._play_with_live2d(audio_data, segment)
                        else:
                            # 普通播放
                            duration = await self.audio_service.play_audio(audio_data)
                            wav_file = None

                        # 分段间的短暂停顿
                        if i < len(segments) - 1:  # 不是最后一个分段
                            pause_duration = float(self.foundation.get_config("tts.segment_pause_duration", "0.5"))
                            if pause_duration > 0:
                                await asyncio.sleep(pause_duration)

                    else:
                        logger.warning(f"分段 {i+1} TTS合成失败")

                except Exception as e:
                    logger.error(f"分段 {i+1} 处理失败: {e}")
                    continue

            # 发布分段播放完成事件
            finish_event = TTSPlaybackFinishedEvent(success=True, duration=None)
            finish_event.data["segmented_playback"] = True
            finish_event.data["segments_count"] = len(segments)
            self.event_bus.emit(finish_event.event_type, finish_event.data, "tts_service")

        except Exception as e:
            logger.error(f"分段播放失败: {e}")
            # 发布播放失败事件
            error_event = TTSPlaybackFinishedEvent(success=False, error=str(e))
            error_event.data["segmented_playback"] = True
            self.event_bus.emit(error_event.event_type, error_event.data, "tts_service")

    # 文本处理相关的公共API方法
    def is_text_processing_enabled(self) -> bool:
        """检查文本处理是否启用"""
        return self._text_processing_enabled

    def set_text_processing_enabled(self, enabled: bool):
        """设置文本处理启用状态"""
        self._text_processing_enabled = enabled
        logger.info(f"✅ 文本处理已{'启用' if enabled else '禁用'}")

    def get_text_cleaner_stats(self) -> Optional[Dict[str, Any]]:
        """获取文本清理器统计信息"""
        if self.text_cleaner:
            return self.text_cleaner.get_cleaning_stats()
        return None

    def get_text_segmenter_stats(self) -> Optional[Dict[str, Any]]:
        """获取文本分段器统计信息"""
        if self.text_segmenter:
            return self.text_segmenter.get_segmentation_stats()
        return None

    def reset_text_processing_stats(self):
        """重置文本处理统计信息"""
        if self.text_cleaner:
            self.text_cleaner.reset_stats()
        if self.text_segmenter:
            self.text_segmenter.reset_stats()
        logger.info("✅ 文本处理统计信息已重置")
