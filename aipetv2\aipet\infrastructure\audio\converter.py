"""
音频格式转换器模块

提供高级音频格式转换功能，专门为TTS和Live2D口型同步优化
"""

import io
import logging
from typing import Optional, Dict, Any
from pathlib import Path

from .format_detector import AudioFormatDetector

logger = logging.getLogger(__name__)


class AudioConverter:
    """音频格式转换器 - 为TTS和Live2D优化"""
    
    # Live2D推荐的音频参数
    LIVE2D_OPTIMAL_PARAMS = {
        "format": "wav",
        "channels": 1,  # 单声道
        "sample_rate": 22050,  # 22kHz采样率
        "sample_width": 2  # 16位
    }
    
    # TTS推荐的音频参数
    TTS_OPTIMAL_PARAMS = {
        "format": "wav",
        "channels": 1,  # 单声道
        "sample_rate": 16000,  # 16kHz采样率
        "sample_width": 2  # 16位
    }
    
    def __init__(self):
        self._check_dependencies()
    
    def _check_dependencies(self):
        """检查依赖库是否可用"""
        try:
            import pydub
            self.pydub_available = True
            logger.debug("pydub库可用")
        except ImportError:
            self.pydub_available = False
            logger.warning("pydub库不可用，音频转换功能将受限")
    
    def convert_for_live2d(self, audio_data: bytes, source_format: str = None) -> Optional[bytes]:
        """
        转换音频为Live2D口型同步优化格式
        
        Args:
            audio_data: 源音频数据
            source_format: 源格式（可选，会自动检测）
            
        Returns:
            Optional[bytes]: 转换后的音频数据，失败返回None
        """
        if not self.pydub_available:
            logger.error("pydub不可用，无法进行Live2D音频转换")
            return None
        
        try:
            # 自动检测格式
            if source_format is None:
                source_format = AudioFormatDetector.detect_format(audio_data)
                logger.debug(f"自动检测到音频格式: {source_format}")
            
            if source_format == "unknown":
                logger.error("无法识别音频格式，转换失败")
                return None
            
            from pydub import AudioSegment
            
            # 加载音频
            audio = AudioSegment.from_file(
                io.BytesIO(audio_data), 
                format=source_format
            )
            
            # 应用Live2D优化参数
            optimized_audio = self._apply_live2d_optimization(audio)
            
            # 导出为WAV
            output_buffer = io.BytesIO()
            optimized_audio.export(output_buffer, format="wav")
            result_data = output_buffer.getvalue()
            
            logger.info(f"Live2D音频转换成功: {len(audio_data)} -> {len(result_data)} bytes")
            logger.debug(f"转换参数: {self.LIVE2D_OPTIMAL_PARAMS}")
            
            return result_data
            
        except Exception as e:
            logger.error(f"Live2D音频转换失败: {e}")
            return None
    
    def convert_for_tts(self, audio_data: bytes, source_format: str = None) -> Optional[bytes]:
        """
        转换音频为TTS播放优化格式
        
        Args:
            audio_data: 源音频数据
            source_format: 源格式（可选，会自动检测）
            
        Returns:
            Optional[bytes]: 转换后的音频数据，失败返回None
        """
        if not self.pydub_available:
            logger.error("pydub不可用，无法进行TTS音频转换")
            return None
        
        try:
            # 自动检测格式
            if source_format is None:
                source_format = AudioFormatDetector.detect_format(audio_data)
                logger.debug(f"自动检测到音频格式: {source_format}")
            
            if source_format == "unknown":
                logger.error("无法识别音频格式，转换失败")
                return None
            
            from pydub import AudioSegment
            
            # 加载音频
            audio = AudioSegment.from_file(
                io.BytesIO(audio_data), 
                format=source_format
            )
            
            # 应用TTS优化参数
            optimized_audio = self._apply_tts_optimization(audio)
            
            # 导出为WAV
            output_buffer = io.BytesIO()
            optimized_audio.export(output_buffer, format="wav")
            result_data = output_buffer.getvalue()
            
            logger.info(f"TTS音频转换成功: {len(audio_data)} -> {len(result_data)} bytes")
            logger.debug(f"转换参数: {self.TTS_OPTIMAL_PARAMS}")
            
            return result_data
            
        except Exception as e:
            logger.error(f"TTS音频转换失败: {e}")
            return None
    
    def _apply_live2d_optimization(self, audio: 'AudioSegment') -> 'AudioSegment':
        """应用Live2D优化参数"""
        params = self.LIVE2D_OPTIMAL_PARAMS
        
        # 转换为单声道
        if audio.channels != params["channels"]:
            if params["channels"] == 1:
                audio = audio.set_channels(1)
                logger.debug("转换为单声道")
        
        # 调整采样率
        if audio.frame_rate != params["sample_rate"]:
            audio = audio.set_frame_rate(params["sample_rate"])
            logger.debug(f"调整采样率: {audio.frame_rate} -> {params['sample_rate']}")
        
        # 调整采样宽度
        if audio.sample_width != params["sample_width"]:
            audio = audio.set_sample_width(params["sample_width"])
            logger.debug(f"调整采样宽度: {audio.sample_width} -> {params['sample_width']}")
        
        # 音量标准化（防止过大或过小）
        audio = self._normalize_volume(audio)
        
        return audio
    
    def _apply_tts_optimization(self, audio: 'AudioSegment') -> 'AudioSegment':
        """应用TTS优化参数"""
        params = self.TTS_OPTIMAL_PARAMS
        
        # 转换为单声道
        if audio.channels != params["channels"]:
            if params["channels"] == 1:
                audio = audio.set_channels(1)
                logger.debug("转换为单声道")
        
        # 调整采样率
        if audio.frame_rate != params["sample_rate"]:
            audio = audio.set_frame_rate(params["sample_rate"])
            logger.debug(f"调整采样率: {audio.frame_rate} -> {params['sample_rate']}")
        
        # 调整采样宽度
        if audio.sample_width != params["sample_width"]:
            audio = audio.set_sample_width(params["sample_width"])
            logger.debug(f"调整采样宽度: {audio.sample_width} -> {params['sample_width']}")
        
        # 音量标准化
        audio = self._normalize_volume(audio)
        
        return audio
    
    def _normalize_volume(self, audio: 'AudioSegment', target_dBFS: float = -20.0) -> 'AudioSegment':
        """
        标准化音频音量
        
        Args:
            audio: 音频段
            target_dBFS: 目标音量（dBFS）
            
        Returns:
            AudioSegment: 标准化后的音频
        """
        try:
            # 获取当前音量
            current_dBFS = audio.dBFS
            
            # 计算需要调整的音量
            change_in_dBFS = target_dBFS - current_dBFS
            
            # 限制调整范围（防止过度放大或缩小）
            if abs(change_in_dBFS) > 20:
                change_in_dBFS = 20 if change_in_dBFS > 0 else -20
                logger.warning(f"音量调整幅度过大，限制为: {change_in_dBFS}dB")
            
            # 应用音量调整
            normalized_audio = audio + change_in_dBFS
            
            logger.debug(f"音量标准化: {current_dBFS:.1f}dBFS -> {normalized_audio.dBFS:.1f}dBFS")
            
            return normalized_audio
            
        except Exception as e:
            logger.warning(f"音量标准化失败: {e}")
            return audio
    
    def convert_with_custom_params(self, 
                                 audio_data: bytes, 
                                 source_format: str = None,
                                 target_params: Dict[str, Any] = None) -> Optional[bytes]:
        """
        使用自定义参数转换音频
        
        Args:
            audio_data: 源音频数据
            source_format: 源格式
            target_params: 目标参数字典
            
        Returns:
            Optional[bytes]: 转换后的音频数据
        """
        if not self.pydub_available:
            logger.error("pydub不可用，无法进行自定义音频转换")
            return None
        
        if target_params is None:
            target_params = self.TTS_OPTIMAL_PARAMS.copy()
        
        try:
            # 自动检测格式
            if source_format is None:
                source_format = AudioFormatDetector.detect_format(audio_data)
            
            if source_format == "unknown":
                logger.error("无法识别音频格式，转换失败")
                return None
            
            from pydub import AudioSegment
            
            # 加载音频
            audio = AudioSegment.from_file(
                io.BytesIO(audio_data), 
                format=source_format
            )
            
            # 应用自定义参数
            if "channels" in target_params:
                audio = audio.set_channels(target_params["channels"])
            
            if "sample_rate" in target_params:
                audio = audio.set_frame_rate(target_params["sample_rate"])
            
            if "sample_width" in target_params:
                audio = audio.set_sample_width(target_params["sample_width"])
            
            # 音量标准化
            if "normalize_volume" in target_params and target_params["normalize_volume"]:
                target_dBFS = target_params.get("target_dBFS", -20.0)
                audio = self._normalize_volume(audio, target_dBFS)
            
            # 导出
            output_format = target_params.get("format", "wav")
            output_buffer = io.BytesIO()
            audio.export(output_buffer, format=output_format)
            result_data = output_buffer.getvalue()
            
            logger.info(f"自定义音频转换成功: {len(audio_data)} -> {len(result_data)} bytes")
            
            return result_data
            
        except Exception as e:
            logger.error(f"自定义音频转换失败: {e}")
            return None
    
    def get_audio_duration(self, audio_data: bytes, source_format: str = None) -> Optional[float]:
        """
        获取音频时长（秒）
        
        Args:
            audio_data: 音频数据
            source_format: 源格式
            
        Returns:
            Optional[float]: 音频时长（秒），失败返回None
        """
        if not self.pydub_available:
            return None
        
        try:
            if source_format is None:
                source_format = AudioFormatDetector.detect_format(audio_data)
            
            if source_format == "unknown":
                return None
            
            from pydub import AudioSegment
            
            audio = AudioSegment.from_file(
                io.BytesIO(audio_data), 
                format=source_format
            )
            
            duration_seconds = len(audio) / 1000.0
            logger.debug(f"音频时长: {duration_seconds:.2f}秒")
            
            return duration_seconds
            
        except Exception as e:
            logger.error(f"获取音频时长失败: {e}")
            return None
