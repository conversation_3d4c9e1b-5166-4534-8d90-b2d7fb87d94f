"""
音频格式检测器模块

基于原aipet项目实现，提供音频格式检测和转换功能
"""

import io
import logging
from typing import Optional

logger = logging.getLogger(__name__)


class AudioFormatDetector:
    """音频格式检测器 - 基于原aipet项目实现"""
    
    # 支持的音频格式
    SUPPORTED_FORMATS = ["wav", "mp3", "ogg", "flac"]
    
    @staticmethod
    def detect_format(audio_data: bytes) -> str:
        """
        检测音频格式
        
        Args:
            audio_data: 音频数据字节
            
        Returns:
            str: 检测到的音频格式 ("wav", "mp3", "ogg", "flac", "unknown")
        """
        if not audio_data or len(audio_data) < 12:
            logger.warning("音频数据为空或过短，无法检测格式")
            return "unknown"
        
        try:
            # 检测WAV格式
            if audio_data.startswith(b'RIFF') and audio_data[8:12] == b'WAVE':
                logger.debug("检测到WAV格式")
                return "wav"
            
            # 检测MP3格式
            if (audio_data.startswith(b'\xff\xfb') or 
                audio_data.startswith(b'\xff\xfa') or 
                audio_data.startswith(b'ID3')):
                logger.debug("检测到MP3格式")
                return "mp3"
            
            # 检测OGG格式
            if audio_data.startswith(b'OggS'):
                logger.debug("检测到OGG格式")
                return "ogg"
            
            # 检测FLAC格式
            if audio_data.startswith(b'fLaC'):
                logger.debug("检测到FLAC格式")
                return "flac"
            
            # 未知格式
            logger.warning(f"未知音频格式，前12字节: {audio_data[:12]}")
            return "unknown"
            
        except Exception as e:
            logger.error(f"音频格式检测时发生异常: {e}")
            return "unknown"
    
    @staticmethod
    def convert_to_wav(audio_data: bytes, source_format: str) -> bytes:
        """
        转换音频格式为WAV
        
        Args:
            audio_data: 源音频数据
            source_format: 源音频格式
            
        Returns:
            bytes: 转换后的WAV格式音频数据
        """
        if source_format == "wav":
            logger.debug("源格式已是WAV，无需转换")
            return audio_data
        
        if source_format == "unknown":
            logger.warning("源格式未知，返回原始数据")
            return audio_data
        
        try:
            # 导入pydub进行格式转换
            from pydub import AudioSegment
            
            logger.info(f"开始转换音频格式: {source_format} -> wav")
            
            # 从字节数据创建AudioSegment
            audio = AudioSegment.from_file(
                io.BytesIO(audio_data), 
                format=source_format
            )
            
            # 导出为WAV格式
            wav_buffer = io.BytesIO()
            audio.export(wav_buffer, format="wav")
            wav_data = wav_buffer.getvalue()
            
            logger.info(f"音频格式转换成功: {len(audio_data)} bytes -> {len(wav_data)} bytes")
            return wav_data
            
        except ImportError:
            logger.error("pydub库未安装，无法进行音频格式转换")
            logger.info("请安装pydub: pip install pydub")
            return audio_data
            
        except Exception as e:
            logger.error(f"音频格式转换失败: {e}")
            logger.warning("返回原始音频数据作为fallback")
            return audio_data
    
    @staticmethod
    def is_format_supported(format_name: str) -> bool:
        """
        检查格式是否支持
        
        Args:
            format_name: 格式名称
            
        Returns:
            bool: 是否支持该格式
        """
        return format_name.lower() in AudioFormatDetector.SUPPORTED_FORMATS
    
    @staticmethod
    def get_audio_info(audio_data: bytes) -> dict:
        """
        获取音频信息
        
        Args:
            audio_data: 音频数据
            
        Returns:
            dict: 音频信息字典
        """
        info = {
            "format": AudioFormatDetector.detect_format(audio_data),
            "size_bytes": len(audio_data),
            "is_supported": False,
            "can_convert": False
        }
        
        info["is_supported"] = AudioFormatDetector.is_format_supported(info["format"])
        info["can_convert"] = info["format"] != "unknown"
        
        # 尝试获取更详细的音频信息
        try:
            from pydub import AudioSegment
            
            if info["format"] != "unknown":
                audio = AudioSegment.from_file(
                    io.BytesIO(audio_data), 
                    format=info["format"]
                )
                
                info.update({
                    "duration_ms": len(audio),
                    "channels": audio.channels,
                    "sample_rate": audio.frame_rate,
                    "sample_width": audio.sample_width
                })
                
        except Exception as e:
            logger.debug(f"获取详细音频信息失败: {e}")
        
        return info


class AudioFormatValidator:
    """音频格式验证器"""
    
    @staticmethod
    def validate_for_tts(audio_data: bytes) -> tuple[bool, str]:
        """
        验证音频数据是否适合TTS使用
        
        Args:
            audio_data: 音频数据
            
        Returns:
            tuple[bool, str]: (是否有效, 错误信息)
        """
        if not audio_data:
            return False, "音频数据为空"
        
        if len(audio_data) < 100:  # 至少100字节
            return False, "音频数据过短"
        
        format_name = AudioFormatDetector.detect_format(audio_data)
        if format_name == "unknown":
            return False, "不支持的音频格式"
        
        # 获取音频信息进行进一步验证
        try:
            info = AudioFormatDetector.get_audio_info(audio_data)
            
            # 检查时长（至少50ms）
            if "duration_ms" in info and info["duration_ms"] < 50:
                return False, "音频时长过短"
            
            # 检查采样率（至少8kHz）
            if "sample_rate" in info and info["sample_rate"] < 8000:
                return False, "音频采样率过低"
            
            return True, "音频格式有效"
            
        except Exception as e:
            logger.warning(f"音频验证时发生异常: {e}")
            return True, "基础格式检查通过"  # 降级处理
    
    @staticmethod
    def validate_for_live2d(audio_data: bytes) -> tuple[bool, str]:
        """
        验证音频数据是否适合Live2D口型同步
        
        Args:
            audio_data: 音频数据
            
        Returns:
            tuple[bool, str]: (是否有效, 错误信息)
        """
        # 首先进行基础TTS验证
        is_valid, message = AudioFormatValidator.validate_for_tts(audio_data)
        if not is_valid:
            return is_valid, message
        
        # Live2D特定验证
        try:
            info = AudioFormatDetector.get_audio_info(audio_data)
            
            # Live2D推荐使用WAV格式
            if info["format"] != "wav":
                # 检查是否可以转换为WAV
                if not info["can_convert"]:
                    return False, "音频格式不支持转换为WAV"
            
            # 检查声道数（推荐单声道或立体声）
            if "channels" in info and info["channels"] > 2:
                return False, "音频声道数过多，Live2D推荐单声道或立体声"
            
            # 检查采样率（推荐16kHz以上）
            if "sample_rate" in info and info["sample_rate"] < 16000:
                return False, "音频采样率过低，Live2D推荐16kHz以上"
            
            return True, "音频适合Live2D口型同步"
            
        except Exception as e:
            logger.warning(f"Live2D音频验证时发生异常: {e}")
            return True, "基础格式检查通过"  # 降级处理
