"""
音频播放器

支持多种音频格式的播放，集成格式检测和转换功能
基于PySide6.QtMultimedia和pygame，增强TTS和Live2D支持
"""

import asyncio
import logging
from typing import Optional, Union
from pathlib import Path
import time
import tempfile

from .format_detector import AudioFormatDetector, AudioFormatValidator
from .converter import AudioConverter
from ..storage.temp_file_manager import get_global_temp_manager

try:
    from PySide6.QtMultimedia import QMediaPlayer, QAudioOutput, QAudioFormat
    from PySide6.QtCore import QUrl, QObject, Signal, QTimer
    PYSIDE6_MULTIMEDIA_AVAILABLE = True
except ImportError:
    PYSIDE6_MULTIMEDIA_AVAILABLE = False
    QMediaPlayer = None
    QAudioOutput = None
    QAudioFormat = None
    QObject = object
    Signal = None

# 备用音频播放方案
try:
    import pygame
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False
    pygame = None

logger = logging.getLogger(__name__)


class AudioPlayer(QObject if PYSIDE6_MULTIMEDIA_AVAILABLE else object):
    """
    增强的音频播放器

    支持多种音频格式，集成格式检测和转换功能
    优先使用pygame（更兼容TTS音频），备用PySide6.QtMultimedia
    """

    # 信号定义（仅在PySide6可用时）
    if PYSIDE6_MULTIMEDIA_AVAILABLE:
        playback_finished = Signal()
        playback_error = Signal(str)

    def __init__(self):
        if PYSIDE6_MULTIMEDIA_AVAILABLE:
            super().__init__()

        self.player = None
        self.audio_output = None
        self.is_initialized = False
        self.current_file = None
        self.start_time = None
        self.use_pygame = False

        # 音频处理组件
        self.audio_converter = AudioConverter()
        self.temp_manager = get_global_temp_manager()

        logger.info("✅ 增强音频播放器已创建")
    
    async def initialize(self) -> bool:
        """
        初始化音频播放器

        Returns:
            bool: 初始化是否成功
        """
        try:
            # 优先使用pygame（更兼容TTS音频格式）
            if PYGAME_AVAILABLE:
                success = await self._initialize_pygame()
                if success:
                    self.use_pygame = True
                    self.is_initialized = True
                    logger.info("✅ 使用pygame音频播放器（优先选择，兼容性更好）")
                    return True

            # 备用PySide6.QtMultimedia
            if PYSIDE6_MULTIMEDIA_AVAILABLE:
                success = await self._initialize_qt_multimedia()
                if success:
                    self.use_pygame = False
                    self.is_initialized = True
                    logger.info("✅ 使用PySide6.QtMultimedia音频播放器（备用）")
                    return True

            logger.error("❌ 没有可用的音频播放器")
            return False
            
        except Exception as e:
            logger.error(f"❌ 音频播放器初始化失败: {e}")
            return False
    
    async def _initialize_qt_multimedia(self) -> bool:
        """初始化Qt多媒体播放器"""
        try:
            self.player = QMediaPlayer()
            self.audio_output = QAudioOutput()
            self.player.setAudioOutput(self.audio_output)
            
            # 连接信号
            self.player.mediaStatusChanged.connect(self._on_media_status_changed)
            self.player.errorOccurred.connect(self._on_error_occurred)
            
            return True
            
        except Exception as e:
            logger.error(f"Qt多媒体播放器初始化失败: {e}")
            return False
    
    async def _initialize_pygame(self) -> bool:
        """初始化pygame音频播放器"""
        try:
            if not pygame.mixer.get_init():
                pygame.mixer.init(frequency=44100, size=-16, channels=2, buffer=2048)
            
            return True
            
        except Exception as e:
            logger.error(f"pygame音频播放器初始化失败: {e}")
            return False
    
    async def play(self, audio_file: Union[str, Path]) -> Optional[float]:
        """
        播放音频文件（基础方法，保持向后兼容）

        Args:
            audio_file: 音频文件路径

        Returns:
            Optional[float]: 播放时长（秒），如果播放失败返回None
        """
        if not self.is_initialized:
            logger.error("音频播放器未初始化")
            return None

        try:
            audio_path = Path(audio_file)
            if not audio_path.exists():
                logger.error(f"音频文件不存在: {audio_path}")
                return None

            self.current_file = audio_path
            self.start_time = time.time()

            # 根据初始化的播放器类型直接播放
            if self.use_pygame:
                return await self._play_with_pygame(audio_path)
            else:
                return await self._play_with_qt(audio_path)

        except Exception as e:
            logger.error(f"❌ 音频播放失败: {e}")
            return None

    async def play_audio_data(self, audio_data: bytes, optimize_for: str = "tts") -> Optional[float]:
        """
        播放音频数据（支持格式检测和转换）

        Args:
            audio_data: 音频数据字节
            optimize_for: 优化目标 ("tts", "live2d", "general")

        Returns:
            Optional[float]: 播放时长（秒），如果播放失败返回None
        """
        if not self.is_initialized:
            logger.error("音频播放器未初始化")
            return None

        try:
            # 1. 验证音频数据
            is_valid, message = AudioFormatValidator.validate_for_tts(audio_data)
            if not is_valid:
                logger.error(f"音频数据验证失败: {message}")
                return None

            # 2. 检测音频格式
            source_format = AudioFormatDetector.detect_format(audio_data)
            logger.info(f"检测到音频格式: {source_format}")

            # 3. 根据优化目标转换音频
            converted_data = await self._convert_audio_for_playback(audio_data, source_format, optimize_for)
            if not converted_data:
                logger.error("音频转换失败")
                return None

            # 4. 创建临时文件并播放
            temp_file_path = self.temp_manager.create_temp_audio_file(converted_data, "wav", "playback")

            try:
                duration = await self.play(temp_file_path)
                return duration
            finally:
                # 清理临时文件
                self.temp_manager.cleanup_file(temp_file_path)

        except Exception as e:
            logger.error(f"❌ 播放音频数据失败: {e}")
            return None

    async def play_for_live2d(self, audio_data: bytes) -> tuple[Optional[float], Optional[str]]:
        """
        为Live2D口型同步播放音频

        Args:
            audio_data: 音频数据

        Returns:
            tuple[Optional[float], Optional[str]]: (播放时长, WAV文件路径用于口型同步)
        """
        if not self.is_initialized:
            logger.error("音频播放器未初始化")
            return None, None

        try:
            # 1. Live2D特定验证
            is_valid, message = AudioFormatValidator.validate_for_live2d(audio_data)
            if not is_valid:
                logger.error(f"音频不适合Live2D: {message}")
                return None, None

            # 2. 转换为Live2D优化格式
            converted_data = self.audio_converter.convert_for_live2d(audio_data)
            if not converted_data:
                logger.error("Live2D音频转换失败")
                return None, None

            # 3. 创建持久的WAV文件（用于口型同步）
            wav_file_path = self.temp_manager.create_temp_file_with_hash(converted_data, "wav")

            # 4. 播放音频
            duration = await self.play(wav_file_path)

            if duration is not None:
                logger.info(f"✅ Live2D音频播放成功，时长: {duration:.2f}秒")
                return duration, wav_file_path
            else:
                # 播放失败，清理文件
                self.temp_manager.cleanup_file(wav_file_path)
                return None, None

        except Exception as e:
            logger.error(f"❌ Live2D音频播放失败: {e}")
            return None, None

    async def _convert_audio_for_playback(self, audio_data: bytes, source_format: str, optimize_for: str) -> Optional[bytes]:
        """
        根据播放需求转换音频格式

        Args:
            audio_data: 源音频数据
            source_format: 源格式
            optimize_for: 优化目标

        Returns:
            Optional[bytes]: 转换后的音频数据
        """
        try:
            if optimize_for == "live2d":
                return self.audio_converter.convert_for_live2d(audio_data, source_format)
            elif optimize_for == "tts":
                return self.audio_converter.convert_for_tts(audio_data, source_format)
            else:
                # 通用转换：确保是WAV格式
                if source_format == "wav":
                    return audio_data
                else:
                    return AudioFormatDetector.convert_to_wav(audio_data, source_format)

        except Exception as e:
            logger.error(f"音频转换失败: {e}")
            return None
    
    async def _play_with_qt(self, audio_path: Path) -> Optional[float]:
        """使用Qt播放音频"""
        try:
            # 设置音频源
            file_url = QUrl.fromLocalFile(str(audio_path.absolute()))
            self.player.setSource(file_url)
            
            # 开始播放
            self.player.play()
            
            # 等待播放完成
            duration = await self._wait_for_qt_playback()
            
            logger.info(f"✅ Qt音频播放完成，时长: {duration:.2f}秒")
            return duration
            
        except Exception as e:
            logger.error(f"Qt音频播放失败: {e}")
            return None
    
    async def _play_with_pygame(self, audio_path: Path) -> Optional[float]:
        """使用pygame播放音频"""
        try:
            # 加载并播放音频
            pygame.mixer.music.load(str(audio_path))
            pygame.mixer.music.play()
            
            # 等待播放完成
            duration = await self._wait_for_pygame_playback()
            
            logger.info(f"✅ pygame音频播放完成，时长: {duration:.2f}秒")
            return duration
            
        except Exception as e:
            logger.error(f"pygame音频播放失败: {e}")
            return None
    
    async def _wait_for_qt_playback(self) -> float:
        """等待Qt播放完成"""
        while True:
            state = self.player.playbackState()
            if state == QMediaPlayer.PlaybackState.StoppedState:
                break
            elif state == QMediaPlayer.PlaybackState.PausedState:
                # 如果暂停了，继续播放
                self.player.play()
            
            await asyncio.sleep(0.1)
        
        return time.time() - self.start_time if self.start_time else 0.0
    
    async def _wait_for_pygame_playback(self) -> float:
        """等待pygame播放完成"""
        while pygame.mixer.music.get_busy():
            await asyncio.sleep(0.1)
        
        return time.time() - self.start_time if self.start_time else 0.0
    
    async def stop(self):
        """停止播放"""
        try:
            if not self.is_initialized:
                return
            
            if self.use_pygame:
                pygame.mixer.music.stop()
            else:
                if self.player:
                    self.player.stop()
            
            logger.info("✅ 音频播放已停止")
            
        except Exception as e:
            logger.error(f"停止音频播放失败: {e}")
    
    async def pause(self):
        """暂停播放"""
        try:
            if not self.is_initialized:
                return
            
            if self.use_pygame:
                pygame.mixer.music.pause()
            else:
                if self.player:
                    self.player.pause()
            
            logger.info("✅ 音频播放已暂停")
            
        except Exception as e:
            logger.error(f"暂停音频播放失败: {e}")
    
    async def resume(self):
        """恢复播放"""
        try:
            if not self.is_initialized:
                return
            
            if self.use_pygame:
                pygame.mixer.music.unpause()
            else:
                if self.player:
                    self.player.play()
            
            logger.info("✅ 音频播放已恢复")
            
        except Exception as e:
            logger.error(f"恢复音频播放失败: {e}")
    
    async def is_playing(self) -> bool:
        """检查是否正在播放"""
        try:
            if not self.is_initialized:
                return False
            
            if self.use_pygame:
                return pygame.mixer.music.get_busy()
            else:
                if self.player:
                    return self.player.playbackState() == QMediaPlayer.PlaybackState.PlayingState
                return False
                
        except Exception as e:
            logger.error(f"检查播放状态失败: {e}")
            return False
    
    async def get_volume(self) -> float:
        """获取音量（0.0-1.0）"""
        try:
            if not self.is_initialized:
                return 1.0
            
            if self.use_pygame:
                return pygame.mixer.music.get_volume()
            else:
                if self.audio_output:
                    return self.audio_output.volume()
                return 1.0
                
        except Exception as e:
            logger.error(f"获取音量失败: {e}")
            return 1.0
    
    async def set_volume(self, volume: float):
        """设置音量（0.0-1.0）"""
        try:
            if not self.is_initialized:
                return
            
            # 限制音量范围
            volume = max(0.0, min(1.0, volume))
            
            if self.use_pygame:
                pygame.mixer.music.set_volume(volume)
            else:
                if self.audio_output:
                    self.audio_output.setVolume(volume)
            
            logger.info(f"设置音量: {volume:.2f}")
            
        except Exception as e:
            logger.error(f"设置音量失败: {e}")
    
    def _on_media_status_changed(self, status):
        """Qt媒体状态变化处理"""
        if status == QMediaPlayer.MediaStatus.EndOfMedia:
            if PYSIDE6_MULTIMEDIA_AVAILABLE:
                self.playback_finished.emit()
    
    def _on_error_occurred(self, error, error_string):
        """Qt播放错误处理"""
        logger.error(f"Qt音频播放错误: {error_string}")
        if PYSIDE6_MULTIMEDIA_AVAILABLE:
            self.playback_error.emit(error_string)
    
    async def cleanup(self):
        """清理资源"""
        try:
            await self.stop()

            if self.player:
                self.player.deleteLater()
                self.player = None

            if self.audio_output:
                self.audio_output.deleteLater()
                self.audio_output = None

            if self.use_pygame and pygame.mixer.get_init():
                pygame.mixer.quit()

            # 清理临时文件
            try:
                self.temp_manager.cleanup_expired_files()
            except Exception as e:
                logger.warning(f"清理临时文件时出错: {e}")

            self.is_initialized = False
            logger.info("✅ 音频播放器清理完成")

        except Exception as e:
            logger.error(f"音频播放器清理失败: {e}")

    async def get_audio_info(self, audio_data: bytes) -> dict:
        """
        获取音频信息

        Args:
            audio_data: 音频数据

        Returns:
            dict: 音频信息
        """
        try:
            return AudioFormatDetector.get_audio_info(audio_data)
        except Exception as e:
            logger.error(f"获取音频信息失败: {e}")
            return {"error": str(e)}

    async def validate_audio_data(self, audio_data: bytes, for_live2d: bool = False) -> tuple[bool, str]:
        """
        验证音频数据

        Args:
            audio_data: 音频数据
            for_live2d: 是否为Live2D验证

        Returns:
            tuple[bool, str]: (是否有效, 消息)
        """
        try:
            if for_live2d:
                return AudioFormatValidator.validate_for_live2d(audio_data)
            else:
                return AudioFormatValidator.validate_for_tts(audio_data)
        except Exception as e:
            logger.error(f"音频验证失败: {e}")
            return False, f"验证异常: {e}"
    
    def __del__(self):
        """析构函数"""
        try:
            if self.use_pygame and pygame and pygame.mixer.get_init():
                pygame.mixer.quit()
        except:
            pass
