"""
临时文件管理器模块

基于原aipet项目实现，提供TTS临时音频文件的创建、管理和清理功能
"""

import os
import time
import tempfile
import threading
import logging
from pathlib import Path
from typing import Optional, List, Dict
import hashlib

logger = logging.getLogger(__name__)


class TempFileManager:
    """临时文件管理器 - 基于原aipet项目实现"""
    
    def __init__(self, base_dir: Optional[str] = None, max_age_hours: int = 24):
        """
        初始化临时文件管理器
        
        Args:
            base_dir: 临时文件基础目录，None则使用系统临时目录
            max_age_hours: 文件最大保存时间（小时）
        """
        if base_dir:
            self.temp_dir = Path(base_dir) / "aipetv2_tts"
        else:
            self.temp_dir = Path(tempfile.gettempdir()) / "aipetv2_tts"
        
        self.max_age_hours = max_age_hours
        self.max_age_seconds = max_age_hours * 3600
        self._lock = threading.Lock()
        self._file_registry = {}  # 文件注册表，记录创建时间等信息
        
        # 确保临时目录存在
        self._ensure_temp_dir()
        
        # 启动时清理一次过期文件
        self.cleanup_expired_files()
        
        logger.info(f"临时文件管理器初始化完成: {self.temp_dir}")
    
    def _ensure_temp_dir(self):
        """确保临时目录存在"""
        try:
            self.temp_dir.mkdir(parents=True, exist_ok=True)
            logger.debug(f"临时目录已创建: {self.temp_dir}")
        except Exception as e:
            logger.error(f"创建临时目录失败: {e}")
            raise
    
    def create_temp_audio_file(self, audio_data: bytes, format: str, prefix: str = "tts_audio") -> str:
        """
        创建临时音频文件
        
        Args:
            audio_data: 音频数据
            format: 音频格式（wav, mp3等）
            prefix: 文件名前缀
            
        Returns:
            str: 临时文件的完整路径
        """
        try:
            with self._lock:
                # 生成唯一文件名
                timestamp = int(time.time() * 1000)
                thread_id = threading.get_ident()
                filename = f"{prefix}_{timestamp}_{thread_id}.{format}"
                file_path = self.temp_dir / filename
                
                # 写入音频数据
                with open(file_path, 'wb') as f:
                    f.write(audio_data)
                
                # 注册文件信息
                self._file_registry[str(file_path)] = {
                    "created_time": time.time(),
                    "size": len(audio_data),
                    "format": format,
                    "prefix": prefix
                }
                
                logger.debug(f"创建临时音频文件: {file_path} ({len(audio_data)} bytes)")
                return str(file_path)
                
        except Exception as e:
            logger.error(f"创建临时音频文件失败: {e}")
            raise
    
    def create_temp_file_with_hash(self, audio_data: bytes, format: str) -> str:
        """
        使用内容哈希创建临时文件（避免重复文件）
        
        Args:
            audio_data: 音频数据
            format: 音频格式
            
        Returns:
            str: 临时文件路径
        """
        try:
            # 计算内容哈希
            content_hash = hashlib.md5(audio_data).hexdigest()[:12]
            filename = f"tts_hash_{content_hash}.{format}"
            file_path = self.temp_dir / filename
            
            with self._lock:
                # 如果文件已存在且内容相同，直接返回
                if file_path.exists():
                    try:
                        existing_data = file_path.read_bytes()
                        if existing_data == audio_data:
                            logger.debug(f"复用现有临时文件: {file_path}")
                            # 更新访问时间
                            if str(file_path) in self._file_registry:
                                self._file_registry[str(file_path)]["last_access"] = time.time()
                            return str(file_path)
                    except Exception:
                        pass  # 如果读取失败，重新创建
                
                # 创建新文件
                with open(file_path, 'wb') as f:
                    f.write(audio_data)
                
                # 注册文件信息
                self._file_registry[str(file_path)] = {
                    "created_time": time.time(),
                    "last_access": time.time(),
                    "size": len(audio_data),
                    "format": format,
                    "content_hash": content_hash
                }
                
                logger.debug(f"创建哈希临时文件: {file_path}")
                return str(file_path)
                
        except Exception as e:
            logger.error(f"创建哈希临时文件失败: {e}")
            raise
    
    def cleanup_file(self, file_path: str) -> bool:
        """
        清理指定的临时文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否成功清理
        """
        try:
            path = Path(file_path)
            
            with self._lock:
                # 删除文件
                if path.exists():
                    path.unlink()
                    logger.debug(f"删除临时文件: {file_path}")
                
                # 从注册表中移除
                if file_path in self._file_registry:
                    del self._file_registry[file_path]
                
                return True
                
        except Exception as e:
            logger.error(f"清理临时文件失败 {file_path}: {e}")
            return False
    
    def cleanup_expired_files(self) -> int:
        """
        清理过期的临时文件
        
        Returns:
            int: 清理的文件数量
        """
        cleaned_count = 0
        current_time = time.time()
        
        try:
            with self._lock:
                # 获取所有临时文件
                if not self.temp_dir.exists():
                    return 0
                
                temp_files = list(self.temp_dir.glob("tts_*"))
                
                for file_path in temp_files:
                    try:
                        # 检查文件年龄
                        file_age = current_time - file_path.stat().st_mtime
                        
                        if file_age > self.max_age_seconds:
                            # 删除过期文件
                            file_path.unlink()
                            cleaned_count += 1
                            logger.debug(f"清理过期临时文件: {file_path}")
                            
                            # 从注册表中移除
                            file_path_str = str(file_path)
                            if file_path_str in self._file_registry:
                                del self._file_registry[file_path_str]
                    
                    except Exception as e:
                        logger.warning(f"清理文件时出错 {file_path}: {e}")
                        continue
                
                if cleaned_count > 0:
                    logger.info(f"清理了 {cleaned_count} 个过期临时文件")
                
                return cleaned_count
                
        except Exception as e:
            logger.error(f"清理过期文件时发生异常: {e}")
            return cleaned_count
    
    def get_temp_dir_info(self) -> Dict:
        """
        获取临时目录信息
        
        Returns:
            Dict: 目录信息
        """
        try:
            info = {
                "temp_dir": str(self.temp_dir),
                "exists": self.temp_dir.exists(),
                "file_count": 0,
                "total_size": 0,
                "registered_files": len(self._file_registry)
            }
            
            if self.temp_dir.exists():
                temp_files = list(self.temp_dir.glob("tts_*"))
                info["file_count"] = len(temp_files)
                
                total_size = 0
                for file_path in temp_files:
                    try:
                        total_size += file_path.stat().st_size
                    except Exception:
                        continue
                
                info["total_size"] = total_size
                info["total_size_mb"] = round(total_size / (1024 * 1024), 2)
            
            return info
            
        except Exception as e:
            logger.error(f"获取临时目录信息失败: {e}")
            return {"error": str(e)}
    
    def cleanup_all(self) -> int:
        """
        清理所有临时文件
        
        Returns:
            int: 清理的文件数量
        """
        cleaned_count = 0
        
        try:
            with self._lock:
                if not self.temp_dir.exists():
                    return 0
                
                temp_files = list(self.temp_dir.glob("tts_*"))
                
                for file_path in temp_files:
                    try:
                        file_path.unlink()
                        cleaned_count += 1
                        logger.debug(f"清理临时文件: {file_path}")
                    except Exception as e:
                        logger.warning(f"清理文件时出错 {file_path}: {e}")
                        continue
                
                # 清空注册表
                self._file_registry.clear()
                
                logger.info(f"清理了所有 {cleaned_count} 个临时文件")
                return cleaned_count
                
        except Exception as e:
            logger.error(f"清理所有临时文件时发生异常: {e}")
            return cleaned_count
    
    def get_file_info(self, file_path: str) -> Optional[Dict]:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            Optional[Dict]: 文件信息，不存在返回None
        """
        try:
            with self._lock:
                if file_path in self._file_registry:
                    info = self._file_registry[file_path].copy()
                    
                    # 添加文件系统信息
                    path = Path(file_path)
                    if path.exists():
                        stat = path.stat()
                        info.update({
                            "exists": True,
                            "actual_size": stat.st_size,
                            "modified_time": stat.st_mtime,
                            "age_seconds": time.time() - stat.st_mtime
                        })
                    else:
                        info["exists"] = False
                    
                    return info
                
                return None
                
        except Exception as e:
            logger.error(f"获取文件信息失败 {file_path}: {e}")
            return None
    
    def __del__(self):
        """析构函数，清理资源"""
        try:
            # 可选：在程序退出时清理临时文件
            # self.cleanup_all()
            pass
        except Exception:
            pass


# 全局临时文件管理器实例
_global_temp_manager = None
_manager_lock = threading.Lock()


def get_global_temp_manager() -> TempFileManager:
    """
    获取全局临时文件管理器实例（单例模式）
    
    Returns:
        TempFileManager: 全局临时文件管理器
    """
    global _global_temp_manager
    
    if _global_temp_manager is None:
        with _manager_lock:
            if _global_temp_manager is None:
                _global_temp_manager = TempFileManager()
                logger.debug("创建全局临时文件管理器实例")
    
    return _global_temp_manager
