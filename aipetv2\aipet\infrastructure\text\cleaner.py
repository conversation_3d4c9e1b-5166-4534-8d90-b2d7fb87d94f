"""
TTS文本清理器

基于原aipet项目实现，提供高级文本清理功能
移除工具调用块、Markdown语法、HTML标签等不适合TTS的内容
"""

import re
import logging
from typing import Optional, List, Dict, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class CleaningRule:
    """文本清理规则"""
    name: str
    pattern: str
    replacement: str = ""
    flags: int = re.MULTILINE | re.DOTALL
    enabled: bool = True
    description: str = ""


class TTSTextCleaner:
    """TTS文本清理器 - 基于原aipet项目实现"""
    
    def __init__(self):
        """初始化文本清理器"""
        self.cleaning_rules = self._initialize_cleaning_rules()
        self.custom_rules: List[CleaningRule] = []
        
        # 清理统计
        self.stats = {
            "total_cleaned": 0,
            "rules_applied": {},
            "average_reduction_ratio": 0.0
        }
        
        logger.info("✅ TTS文本清理器初始化完成")
    
    def _initialize_cleaning_rules(self) -> List[CleaningRule]:
        """初始化默认清理规则"""
        rules = [
            # 1. 工具调用块清理
            CleaningRule(
                name="vcp_tool_calls",
                pattern=r'<<<\[TOOL_REQUEST\]>>>.*?<<<\[END_TOOL_REQUEST\]>>>',
                replacement="",
                description="移除VCP格式的工具调用块"
            ),
            
            CleaningRule(
                name="function_calls",
                pattern=r'<function_calls>.*?</function_calls>',
                replacement="",
                description="移除function_calls格式的工具调用块"
            ),

            # 2. Markdown语法清理
            CleaningRule(
                name="markdown_headers",
                pattern=r'^#{1,6}\s+.*$',
                replacement="",
                flags=re.MULTILINE,
                description="移除Markdown标题"
            ),

            CleaningRule(
                name="markdown_code_blocks",
                pattern=r'```.*?```',
                replacement="",
                description="移除Markdown代码块"
            ),

            CleaningRule(
                name="markdown_inline_code",
                pattern=r'`[^`]+`',
                replacement="",
                description="移除Markdown行内代码"
            ),

            CleaningRule(
                name="markdown_links",
                pattern=r'\[([^\]]+)\]\([^\)]+\)',
                replacement=r'\1',
                description="保留链接文本，移除链接格式"
            ),

            CleaningRule(
                name="markdown_bold_italic",
                pattern=r'\*{1,2}([^*]+)\*{1,2}',
                replacement=r'\1',
                description="移除粗体和斜体格式，保留文本"
            ),

            # 3. HTML标签清理
            CleaningRule(
                name="html_tags",
                pattern=r'<[^>]+>',
                replacement="",
                description="移除所有HTML标签"
            ),

            # 4. 特殊字符清理
            CleaningRule(
                name="special_chars",
                pattern=r'[^\w\s\u4e00-\u9fff.,!?;:()"\'-]',
                replacement="",
                flags=re.UNICODE,
                description="移除特殊字符，保留中英文、标点符号"
            ),

            # 5. 空白字符规范化
            CleaningRule(
                name="normalize_whitespace",
                pattern=r'\s+',
                replacement=" ",
                description="规范化空白字符"
            ),

            CleaningRule(
                name="remove_empty_lines",
                pattern=r'\n\s*\n',
                replacement="\n",
                description="移除空行"
            )
        ]

        return rules

    def clean_text(self, text: str, custom_rules_only: bool = False) -> str:
        """
        清理文本内容

        Args:
            text: 待清理的文本
            custom_rules_only: 是否只应用自定义规则

        Returns:
            清理后的文本
        """
        if not text or not text.strip():
            return ""

        original_length = len(text)
        cleaned_text = text
        applied_rules = []

        # 选择要应用的规则
        rules_to_apply = self.custom_rules if custom_rules_only else (self.cleaning_rules + self.custom_rules)

        # 应用清理规则
        for rule in rules_to_apply:
            if not rule.enabled:
                continue

            try:
                before_length = len(cleaned_text)
                cleaned_text = self.apply_rule(cleaned_text, rule)
                after_length = len(cleaned_text)

                if before_length != after_length:
                    applied_rules.append(rule.name)

            except Exception as e:
                logger.warning(f"应用清理规则 {rule.name} 时出错: {e}")
                continue

        # 最终清理：去除首尾空白
        cleaned_text = cleaned_text.strip()

        # 更新统计信息
        self._update_stats(original_length, len(cleaned_text), applied_rules)

        logger.debug(f"文本清理完成: {original_length} -> {len(cleaned_text)} 字符，应用规则: {applied_rules}")

        return cleaned_text

    def apply_rule(self, text: str, rule: CleaningRule) -> str:
        """
        应用单个清理规则

        Args:
            text: 待处理文本
            rule: 清理规则

        Returns:
            处理后的文本
        """
        try:
            pattern = re.compile(rule.pattern, rule.flags)
            result = pattern.sub(rule.replacement, text)
            return result
        except Exception as e:
            logger.error(f"应用规则 {rule.name} 失败: {e}")
            return text

    def add_custom_rule(self, rule: CleaningRule) -> bool:
        """
        添加自定义清理规则

        Args:
            rule: 自定义规则

        Returns:
            是否添加成功
        """
        try:
            # 验证规则的正则表达式
            re.compile(rule.pattern, rule.flags)

            # 检查是否已存在同名规则
            for existing_rule in self.custom_rules:
                if existing_rule.name == rule.name:
                    logger.warning(f"自定义规则 {rule.name} 已存在，将被替换")
                    self.custom_rules.remove(existing_rule)
                    break

            self.custom_rules.append(rule)
            logger.info(f"✅ 添加自定义清理规则: {rule.name}")
            return True

        except re.error as e:
            logger.error(f"自定义规则 {rule.name} 的正则表达式无效: {e}")
            return False
        except Exception as e:
            logger.error(f"添加自定义规则失败: {e}")
            return False

    def remove_custom_rule(self, rule_name: str) -> bool:
        """
        移除自定义清理规则

        Args:
            rule_name: 规则名称

        Returns:
            是否移除成功
        """
        for rule in self.custom_rules:
            if rule.name == rule_name:
                self.custom_rules.remove(rule)
                logger.info(f"✅ 移除自定义清理规则: {rule_name}")
                return True

        logger.warning(f"未找到自定义规则: {rule_name}")
        return False

    def enable_rule(self, rule_name: str) -> bool:
        """启用指定规则"""
        return self._toggle_rule(rule_name, True)

    def disable_rule(self, rule_name: str) -> bool:
        """禁用指定规则"""
        return self._toggle_rule(rule_name, False)

    def _toggle_rule(self, rule_name: str, enabled: bool) -> bool:
        """切换规则启用状态"""
        all_rules = self.cleaning_rules + self.custom_rules

        for rule in all_rules:
            if rule.name == rule_name:
                rule.enabled = enabled
                status = "启用" if enabled else "禁用"
                logger.info(f"✅ {status}清理规则: {rule_name}")
                return True

        logger.warning(f"未找到规则: {rule_name}")
        return False

    def get_cleaning_stats(self) -> Dict[str, Any]:
        """
        获取清理统计信息

        Returns:
            统计信息字典
        """
        return {
            "total_cleaned": self.stats["total_cleaned"],
            "rules_applied": dict(self.stats["rules_applied"]),
            "average_reduction_ratio": self.stats["average_reduction_ratio"],
            "available_rules": [rule.name for rule in self.cleaning_rules],
            "custom_rules": [rule.name for rule in self.custom_rules],
            "enabled_rules": [rule.name for rule in (self.cleaning_rules + self.custom_rules) if rule.enabled]
        }

    def _update_stats(self, original_length: int, cleaned_length: int, applied_rules: List[str]):
        """更新统计信息"""
        self.stats["total_cleaned"] += 1

        # 更新规则应用次数
        for rule_name in applied_rules:
            self.stats["rules_applied"][rule_name] = self.stats["rules_applied"].get(rule_name, 0) + 1

        # 更新平均压缩比
        if original_length > 0:
            reduction_ratio = (original_length - cleaned_length) / original_length
            current_avg = self.stats["average_reduction_ratio"]
            total_count = self.stats["total_cleaned"]

            # 计算新的平均值
            self.stats["average_reduction_ratio"] = (current_avg * (total_count - 1) + reduction_ratio) / total_count

    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            "total_cleaned": 0,
            "rules_applied": {},
            "average_reduction_ratio": 0.0
        }
        logger.info("✅ 清理统计信息已重置")

    def preview_cleaning(self, text: str, max_preview_length: int = 200) -> Dict[str, str]:
        """
        预览文本清理效果

        Args:
            text: 待清理文本
            max_preview_length: 预览最大长度

        Returns:
            包含原文和清理后文本的字典
        """
        cleaned = self.clean_text(text)

        # 截断预览文本
        original_preview = text[:max_preview_length] + ("..." if len(text) > max_preview_length else "")
        cleaned_preview = cleaned[:max_preview_length] + ("..." if len(cleaned) > max_preview_length else "")

        return {
            "original": original_preview,
            "cleaned": cleaned_preview,
            "original_length": len(text),
            "cleaned_length": len(cleaned),
            "reduction_ratio": (len(text) - len(cleaned)) / len(text) if len(text) > 0 else 0
        }
