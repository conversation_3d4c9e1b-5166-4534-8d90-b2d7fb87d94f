"""
TTS文本分段器

基于原aipet项目实现，提供智能文本分段功能
支持按句子、标点符号、长度等多种方式分段
"""

import re
import logging
from typing import List, Optional, Dict, Any
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class SegmentationType(Enum):
    """分段类型"""
    SENTENCE = "sentence"  # 按句子分段
    PUNCTUATION = "punctuation"  # 按标点符号分段
    LENGTH = "length"  # 按长度分段
    MIXED = "mixed"  # 混合分段


@dataclass
class SegmentationConfig:
    """分段配置"""
    type: SegmentationType = SegmentationType.MIXED
    max_length: int = 100  # 最大分段长度
    min_length: int = 10   # 最小分段长度
    sentence_endings: List[str] = None  # 句子结束符
    pause_markers: List[str] = None     # 停顿标记
    preserve_formatting: bool = False   # 是否保留格式
    
    def __post_init__(self):
        if self.sentence_endings is None:
            self.sentence_endings = ['。', '！', '？', '.', '!', '?', '；', ';']
        if self.pause_markers is None:
            self.pause_markers = ['，', ',', '、', '：', ':']


class TTSTextSegmenter:
    """TTS文本分段器 - 基于原aipet项目实现"""
    
    def __init__(self, config: Optional[SegmentationConfig] = None):
        """初始化文本分段器"""
        self.config = config or SegmentationConfig()
        
        # 分段统计
        self.stats = {
            "total_segmented": 0,
            "average_segments_per_text": 0.0,
            "average_segment_length": 0.0
        }
        
        logger.info("✅ TTS文本分段器初始化完成")
    
    def segment_text(self, text: str) -> List[str]:
        """
        分段文本
        
        Args:
            text: 待分段的文本
            
        Returns:
            分段后的文本列表
        """
        if not text or not text.strip():
            return []
        
        text = text.strip()
        
        # 根据配置选择分段方法
        if self.config.type == SegmentationType.SENTENCE:
            segments = self._segment_by_sentence(text)
        elif self.config.type == SegmentationType.PUNCTUATION:
            segments = self._segment_by_punctuation(text)
        elif self.config.type == SegmentationType.LENGTH:
            segments = self._segment_by_length(text)
        else:  # MIXED
            segments = self._segment_mixed(text)
        
        # 后处理：清理和验证分段
        segments = self._post_process_segments(segments)
        
        # 更新统计信息
        self._update_stats(text, segments)
        
        logger.debug(f"文本分段完成: 原文 {len(text)} 字符 -> {len(segments)} 个分段")
        
        return segments
    
    def _segment_by_sentence(self, text: str) -> List[str]:
        """按句子分段"""
        # 构建句子结束符的正则表达式
        endings_pattern = '|'.join(re.escape(ending) for ending in self.config.sentence_endings)
        pattern = f'([^{endings_pattern}]*[{endings_pattern}]+)'
        
        segments = []
        matches = re.findall(pattern, text)
        
        # 处理匹配到的句子
        for match in matches:
            sentence = match.strip()
            if sentence and len(sentence) >= self.config.min_length:
                # 如果句子太长，进一步分段
                if len(sentence) > self.config.max_length:
                    sub_segments = self._segment_by_length(sentence)
                    segments.extend(sub_segments)
                else:
                    segments.append(sentence)
        
        # 处理剩余文本（没有句子结束符的部分）
        remaining_text = re.sub(pattern, '', text).strip()
        if remaining_text and len(remaining_text) >= self.config.min_length:
            if len(remaining_text) > self.config.max_length:
                sub_segments = self._segment_by_length(remaining_text)
                segments.extend(sub_segments)
            else:
                segments.append(remaining_text)
        
        return segments
    
    def _segment_by_punctuation(self, text: str) -> List[str]:
        """按标点符号分段"""
        # 构建所有分段标记的正则表达式
        all_markers = self.config.sentence_endings + self.config.pause_markers
        markers_pattern = '|'.join(re.escape(marker) for marker in all_markers)
        pattern = f'([^{markers_pattern}]*[{markers_pattern}]+)'
        
        segments = []
        matches = re.findall(pattern, text)
        
        current_segment = ""
        
        for match in matches:
            segment_part = match.strip()
            if not segment_part:
                continue
            
            # 累积分段内容
            if current_segment:
                current_segment += segment_part
            else:
                current_segment = segment_part
            
            # 检查是否应该结束当前分段
            should_end = False
            
            # 如果遇到句子结束符，结束分段
            if any(ending in segment_part for ending in self.config.sentence_endings):
                should_end = True
            # 如果长度超过最大限制，结束分段
            elif len(current_segment) >= self.config.max_length:
                should_end = True
            
            if should_end and len(current_segment) >= self.config.min_length:
                segments.append(current_segment)
                current_segment = ""
        
        # 处理最后的分段
        if current_segment and len(current_segment) >= self.config.min_length:
            segments.append(current_segment)
        
        # 处理剩余文本
        remaining_text = re.sub(pattern, '', text).strip()
        if remaining_text and len(remaining_text) >= self.config.min_length:
            segments.append(remaining_text)
        
        return segments
    
    def _segment_by_length(self, text: str) -> List[str]:
        """按长度分段"""
        segments = []
        current_pos = 0
        
        while current_pos < len(text):
            # 计算当前分段的结束位置
            end_pos = min(current_pos + self.config.max_length, len(text))
            
            # 如果不是最后一段，尝试在合适的位置断开
            if end_pos < len(text):
                # 寻找最近的标点符号或空格
                best_break = end_pos
                
                # 向前搜索合适的断点
                for i in range(end_pos - 1, current_pos + self.config.min_length - 1, -1):
                    char = text[i]
                    if char in self.config.sentence_endings:
                        best_break = i + 1
                        break
                    elif char in self.config.pause_markers or char.isspace():
                        best_break = i + 1
                
                end_pos = best_break
            
            # 提取分段
            segment = text[current_pos:end_pos].strip()
            if segment and len(segment) >= self.config.min_length:
                segments.append(segment)
            
            current_pos = end_pos
        
        return segments
    
    def _segment_mixed(self, text: str) -> List[str]:
        """混合分段策略"""
        # 首先按句子分段
        sentence_segments = self._segment_by_sentence(text)
        
        # 对过长的句子进一步按标点符号分段
        final_segments = []
        for segment in sentence_segments:
            if len(segment) > self.config.max_length:
                punct_segments = self._segment_by_punctuation(segment)
                
                # 对仍然过长的分段按长度分段
                for punct_segment in punct_segments:
                    if len(punct_segment) > self.config.max_length:
                        length_segments = self._segment_by_length(punct_segment)
                        final_segments.extend(length_segments)
                    else:
                        final_segments.append(punct_segment)
            else:
                final_segments.append(segment)
        
        return final_segments
    
    def _post_process_segments(self, segments: List[str]) -> List[str]:
        """后处理分段"""
        processed_segments = []
        
        for segment in segments:
            # 清理空白字符
            cleaned_segment = segment.strip()
            
            # 跳过空分段和过短分段
            if not cleaned_segment or len(cleaned_segment) < self.config.min_length:
                continue
            
            # 如果不保留格式，进一步清理
            if not self.config.preserve_formatting:
                # 规范化空白字符
                cleaned_segment = re.sub(r'\s+', ' ', cleaned_segment)
            
            processed_segments.append(cleaned_segment)
        
        return processed_segments
    
    def _update_stats(self, original_text: str, segments: List[str]):
        """更新统计信息"""
        self.stats["total_segmented"] += 1
        
        # 更新平均分段数
        current_avg_segments = self.stats["average_segments_per_text"]
        total_count = self.stats["total_segmented"]
        self.stats["average_segments_per_text"] = (current_avg_segments * (total_count - 1) + len(segments)) / total_count
        
        # 更新平均分段长度
        if segments:
            avg_length = sum(len(seg) for seg in segments) / len(segments)
            current_avg_length = self.stats["average_segment_length"]
            self.stats["average_segment_length"] = (current_avg_length * (total_count - 1) + avg_length) / total_count
    
    def get_segmentation_stats(self) -> Dict[str, Any]:
        """获取分段统计信息"""
        return {
            "total_segmented": self.stats["total_segmented"],
            "average_segments_per_text": self.stats["average_segments_per_text"],
            "average_segment_length": self.stats["average_segment_length"],
            "config": {
                "type": self.config.type.value,
                "max_length": self.config.max_length,
                "min_length": self.config.min_length,
                "sentence_endings": self.config.sentence_endings,
                "pause_markers": self.config.pause_markers
            }
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            "total_segmented": 0,
            "average_segments_per_text": 0.0,
            "average_segment_length": 0.0
        }
        logger.info("✅ 分段统计信息已重置")
    
    def update_config(self, **kwargs):
        """更新配置"""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                logger.info(f"✅ 更新分段配置: {key} = {value}")
            else:
                logger.warning(f"未知配置项: {key}")
