"""
TTS双队列管理器

基于原aipet项目实现，提供文本队列和音频队列的双队列系统
支持并发控制、中断机制和优先级管理
"""

import asyncio
import logging
import threading
import time
from typing import Optional, Dict, Any, List, Callable
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import uuid

logger = logging.getLogger(__name__)


class TTSTaskPriority(Enum):
    """TTS任务优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


class TTSTaskStatus(Enum):
    """TTS任务状态"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class TTSTask:
    """TTS任务数据结构"""
    task_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    text: str = ""
    service: Optional[str] = None
    speaker: Optional[str] = None
    priority: TTSTaskPriority = TTSTaskPriority.NORMAL
    status: TTSTaskStatus = TTSTaskStatus.PENDING
    created_time: float = field(default_factory=time.time)
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    audio_data: Optional[bytes] = None
    audio_file_path: Optional[str] = None
    error_message: Optional[str] = None
    callback: Optional[Callable] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def get_processing_time(self) -> Optional[float]:
        """获取处理时间"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None
    
    def get_wait_time(self) -> float:
        """获取等待时间"""
        start = self.start_time or time.time()
        return start - self.created_time


class TTSQueueManager:
    """TTS双队列管理器 - 基于原aipet项目实现"""
    
    def __init__(self, max_text_queue_size: int = 100, max_audio_queue_size: int = 50):
        """
        初始化双队列管理器
        
        Args:
            max_text_queue_size: 文本队列最大大小
            max_audio_queue_size: 音频队列最大大小
        """
        # 队列配置
        self.max_text_queue_size = max_text_queue_size
        self.max_audio_queue_size = max_audio_queue_size
        
        # 文本处理队列（待合成的文本任务）
        self.text_queue = asyncio.Queue(maxsize=max_text_queue_size)
        
        # 音频播放队列（已合成的音频任务）
        self.audio_queue = asyncio.Queue(maxsize=max_audio_queue_size)
        
        # 任务管理
        self.active_tasks: Dict[str, TTSTask] = {}
        self.completed_tasks: List[TTSTask] = []
        self.max_completed_history = 1000
        
        # 工作线程控制
        self.text_worker_running = False
        self.audio_worker_running = False
        self.text_worker_task = None
        self.audio_worker_task = None
        
        # 中断控制
        self.interrupt_event = asyncio.Event()
        self.current_playing_task_id = None
        
        # 统计信息
        self.stats = {
            "total_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "cancelled_tasks": 0,
            "average_processing_time": 0.0,
            "average_wait_time": 0.0
        }
        
        # 锁
        self._lock = asyncio.Lock()
        
        logger.info("✅ TTS双队列管理器初始化完成")
    
    async def start(self):
        """启动队列管理器"""
        try:
            async with self._lock:
                if self.text_worker_running or self.audio_worker_running:
                    logger.warning("队列管理器已在运行")
                    return
                
                # 启动文本处理工作线程
                self.text_worker_running = True
                self.text_worker_task = asyncio.create_task(self._text_worker())
                
                # 启动音频播放工作线程
                self.audio_worker_running = True
                self.audio_worker_task = asyncio.create_task(self._audio_worker())
                
                logger.info("✅ TTS双队列管理器已启动")
                
        except Exception as e:
            logger.error(f"启动队列管理器失败: {e}")
            raise
    
    async def stop(self):
        """停止队列管理器"""
        try:
            async with self._lock:
                # 停止工作线程
                self.text_worker_running = False
                self.audio_worker_running = False
                
                # 等待工作线程结束
                if self.text_worker_task:
                    self.text_worker_task.cancel()
                    try:
                        await self.text_worker_task
                    except asyncio.CancelledError:
                        pass
                
                if self.audio_worker_task:
                    self.audio_worker_task.cancel()
                    try:
                        await self.audio_worker_task
                    except asyncio.CancelledError:
                        pass
                
                # 清理队列
                await self._clear_queues()
                
                logger.info("✅ TTS双队列管理器已停止")
                
        except Exception as e:
            logger.error(f"停止队列管理器失败: {e}")
    
    async def add_task(self, text: str, service: str = None, speaker: str = None, 
                      priority: TTSTaskPriority = TTSTaskPriority.NORMAL,
                      callback: Callable = None, metadata: Dict[str, Any] = None) -> str:
        """
        添加TTS任务到文本队列
        
        Args:
            text: 要合成的文本
            service: TTS服务名称
            speaker: 音色名称
            priority: 任务优先级
            callback: 完成回调函数
            metadata: 任务元数据
            
        Returns:
            str: 任务ID
        """
        try:
            # 创建任务
            task = TTSTask(
                text=text,
                service=service,
                speaker=speaker,
                priority=priority,
                callback=callback,
                metadata=metadata or {}
            )
            
            # 检查队列是否已满
            if self.text_queue.full():
                logger.warning("文本队列已满，尝试清理过期任务")
                await self._cleanup_expired_tasks()
                
                if self.text_queue.full():
                    raise RuntimeError("文本队列已满，无法添加新任务")
            
            # 添加到活动任务列表
            self.active_tasks[task.task_id] = task
            
            # 根据优先级添加到队列
            if priority == TTSTaskPriority.URGENT:
                # 紧急任务：插入到队列前面
                await self._insert_high_priority_task(task)
            else:
                # 普通任务：添加到队列末尾
                await self.text_queue.put(task)
            
            self.stats["total_tasks"] += 1
            
            logger.info(f"✅ TTS任务已添加: {task.task_id} (优先级: {priority.name})")
            return task.task_id
            
        except Exception as e:
            logger.error(f"添加TTS任务失败: {e}")
            raise
    
    async def cancel_task(self, task_id: str) -> bool:
        """
        取消指定的TTS任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功取消
        """
        try:
            async with self._lock:
                task = self.active_tasks.get(task_id)
                if not task:
                    logger.warning(f"任务不存在: {task_id}")
                    return False
                
                if task.status == TTSTaskStatus.COMPLETED:
                    logger.warning(f"任务已完成，无法取消: {task_id}")
                    return False
                
                # 如果是当前播放的任务，触发中断
                if task_id == self.current_playing_task_id:
                    self.interrupt_event.set()
                
                # 标记任务为已取消
                task.status = TTSTaskStatus.CANCELLED
                task.end_time = time.time()
                
                # 移动到完成列表
                self._move_to_completed(task)
                
                self.stats["cancelled_tasks"] += 1
                
                logger.info(f"✅ TTS任务已取消: {task_id}")
                return True
                
        except Exception as e:
            logger.error(f"取消TTS任务失败: {e}")
            return False
    
    async def interrupt_current(self):
        """中断当前播放的任务"""
        try:
            if self.current_playing_task_id:
                logger.info(f"中断当前播放任务: {self.current_playing_task_id}")
                self.interrupt_event.set()
            else:
                logger.info("没有正在播放的任务")
                
        except Exception as e:
            logger.error(f"中断当前任务失败: {e}")
    
    async def clear_all_queues(self):
        """清空所有队列"""
        try:
            async with self._lock:
                # 中断当前播放
                await self.interrupt_current()
                
                # 清空队列
                await self._clear_queues()
                
                # 取消所有活动任务
                for task_id, task in list(self.active_tasks.items()):
                    if task.status in [TTSTaskStatus.PENDING, TTSTaskStatus.PROCESSING]:
                        task.status = TTSTaskStatus.CANCELLED
                        task.end_time = time.time()
                        self._move_to_completed(task)
                        self.stats["cancelled_tasks"] += 1
                
                logger.info("✅ 所有队列已清空")
                
        except Exception as e:
            logger.error(f"清空队列失败: {e}")
    
    async def get_queue_status(self) -> Dict[str, Any]:
        """
        获取队列状态信息
        
        Returns:
            Dict: 队列状态信息
        """
        try:
            return {
                "text_queue_size": self.text_queue.qsize(),
                "audio_queue_size": self.audio_queue.qsize(),
                "text_queue_max": self.max_text_queue_size,
                "audio_queue_max": self.max_audio_queue_size,
                "active_tasks": len(self.active_tasks),
                "completed_tasks": len(self.completed_tasks),
                "current_playing_task": self.current_playing_task_id,
                "workers_running": {
                    "text_worker": self.text_worker_running,
                    "audio_worker": self.audio_worker_running
                },
                "statistics": self.stats.copy()
            }
            
        except Exception as e:
            logger.error(f"获取队列状态失败: {e}")
            return {"error": str(e)}
    
    async def get_task_info(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[Dict]: 任务信息，不存在返回None
        """
        try:
            # 在活动任务中查找
            task = self.active_tasks.get(task_id)
            if not task:
                # 在完成任务中查找
                for completed_task in self.completed_tasks:
                    if completed_task.task_id == task_id:
                        task = completed_task
                        break
            
            if not task:
                return None
            
            return {
                "task_id": task.task_id,
                "text": task.text,
                "service": task.service,
                "speaker": task.speaker,
                "priority": task.priority.name,
                "status": task.status.value,
                "created_time": task.created_time,
                "start_time": task.start_time,
                "end_time": task.end_time,
                "processing_time": task.get_processing_time(),
                "wait_time": task.get_wait_time(),
                "has_audio_data": task.audio_data is not None,
                "audio_file_path": task.audio_file_path,
                "error_message": task.error_message,
                "metadata": task.metadata
            }
            
        except Exception as e:
            logger.error(f"获取任务信息失败: {e}")
            return None

    async def _text_worker(self):
        """文本处理工作线程"""
        logger.info("文本处理工作线程已启动")

        while self.text_worker_running:
            try:
                # 从文本队列获取任务
                task = await asyncio.wait_for(self.text_queue.get(), timeout=1.0)

                if task.status == TTSTaskStatus.CANCELLED:
                    continue

                # 开始处理任务
                task.status = TTSTaskStatus.PROCESSING
                task.start_time = time.time()

                logger.debug(f"开始处理文本任务: {task.task_id}")

                # 这里需要调用实际的TTS合成服务
                # 由于这是基础设施层，我们只是模拟处理
                # 实际的TTS合成应该由上层服务调用

                # 将任务添加到音频队列等待播放
                if not self.audio_queue.full():
                    await self.audio_queue.put(task)
                    logger.debug(f"文本任务已转移到音频队列: {task.task_id}")
                else:
                    logger.warning(f"音频队列已满，任务失败: {task.task_id}")
                    task.status = TTSTaskStatus.FAILED
                    task.error_message = "音频队列已满"
                    task.end_time = time.time()
                    self._move_to_completed(task)
                    self.stats["failed_tasks"] += 1

            except asyncio.TimeoutError:
                # 超时是正常的，继续循环
                continue
            except Exception as e:
                logger.error(f"文本处理工作线程异常: {e}")
                await asyncio.sleep(0.1)

        logger.info("文本处理工作线程已停止")

    async def _audio_worker(self):
        """音频播放工作线程"""
        logger.info("音频播放工作线程已启动")

        while self.audio_worker_running:
            try:
                # 从音频队列获取任务
                task = await asyncio.wait_for(self.audio_queue.get(), timeout=1.0)

                if task.status == TTSTaskStatus.CANCELLED:
                    continue

                # 设置当前播放任务
                self.current_playing_task_id = task.task_id

                logger.debug(f"开始播放音频任务: {task.task_id}")

                try:
                    # 这里需要调用实际的音频播放服务
                    # 由于这是基础设施层，我们只是模拟播放
                    # 实际的音频播放应该由上层服务调用

                    # 检查中断事件
                    if self.interrupt_event.is_set():
                        logger.info(f"任务被中断: {task.task_id}")
                        task.status = TTSTaskStatus.CANCELLED
                        task.error_message = "任务被中断"
                        self.interrupt_event.clear()
                    else:
                        # 模拟播放完成
                        task.status = TTSTaskStatus.COMPLETED
                        self.stats["completed_tasks"] += 1
                        logger.debug(f"音频任务播放完成: {task.task_id}")

                    task.end_time = time.time()

                    # 调用回调函数
                    if task.callback:
                        try:
                            if asyncio.iscoroutinefunction(task.callback):
                                await task.callback(task)
                            else:
                                task.callback(task)
                        except Exception as callback_error:
                            logger.error(f"任务回调执行失败: {callback_error}")

                    # 移动到完成列表
                    self._move_to_completed(task)

                finally:
                    # 清除当前播放任务
                    self.current_playing_task_id = None

            except asyncio.TimeoutError:
                # 超时是正常的，继续循环
                continue
            except Exception as e:
                logger.error(f"音频播放工作线程异常: {e}")
                await asyncio.sleep(0.1)

        logger.info("音频播放工作线程已停止")

    async def _insert_high_priority_task(self, task: TTSTask):
        """插入高优先级任务到队列前面"""
        try:
            # 创建新的队列，将高优先级任务放在前面
            temp_queue = asyncio.Queue(maxsize=self.max_text_queue_size)

            # 先放入高优先级任务
            await temp_queue.put(task)

            # 将原队列中的任务移到临时队列
            while not self.text_queue.empty():
                old_task = await self.text_queue.get()
                if not temp_queue.full():
                    await temp_queue.put(old_task)
                else:
                    # 如果临时队列满了，丢弃低优先级任务
                    logger.warning(f"队列已满，丢弃低优先级任务: {old_task.task_id}")
                    old_task.status = TTSTaskStatus.CANCELLED
                    old_task.error_message = "队列已满被丢弃"
                    old_task.end_time = time.time()
                    self._move_to_completed(old_task)
                    self.stats["cancelled_tasks"] += 1

            # 替换原队列
            self.text_queue = temp_queue

        except Exception as e:
            logger.error(f"插入高优先级任务失败: {e}")
            # 降级到普通添加
            await self.text_queue.put(task)

    async def _clear_queues(self):
        """清空所有队列"""
        try:
            # 清空文本队列
            while not self.text_queue.empty():
                try:
                    task = self.text_queue.get_nowait()
                    if task.status != TTSTaskStatus.CANCELLED:
                        task.status = TTSTaskStatus.CANCELLED
                        task.error_message = "队列被清空"
                        task.end_time = time.time()
                        self._move_to_completed(task)
                        self.stats["cancelled_tasks"] += 1
                except asyncio.QueueEmpty:
                    break

            # 清空音频队列
            while not self.audio_queue.empty():
                try:
                    task = self.audio_queue.get_nowait()
                    if task.status != TTSTaskStatus.CANCELLED:
                        task.status = TTSTaskStatus.CANCELLED
                        task.error_message = "队列被清空"
                        task.end_time = time.time()
                        self._move_to_completed(task)
                        self.stats["cancelled_tasks"] += 1
                except asyncio.QueueEmpty:
                    break

        except Exception as e:
            logger.error(f"清空队列失败: {e}")

    async def _cleanup_expired_tasks(self):
        """清理过期任务"""
        try:
            current_time = time.time()
            expired_threshold = 300  # 5分钟超时

            expired_task_ids = []

            for task_id, task in self.active_tasks.items():
                if (task.status == TTSTaskStatus.PENDING and
                    current_time - task.created_time > expired_threshold):
                    expired_task_ids.append(task_id)

            for task_id in expired_task_ids:
                await self.cancel_task(task_id)
                logger.info(f"清理过期任务: {task_id}")

        except Exception as e:
            logger.error(f"清理过期任务失败: {e}")

    def _move_to_completed(self, task: TTSTask):
        """将任务移动到完成列表"""
        try:
            # 从活动任务中移除
            if task.task_id in self.active_tasks:
                del self.active_tasks[task.task_id]

            # 添加到完成列表
            self.completed_tasks.append(task)

            # 限制完成任务历史大小
            if len(self.completed_tasks) > self.max_completed_history:
                self.completed_tasks = self.completed_tasks[-self.max_completed_history:]

            # 更新统计信息
            self._update_statistics(task)

        except Exception as e:
            logger.error(f"移动任务到完成列表失败: {e}")

    def _update_statistics(self, task: TTSTask):
        """更新统计信息"""
        try:
            # 更新平均处理时间
            processing_time = task.get_processing_time()
            if processing_time is not None:
                current_avg = self.stats["average_processing_time"]
                completed_count = self.stats["completed_tasks"]

                if completed_count > 0:
                    self.stats["average_processing_time"] = (
                        (current_avg * completed_count + processing_time) / (completed_count + 1)
                    )
                else:
                    self.stats["average_processing_time"] = processing_time

            # 更新平均等待时间
            wait_time = task.get_wait_time()
            current_avg_wait = self.stats["average_wait_time"]
            total_tasks = self.stats["total_tasks"]

            if total_tasks > 0:
                self.stats["average_wait_time"] = (
                    (current_avg_wait * (total_tasks - 1) + wait_time) / total_tasks
                )
            else:
                self.stats["average_wait_time"] = wait_time

        except Exception as e:
            logger.error(f"更新统计信息失败: {e}")


# 全局队列管理器实例
_global_queue_manager = None
_manager_lock = threading.Lock()


def get_global_queue_manager() -> TTSQueueManager:
    """
    获取全局队列管理器实例（单例模式）

    Returns:
        TTSQueueManager: 全局队列管理器
    """
    global _global_queue_manager

    if _global_queue_manager is None:
        with _manager_lock:
            if _global_queue_manager is None:
                _global_queue_manager = TTSQueueManager()
                logger.debug("创建全局TTS队列管理器实例")

    return _global_queue_manager
