"""
AIPet V2 聊天控制器

连接UI组件和V2业务逻辑的桥梁，处理用户交互和AI响应
"""

import asyncio
import logging
from typing import Optional, Dict, Any
from datetime import datetime

from aipet.core.events.base import EventBus
from aipet.application.conversation.orchestrator import ConversationOrchestrator
from aipet.core.entities.message import MessageRole
from aipet.core.services.base import ServiceResult


class ChatController:
    """
    聊天控制器

    职责：
    - 处理UI事件并转换为业务逻辑调用
    - 处理业务逻辑结果并发布UI事件
    - 管理对话状态和流程
    - 处理错误和异常情况
    """

    def __init__(self,
                 conversation_orchestrator: ConversationOrchestrator,
                 event_bus: EventBus,
                 model_manager=None):
        self.orchestrator = conversation_orchestrator
        self.event_bus = event_bus
        self.model_manager = model_manager
        self.logger = logging.getLogger(self.__class__.__name__)

        # 当前状态
        self.current_conversation_id: Optional[str] = None
        self.current_assistant_id: Optional[str] = None
        self.is_processing = False

        # 设置事件处理
        self._setup_event_handlers()

        self.logger.info("ChatController initialized")

    def _setup_event_handlers(self):
        """设置事件处理器"""
        # 用户输入事件
        self.event_bus.subscribe_simple("user_message_sent", self._handle_user_message)
        self.event_bus.subscribe_simple("new_conversation_requested", self._handle_new_conversation)

        # 对话管理事件
        self.event_bus.subscribe_simple("conversation_switch_requested", self._handle_conversation_switch)
        self.event_bus.subscribe_simple("conversation_clear_requested", self._handle_conversation_clear)
        self.event_bus.subscribe_simple("conversation_delete_requested", self._handle_conversation_delete)
        self.event_bus.subscribe_simple("conversation_create_requested", self._handle_new_conversation)
        self.event_bus.subscribe_simple("new_topic_requested", self._handle_new_topic_requested)  # 新增：新建话题事件

        # 系统事件
        self.event_bus.subscribe_simple("window_closing", self._handle_window_closing)

        # 模型管理事件
        self.event_bus.subscribe_simple("model_switch_requested", self._handle_model_switch)
        self.event_bus.subscribe_simple("assistant_switch_requested", self._handle_assistant_switch)

        # 设置管理事件
        self.event_bus.subscribe_simple("setting_changed", self._handle_setting_change)
        self.event_bus.subscribe_simple("export_settings_requested", self._handle_export_settings)
        self.event_bus.subscribe_simple("import_settings_requested", self._handle_import_settings)
        self.event_bus.subscribe_simple("reset_settings_requested", self._handle_reset_settings)

        # 对话框事件
        self.event_bus.subscribe_simple("assistant_creation_requested", self._handle_assistant_creation_dialog)
        self.event_bus.subscribe_simple("assistant_edit_dialog_requested", self._handle_assistant_edit_dialog)

        # 助手数据响应事件
        self.event_bus.subscribe_simple("assistant_data_response", self._handle_assistant_data_response)
        self.event_bus.subscribe_simple("current_assistant_id_response", self._handle_current_assistant_id_response)
        self.event_bus.subscribe_simple("assistant_data_refresh_requested", self._handle_assistant_data_refresh_request)

        # 会话恢复事件
        self.event_bus.subscribe_simple("assistant_restore_requested", self._handle_assistant_restore)
        self.event_bus.subscribe_simple("model_restore_requested", self._handle_model_restore)
        self.event_bus.subscribe_simple("conversation_restore_requested", self._handle_conversation_restore)

    async def _handle_user_message(self, event_data: Dict[str, Any]):
        """处理用户消息"""
        try:
            conversation_id = event_data.get("conversation_id")
            content = event_data.get("content")

            if not conversation_id or not content:
                self._publish_error("无效的消息数据")
                return

            if self.is_processing:
                self._publish_error("正在处理中，请稍候...")
                return

            self.is_processing = True
            self.logger.info(f"Processing user message in conversation {conversation_id}")

            # 发布用户消息添加事件
            user_message_id = self._generate_message_id()
            self.event_bus.publish_simple("message_added", {
                "message_id": user_message_id,
                "role": "user",
                "content": content,
                "conversation_id": conversation_id
            })

            # 发布AI响应开始事件
            ai_message_id = self._generate_message_id()
            # 使用当前助手ID获取最新的助手信息
            assistant_info = await self._get_current_assistant_info(self.current_assistant_id)
            self.event_bus.publish_simple("ai_response_started", {
                "message_id": ai_message_id,
                "conversation_id": conversation_id,
                "assistant_name": assistant_info.get("name"),
                "avatar_path": assistant_info.get("avatar_path")
            })

            # 调用对话编排器处理消息
            result = await self.orchestrator.handle_user_message(conversation_id, content)

            if result.success:
                # 处理AI响应 - 获取流式响应
                ai_result = await self.orchestrator.process_ai_response(conversation_id)
                if ai_result.success:
                    await self._handle_streaming_response(ai_message_id, conversation_id, {"stream": ai_result.data})
                else:
                    await self._handle_ai_error(ai_message_id, ai_result.error)
            else:
                # 处理错误
                await self._handle_ai_error(ai_message_id, result.error)

        except Exception as e:
            self.logger.error(f"Error handling user message: {e}")
            self._publish_error(f"处理消息时发生错误: {str(e)}")
        finally:
            self.is_processing = False

    async def _handle_new_conversation(self, event_data: Dict[str, Any]):
        """处理新对话请求"""
        try:
            initial_message = event_data.get("initial_message", "")

            self.logger.info(f"Creating new conversation with initial message: {initial_message}")

            # 获取当前助手和模型信息
            current_assistant_id = await self._get_current_assistant_id()
            current_model_id = await self._get_current_model_id()

            self.logger.info(f"Creating conversation with assistant: {current_assistant_id}, model: {current_model_id}")

            # 创建新对话，传递助手和模型信息
            result = await self.orchestrator.start_conversation(
                assistant_id=current_assistant_id,
                model_id=current_model_id
            )
            self.logger.info(f"Orchestrator start_conversation result: {result.success}")

            if result.success:
                conversation = result.data
                conversation_id = conversation.id
                self.current_conversation_id = conversation_id

                self.logger.info(f"New conversation created with ID: {conversation_id}")

                # 发布对话创建事件
                self.event_bus.publish_simple("conversation_created", {
                    "conversation_id": conversation_id,
                    "conversation": conversation
                })

                # 如果有初始消息，处理它
                if initial_message:
                    self.logger.info(f"Processing initial message: {initial_message}")
                    await self._handle_user_message({
                        "conversation_id": conversation_id,
                        "content": initial_message
                    })
                    self.logger.info("Initial message processing completed")
            else:
                self.logger.error(f"Failed to create conversation: {result.error}")
                self._publish_error(f"创建对话失败: {result.error}")

        except Exception as e:
            self.logger.error(f"Error creating new conversation: {e}")
            self._publish_error(f"创建对话时发生错误: {str(e)}")

    async def _get_current_assistant_id(self) -> Optional[str]:
        """获取当前助手ID"""
        try:
            # 如果已经缓存了当前助手ID，直接返回
            if self.current_assistant_id:
                return self.current_assistant_id

            # 发布请求获取当前助手ID的事件
            self.event_bus.publish_simple("current_assistant_id_requested", {
                "requester": "chat_controller"
            })

            # 等待一小段时间让事件处理完成
            import asyncio
            await asyncio.sleep(0.1)

            # 返回缓存的助手ID（可能为None）
            return self.current_assistant_id

        except Exception as e:
            self.logger.error(f"Error getting current assistant ID: {e}")
            return None

    async def _get_current_assistant_info(self, assistant_id: str = None) -> Dict[str, Any]:
        """获取当前助手信息"""
        try:
            # 如果没有传入assistant_id，则获取当前助手ID
            if not assistant_id:
                assistant_id = await self._get_current_assistant_id()

            if not assistant_id:
                return {"name": "AI助手", "avatar_path": None}

            # 尝试从配置文件加载助手信息
            import json
            import os

            config_file = "data/assistant_configs.json"
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                assistants = data.get("assistants", {})
                if assistant_id in assistants:
                    assistant_data = assistants[assistant_id]
                    return {
                        "name": assistant_data.get("name", "AI助手"),
                        "avatar_path": assistant_data.get("avatar_path")
                    }

            # 默认返回
            return {"name": "AI助手", "avatar_path": None}

        except Exception as e:
            self.logger.error(f"Error getting current assistant info: {e}")
            return {"name": "AI助手", "avatar_path": None}

    async def _get_current_model_id(self) -> Optional[str]:
        """获取当前模型ID"""
        try:
            if self.model_manager:
                result = await self.model_manager.get_current_model()
                if result.success:
                    return result.data
            return None

        except Exception as e:
            self.logger.error(f"Error getting current model ID: {e}")
            return None

    async def _handle_conversation_switch(self, event_data: Dict[str, Any]):
        """处理对话切换"""
        try:
            conversation_id = event_data.get("conversation_id")

            if not conversation_id:
                self._publish_error("无效的对话ID")
                return

            self.logger.info(f"Switching to conversation: {conversation_id}")

            # 通过编排器切换对话
            result = await self.orchestrator.switch_conversation(conversation_id)

            if result.success:
                # 设置当前对话ID
                self.current_conversation_id = conversation_id

                # 发布对话切换事件
                self.event_bus.publish_simple("conversation_switched", {
                    "conversation_id": conversation_id
                })

                # 加载对话历史
                await self._load_conversation_history(conversation_id)

                self.logger.info(f"Successfully switched to conversation: {conversation_id}")
            else:
                self.logger.error(f"Failed to switch conversation: {result.error}")
                self._publish_error(f"切换对话失败: {result.error}")

        except Exception as e:
            self.logger.error(f"Error switching conversation: {e}")
            self._publish_error(f"切换对话时发生错误: {str(e)}")

    async def _handle_conversation_clear(self, event_data: Dict[str, Any]):
        """处理对话清空"""
        try:
            conversation_id = event_data.get("conversation_id", self.current_conversation_id)

            if not conversation_id:
                self._publish_error("没有活动的对话")
                return

            # 发布对话清空事件（UI层处理）
            self.event_bus.publish_simple("conversation_cleared", {
                "conversation_id": conversation_id
            })

        except Exception as e:
            self.logger.error(f"Error clearing conversation: {e}")
            self._publish_error(f"清空对话时发生错误: {str(e)}")

    async def _handle_conversation_delete(self, event_data: Dict[str, Any]):
        """处理对话删除"""
        try:
            conversation_id = event_data.get("conversation_id")

            if not conversation_id:
                self._publish_error("无效的对话ID")
                return

            self.logger.info(f"Deleting conversation: {conversation_id}")

            # 通过对话编排器删除对话
            result = await self.orchestrator.delete_conversation(conversation_id)

            if result.success:
                # 如果删除的是当前对话，清空当前对话ID
                if self.current_conversation_id == conversation_id:
                    self.current_conversation_id = None

                # 发布对话删除事件
                self.event_bus.publish_simple("conversation_deleted", {
                    "conversation_id": conversation_id
                })

                self.logger.info(f"Conversation deleted successfully: {conversation_id}")
            else:
                self.logger.error(f"Failed to delete conversation: {result.error}")
                self._publish_error(f"删除对话失败: {result.error}")

        except Exception as e:
            self.logger.error(f"Error deleting conversation: {e}")
            self._publish_error(f"删除对话时发生错误: {str(e)}")

    async def _handle_ai_response(self, message_id: str, conversation_id: str, response_data: Dict[str, Any]):
        """处理AI响应"""
        try:
            # 检查是否是流式响应
            if response_data.get("is_streaming", False):
                await self._handle_streaming_response(message_id, conversation_id, response_data)
            else:
                await self._handle_complete_response(message_id, conversation_id, response_data)

        except Exception as e:
            self.logger.error(f"Error handling AI response: {e}")
            await self._handle_ai_error(message_id, str(e))

    async def _handle_streaming_response(self, message_id: str, conversation_id: str, response_data: Dict[str, Any]):
        """处理流式响应"""
        try:
            response_stream = response_data.get("stream")

            if not response_stream:
                await self._handle_ai_error(message_id, "无效的流式响应")
                return

            # 获取当前助手信息
            assistant_info = await self._get_current_assistant_info()

            # 处理流式内容
            async for chunk in response_stream:
                if chunk.get("type") == "content":
                    # 发布AI内容块事件（包含助手信息）
                    self.event_bus.publish_simple("ai_content_chunk", {
                        "message_id": message_id,
                        "content": chunk.get('content', ''),
                        "conversation_id": conversation_id,
                        "assistant_name": assistant_info.get("name"),
                        "avatar_path": assistant_info.get("avatar_path")
                    })
                elif chunk.get("type") == "finished":
                    # 获取完整的消息内容用于TTS
                    complete_content = await self._get_message_content_by_id(message_id)
                    tts_text = self._extract_plain_text_for_tts(complete_content) if complete_content else ""

                    # 发布AI响应完成事件（包含清理后的文本内容用于TTS）
                    self.event_bus.publish_simple("ai_response_finished", {
                        "message_id": message_id,
                        "conversation_id": conversation_id,
                        "text": tts_text  # 发送清理后的纯文本用于TTS自动播放
                    })
                    break
                elif chunk.get("type") == "error":
                    await self._handle_ai_error(message_id, chunk.get("error", "流式响应错误"))
                    break

        except Exception as e:
            self.logger.error(f"Error in streaming response: {e}")
            await self._handle_ai_error(message_id, str(e))

    async def _handle_complete_response(self, message_id: str, conversation_id: str, response_data: Dict[str, Any]):
        """处理完整响应"""
        try:
            content = response_data.get("content", "")

            # 获取当前助手信息（使用当前助手ID确保信息准确）
            assistant_info = await self._get_current_assistant_info(self.current_assistant_id)

            # 发布消息添加事件（包含助手信息）
            self.event_bus.publish_simple("message_added", {
                "message_id": message_id,
                "role": "assistant",
                "content": content,
                "conversation_id": conversation_id,
                "assistant_name": assistant_info.get("name"),
                "avatar_path": assistant_info.get("avatar_path")
            })

            # 为TTS清理HTML标签，提取纯文本
            tts_text = self._extract_plain_text_for_tts(content)

            # 发布响应完成事件（包含清理后的文本内容用于TTS）
            self.event_bus.publish_simple("ai_response_finished", {
                "message_id": message_id,
                "conversation_id": conversation_id,
                "text": tts_text  # 发送清理后的纯文本用于TTS自动播放
            })

        except Exception as e:
            self.logger.error(f"Error in complete response: {e}")
            await self._handle_ai_error(message_id, str(e))

    def _extract_plain_text_for_tts(self, html_content: str) -> str:
        """
        从HTML内容中提取纯文本用于TTS播放

        Args:
            html_content: 包含HTML标签的内容

        Returns:
            清理后的纯文本内容
        """
        if not html_content:
            return ""

        import re

        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', html_content)

        # 解码HTML实体
        text = (text.replace('&amp;', '&')
                   .replace('&lt;', '<')
                   .replace('&gt;', '>')
                   .replace('&quot;', '"')
                   .replace('&#x27;', "'")
                   .replace('&nbsp;', ' '))

        # 规范化空白字符
        text = re.sub(r'\s+', ' ', text).strip()

        return text

    async def _get_message_content_by_id(self, message_id: str) -> str:
        """
        根据消息ID获取完整的消息内容

        Args:
            message_id: 消息ID

        Returns:
            消息的完整内容，如果未找到则返回空字符串
        """
        try:
            # 从消息存储中获取消息内容
            if hasattr(self, 'message_storage') and self.message_storage:
                messages = await self.message_storage.get_messages(self.current_conversation_id)
                for message in messages:
                    if message.get("id") == message_id:
                        return message.get("content", "")

            # 如果消息存储不可用，尝试从事件总线获取
            # 这里可以添加其他获取消息内容的方法
            self.logger.warning(f"无法获取消息内容: message_id={message_id}")
            return ""

        except Exception as e:
            self.logger.error(f"获取消息内容时出错: {e}")
            return ""

    async def _handle_ai_error(self, message_id: str, error_message: str):
        """处理AI错误"""
        # 获取当前助手信息
        assistant_info = await self._get_current_assistant_info()

        # 发布错误消息
        self.event_bus.publish_simple("message_added", {
            "message_id": message_id,
            "role": "assistant",
            "content": f"抱歉，发生了错误: {error_message}",
            "conversation_id": self.current_conversation_id,
            "assistant_name": assistant_info.get("name"),
            "avatar_path": assistant_info.get("avatar_path"),
            "is_error": True
        })

        # 发布响应完成事件
        self.event_bus.publish_simple("ai_response_finished", {
            "message_id": message_id,
            "conversation_id": self.current_conversation_id,
            "has_error": True
        })

        # 发布错误事件
        self._publish_error(error_message)

    async def _load_conversation_history(self, conversation_id: str):
        """加载对话历史"""
        try:
            self.logger.info(f"Loading conversation history for: {conversation_id}")

            # 发布历史加载开始事件
            self.event_bus.publish_simple("conversation_history_loading", {
                "conversation_id": conversation_id
            })

            # 通过编排器获取对话
            result = await self.orchestrator.switch_conversation(conversation_id)

            if result.success and result.data:
                conversation = result.data

                # 清空当前显示的消息
                self.event_bus.publish_simple("conversation_cleared", {
                    "conversation_id": conversation_id
                })

                # 逐个发布历史消息
                for message in conversation.messages:
                    # 🔧 修复：过滤掉工具响应消息，避免在聊天界面显示JSON格式的工具结果
                    role_str = message.role.value if hasattr(message.role, 'value') else message.role
                    if role_str.lower() == "tool":
                        self.logger.debug(f"Skipping tool response message in history: {message.id}")
                        continue  # 跳过工具响应消息

                    message_data = {
                        "message_id": message.id,
                        "role": role_str,
                        "content": message.content,
                        "conversation_id": conversation_id,
                        "timestamp": message.timestamp.isoformat() if hasattr(message, 'timestamp') else None,
                        "is_history": True  # 标记为历史消息
                    }

                    # 如果是助手消息，添加助手信息
                    if role_str.lower() == "assistant":
                        assistant_info = await self._get_current_assistant_info()
                        message_data["assistant_name"] = assistant_info.get("name")
                        message_data["avatar_path"] = assistant_info.get("avatar_path")

                    self.event_bus.publish_simple("message_added", message_data)

                # 发布历史加载完成事件
                self.event_bus.publish_simple("conversation_history_loaded", {
                    "conversation_id": conversation_id,
                    "message_count": len(conversation.messages)
                })

                self.logger.info(f"Loaded {len(conversation.messages)} messages for conversation: {conversation_id}")
            else:
                self.logger.warning(f"Failed to load conversation: {result.error if result else 'No result'}")
                self._publish_error(f"无法加载对话历史: {result.error if result else '未知错误'}")

        except Exception as e:
            self.logger.error(f"Error loading conversation history: {e}")
            self._publish_error(f"加载对话历史时发生错误: {str(e)}")

    async def _handle_model_switch(self, event_data: Dict[str, Any]):
        """处理模型切换"""
        try:
            model_id = event_data.get("model_id")

            if not model_id:
                self._publish_error("无效的模型ID")
                return

            if not self.model_manager:
                self._publish_error("模型管理器未初始化")
                return

            self.logger.info(f"Switching to model: {model_id}")

            # 切换模型
            result = await self.model_manager.switch_model(model_id)

            if result.success:
                self.logger.info(f"Successfully switched to model: {model_id}")

                # 获取模型名称
                model_name = model_id
                try:
                    models_result = await self.model_manager.get_available_models()
                    if models_result.success:
                        models = models_result.data
                        if model_id in models:
                            model_name = models[model_id].get("name", model_id)
                except Exception as e:
                    self.logger.warning(f"Failed to get model name: {e}")

                # 发布模型切换成功事件
                self.event_bus.publish_simple("model_switched", {
                    "model_id": model_id,
                    "model_name": model_name,
                    "timestamp": datetime.now().isoformat()
                })

                # 发布状态更新
                self.event_bus.publish_simple("connection_status_changed", {
                    "status": "connected",
                    "model": model_id,
                    "message": f"已切换到 {model_name}"
                })
            else:
                self.logger.error(f"Failed to switch model: {result.error}")
                self._publish_error(f"模型切换失败: {result.error}")

        except Exception as e:
            self.logger.error(f"Error switching model: {e}")
            self._publish_error(f"模型切换时发生错误: {str(e)}")

    async def _handle_assistant_switch(self, event_data: Dict[str, Any]):
        """处理助手切换 - 智能话题切换版本"""
        self.logger.info(f"🔥 [ChatController] Received 'assistant_switch_requested' event with data: {event_data}")
        try:
            assistant_id = event_data.get("assistant_id")

            if not assistant_id:
                self._publish_error("无效的助手ID")
                return

            self.logger.info(f"Switching to assistant: {assistant_id}")

            # 1. 查找该助手的最近对话
            latest_conversation_result = await self.orchestrator.conversation_manager.get_latest_conversation_for_assistant(assistant_id)

            if latest_conversation_result.success and latest_conversation_result.data:
                # 找到了该助手的最近对话，切换到该对话
                latest_conversation = latest_conversation_result.data
                self.logger.info(f"Found latest conversation for assistant {assistant_id}: {latest_conversation.id}")

                # 切换到该对话
                await self._switch_to_conversation(latest_conversation.id)

            else:
                # 没有找到该助手的对话，创建新对话
                self.logger.info(f"No existing conversation found for assistant {assistant_id}, creating new one")

                # 创建新对话并关联助手
                result = await self.orchestrator.conversation_manager.create_conversation(
                    title=f"与助手的对话",
                    assistant_id=assistant_id
                )

                if result.success:
                    new_conversation = result.data
                    self.logger.info(f"Created new conversation for assistant {assistant_id}: {new_conversation.id}")

                    # 切换到新对话
                    await self._switch_to_conversation(new_conversation.id)

                    # 发布对话创建事件，确保侧边栏实时更新
                    self.event_bus.publish_simple("conversation_created", {
                        "conversation_id": new_conversation.id,
                        "conversation": new_conversation
                    })

                    self.logger.info(f"Published conversation_created event for assistant switch: {new_conversation.id}")
                else:
                    self.logger.error(f"Failed to create conversation for assistant {assistant_id}: {result.error}")
                    self._publish_error(f"创建新对话失败: {result.error}")
                    return

            # 2. 更新当前助手ID
            self.current_assistant_id = assistant_id

            # 3. 获取助手数据并更新对话编排器
            await self._update_orchestrator_assistant(assistant_id)

            # 4. 获取助手信息并缓存（确保头像信息及时更新）
            assistant_info = await self._get_current_assistant_info(assistant_id)
            self.logger.info(f"Assistant info for {assistant_id}: {assistant_info}")

            # 5. 发布助手切换事件（包含助手信息）
            self.event_bus.publish_simple("assistant_switched", {
                "assistant_id": assistant_id,
                "assistant_name": assistant_info.get("name", "AI助手"),
                "avatar_path": assistant_info.get("avatar_path"),
                "timestamp": datetime.now().isoformat()
            })

            # 6. 发布状态更新
            self.event_bus.publish_simple("connection_status_changed", {
                "status": "connected",
                "assistant": assistant_id,
                "message": f"已切换到助手: {assistant_info.get('name', assistant_id)}"
            })

        except Exception as e:
            self.logger.error(f"Error switching assistant: {e}")
            self._publish_error(f"助手切换时发生错误: {str(e)}")

    async def _switch_to_conversation(self, conversation_id: str):
        """切换到指定对话的辅助方法"""
        try:
            # 通过编排器切换对话
            result = await self.orchestrator.switch_conversation(conversation_id)

            if result.success:
                # 设置当前对话ID
                self.current_conversation_id = conversation_id

                # 发布对话切换事件
                self.event_bus.publish_simple("conversation_switched", {
                    "conversation_id": conversation_id
                })

                # 加载对话历史
                await self._load_conversation_history(conversation_id)

                # 设置侧边栏中的活跃话题
                self.event_bus.publish_simple("topic_activated", {
                    "topic_id": conversation_id
                })

                self.logger.info(f"Successfully switched to conversation: {conversation_id}")
            else:
                self.logger.error(f"Failed to switch conversation: {result.error}")
                self._publish_error(f"切换对话失败: {result.error}")

        except Exception as e:
            self.logger.error(f"Error switching to conversation {conversation_id}: {e}")
            self._publish_error(f"切换对话时发生错误: {str(e)}")

    async def _handle_new_topic_requested(self, event_data: Dict[str, Any]):
        """处理新建话题请求"""
        try:
            self.logger.info("New topic requested")

            # 获取当前助手ID
            current_assistant_id = self.current_assistant_id or await self._get_current_assistant_id()

            # 创建新对话并关联当前助手
            result = await self.orchestrator.conversation_manager.create_conversation(
                title=f"新话题 {datetime.now().strftime('%m-%d %H:%M')}",
                assistant_id=current_assistant_id
            )

            if result.success:
                new_conversation = result.data
                self.logger.info(f"Created new topic: {new_conversation.id}")

                # 切换到新话题
                await self._switch_to_conversation(new_conversation.id)

                # 发布对话创建事件（保证兼容性）
                self.event_bus.publish_simple("conversation_created", {
                    "conversation_id": new_conversation.id,
                    "conversation": new_conversation
                })

                # 发布新话题创建事件（QML专用）
                self.event_bus.publish_simple("new_topic_created", {
                    "topic_id": new_conversation.id,
                    "assistant_id": current_assistant_id,
                    "timestamp": datetime.now().isoformat()
                })

                self.logger.info(f"Successfully created and switched to new topic: {new_conversation.id}")
            else:
                self.logger.error(f"Failed to create new topic: {result.error}")
                self._publish_error(f"创建新话题失败: {result.error}")

        except Exception as e:
            self.logger.error(f"Error creating new topic: {e}")
            self._publish_error(f"创建新话题时发生错误: {str(e)}")

    async def _handle_setting_change(self, event_data: Dict[str, Any]):
        """处理设置变更"""
        try:
            key = event_data.get("key")
            value = event_data.get("value")

            if not key:
                return

            self.logger.info(f"Setting changed: {key} = {value}")

            # 根据设置类型进行相应处理
            if key == "window_opacity":
                self.event_bus.publish_simple("window_opacity_changed", {
                    "opacity": value / 100.0
                })
            elif key == "font_size":
                self.event_bus.publish_simple("font_size_changed", {
                    "size": value
                })
            elif key == "theme":
                self.event_bus.publish_simple("theme_changed", {
                    "theme": value
                })

        except Exception as e:
            self.logger.error(f"Error handling setting change: {e}")

    async def _handle_export_settings(self, event_data: Dict[str, Any]):
        """处理导出设置"""
        try:
            self.logger.info("Exporting settings")

            # 发布导出设置事件
            self.event_bus.publish_simple("settings_export_dialog_requested", {
                "timestamp": datetime.now().isoformat()
            })

        except Exception as e:
            self.logger.error(f"Error exporting settings: {e}")
            self._publish_error(f"导出设置时发生错误: {str(e)}")

    async def _handle_import_settings(self, event_data: Dict[str, Any]):
        """处理导入设置"""
        try:
            self.logger.info("Importing settings")

            # 发布导入设置事件
            self.event_bus.publish_simple("settings_import_dialog_requested", {
                "timestamp": datetime.now().isoformat()
            })

        except Exception as e:
            self.logger.error(f"Error importing settings: {e}")
            self._publish_error(f"导入设置时发生错误: {str(e)}")

    async def _handle_reset_settings(self, event_data: Dict[str, Any]):
        """处理重置设置"""
        try:
            self.logger.info("Resetting settings")

            # 发布重置设置事件
            self.event_bus.publish_simple("settings_reset_confirmed", {
                "timestamp": datetime.now().isoformat()
            })

        except Exception as e:
            self.logger.error(f"Error resetting settings: {e}")
            self._publish_error(f"重置设置时发生错误: {str(e)}")

    async def _handle_window_closing(self, event_data: Dict[str, Any]):
        """处理窗口关闭"""
        try:
            self.logger.info("Window closing, cleaning up...")

            # 清理资源
            await self.orchestrator.shutdown()

            if self.model_manager:
                await self.model_manager.shutdown()

        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")

    async def _update_orchestrator_assistant(self, assistant_id: str):
        """更新对话编排器的当前助手数据"""
        try:
            # 🔧 同时为chat_controller和orchestrator请求助手数据
            self.event_bus.publish_simple("assistant_data_requested", {
                "assistant_id": assistant_id,
                "requester": "chat_controller"
            })

            # 🔧 额外为orchestrator请求助手数据，确保它能直接接收
            self.event_bus.publish_simple("assistant_data_requested", {
                "assistant_id": assistant_id,
                "requester": "conversation_orchestrator"
            })

            # 等待助手数据响应，而不是立即使用默认数据
            import asyncio
            await asyncio.sleep(0.1)  # 给事件处理足够时间

            self.logger.info(f"Requested assistant data for chat_controller and orchestrator: {assistant_id}")

        except Exception as e:
            self.logger.error(f"Error updating orchestrator assistant: {e}")

    async def _handle_assistant_data_response(self, event_data: Dict[str, Any]):
        """处理助手数据响应事件"""
        try:
            requester = event_data.get("requester")

            # 只处理发给聊天控制器的响应
            if requester != "chat_controller":
                return

            assistant_id = event_data.get("assistant_id")
            assistant_data = event_data.get("assistant_data", {})

            if assistant_data:
                # 更新对话编排器
                self.orchestrator.set_current_assistant(assistant_data)
                self.logger.info(f"Updated orchestrator with real assistant data: {assistant_data.get('name', 'Unknown')}")

        except Exception as e:
            self.logger.error(f"Error handling assistant data response: {e}")

    async def _handle_assistant_data_refresh_request(self, event_data: Dict[str, Any]):
        """处理助手数据刷新请求"""
        try:
            requester = event_data.get("requester")

            # 只处理来自对话编排器的请求
            if requester == "conversation_orchestrator":
                self.logger.info("🔄 Received assistant data refresh request from orchestrator")

                # 获取当前助手ID
                if hasattr(self, 'current_assistant_id') and self.current_assistant_id:
                    # 立即请求最新的助手数据
                    self.event_bus.publish_simple("assistant_data_requested", {
                        "assistant_id": self.current_assistant_id,
                        "requester": "chat_controller"
                    })
                    self.logger.info(f"🔄 Requested fresh assistant data for: {self.current_assistant_id}")
                else:
                    self.logger.warning("⚠️ No current assistant ID available for refresh")

        except Exception as e:
            self.logger.error(f"Error handling assistant data refresh request: {e}")

    async def _handle_current_assistant_id_response(self, event_data: Dict[str, Any]):
        """处理当前助手ID响应"""
        try:
            requester = event_data.get("requester")
            assistant_id = event_data.get("assistant_id")

            # 只处理发给chat_controller的响应
            if requester == "chat_controller":
                self.current_assistant_id = assistant_id
                self.logger.info(f"Received current assistant ID: {assistant_id}")

        except Exception as e:
            self.logger.error(f"Error handling current assistant ID response: {e}")

    async def _handle_assistant_restore(self, event_data: Dict[str, Any]):
        """处理助手恢复请求"""
        try:
            assistant_id = event_data.get("assistant_id")
            if assistant_id:
                self.logger.info(f"Restoring assistant: {assistant_id}")
                await self._handle_assistant_switch({"assistant_id": assistant_id})

        except Exception as e:
            self.logger.error(f"Error restoring assistant: {e}")

    async def _handle_model_restore(self, event_data: Dict[str, Any]):
        """处理模型恢复请求"""
        try:
            model_id = event_data.get("model_id")
            if model_id:
                self.logger.info(f"Restoring model: {model_id}")
                await self._handle_model_switch({"model_id": model_id})

        except Exception as e:
            self.logger.error(f"Error restoring model: {e}")

    async def _handle_conversation_restore(self, event_data: Dict[str, Any]):
        """处理对话恢复请求"""
        try:
            conversation_id = event_data.get("conversation_id")
            if conversation_id:
                self.logger.info(f"Restoring conversation: {conversation_id}")
                await self._handle_conversation_switch({"conversation_id": conversation_id})

        except Exception as e:
            self.logger.error(f"Error restoring conversation: {e}")

    async def _set_default_assistant(self):
        """设置默认助手数据"""
        try:
            # 不再使用硬编码的"default"，而是使用当前活跃的助手
            if hasattr(self, 'current_assistant_id') and self.current_assistant_id:
                await self._update_orchestrator_assistant(self.current_assistant_id)
                self.logger.info(f"Set assistant data for current assistant: {self.current_assistant_id}")
            else:
                # 如果没有当前助手ID，使用临时默认数据，但会在后续被真实数据覆盖
                default_assistant_data = {
                    "name": "AI助手",
                    "system_prompt": "你是一个友善、专业的AI助手。请根据用户的需求提供准确、有用的回答。",
                    "max_tokens": 4096,
                    "temperature": 0.7
                }
                self.orchestrator.set_current_assistant(default_assistant_data)
                self.logger.info("Set temporary default assistant data")

        except Exception as e:
            self.logger.error(f"Error setting default assistant: {e}")

    async def _initialize_current_assistant_id(self):
        """初始化当前助手ID"""
        try:
            # 请求当前助手ID
            self.event_bus.publish_simple("current_assistant_id_requested", {
                "requester": "chat_controller"
            })

            # 等待响应
            import asyncio
            await asyncio.sleep(0.2)  # 给足够时间处理事件

            self.logger.info(f"Initialized current assistant ID: {self.current_assistant_id}")

        except Exception as e:
            self.logger.error(f"Error initializing current assistant ID: {e}")

    async def _restore_session_state(self):
        """恢复上次会话状态"""
        try:
            self.logger.info("Restoring session state...")

            # 请求获取会话状态
            self.event_bus.publish_simple("session_state_requested", {
                "requester": "chat_controller"
            })

            # 等待响应
            import asyncio
            await asyncio.sleep(0.2)

            self.logger.info("Session state restoration completed")

        except Exception as e:
            self.logger.error(f"Error restoring session state: {e}")

    async def _handle_assistant_creation_dialog(self, event_data: Dict[str, Any]):
        """处理助手创建对话框请求"""
        try:
            self.logger.info("Assistant creation dialog requested")
            # 这个事件主要用于日志记录，实际对话框由AssistantManager处理

        except Exception as e:
            self.logger.error(f"Error handling assistant creation dialog: {e}")

    async def _handle_assistant_edit_dialog(self, event_data: Dict[str, Any]):
        """处理助手编辑对话框请求"""
        try:
            assistant_id = event_data.get("assistant_id")
            self.logger.info(f"Assistant edit dialog requested for: {assistant_id}")
            # 这个事件主要用于日志记录，实际对话框由AssistantManager处理

        except Exception as e:
            self.logger.error(f"Error handling assistant edit dialog: {e}")

    async def _publish_error(self, error_message: str):
        """发布错误事件"""
        # 发布错误事件
        self.event_bus.publish_simple("error_occurred", {
            "error_message": error_message,
            "timestamp": datetime.now().isoformat()
        })

    def _generate_message_id(self) -> str:
        """生成消息ID"""
        import uuid
        return str(uuid.uuid4())

    # 公共方法
    async def initialize(self) -> ServiceResult[None]:
        """初始化控制器"""
        try:
            # 初始化对话编排器
            result = await self.orchestrator.initialize()

            if result.success:
                self.logger.info("ChatController initialized successfully")

                # 先获取当前助手ID
                await self._initialize_current_assistant_id()

                # 注意：助手数据设置移到GUI完全初始化后执行
                # 因为此时AssistantManager还没有被创建
                # 实际的助手数据设置会在_load_historical_data_to_ui中完成

                # 恢复上次会话状态
                await self._restore_session_state()

                # 加载对话列表到UI
                await self._load_conversations()

                # 注意：历史数据加载移到GUI主程序中，在UI完全准备好后执行

                return ServiceResult.success_result()
            else:
                self.logger.error(f"Failed to initialize orchestrator: {result.error}")
                return ServiceResult.error_result(f"初始化失败: {result.error}")

        except Exception as e:
            self.logger.error(f"Error initializing ChatController: {e}")
            return ServiceResult.error_result(f"初始化时发生错误: {str(e)}")

    async def _load_historical_data_to_ui(self):
        """加载历史数据到UI"""
        try:
            self.logger.info("Loading historical data to UI...")

            # 1. 先加载助手配置到侧边栏（确保助手状态先建立）
            await self._load_assistants_to_sidebar()

            # 1.5. 现在AssistantManager已经创建，设置对话编排器的助手数据
            await self._set_default_assistant()

            # 2. 再加载历史对话到侧边栏（此时助手状态已确定，话题过滤会正确应用）
            await self._load_conversations_to_sidebar()

            # 3. 如果有活跃对话，加载最近的一个
            await self._load_most_recent_conversation()

            # 4. 确保话题过滤与当前助手同步
            await self._sync_topic_filter_with_current_assistant()

            self.logger.info("Historical data loaded to UI successfully")

        except Exception as e:
            self.logger.error(f"Error loading historical data to UI: {e}")

    async def _sync_topic_filter_with_current_assistant(self):
        """同步话题过滤与当前助手"""
        try:
            # 获取当前活跃的助手ID
            if hasattr(self, 'current_assistant_id') and self.current_assistant_id:
                # 发布助手切换事件，确保话题栏应用正确的过滤
                self.event_bus.publish_simple("assistant_switched", {
                    "assistant_id": self.current_assistant_id,
                    "timestamp": datetime.now().isoformat(),
                    "source": "startup_sync"
                })
                self.logger.info(f"Synced topic filter with current assistant: {self.current_assistant_id}")
            else:
                self.logger.warning("No current assistant ID found for topic filter sync")

        except Exception as e:
            self.logger.error(f"Error syncing topic filter with current assistant: {e}")

    async def _load_conversations_to_sidebar(self):
        """加载对话历史到侧边栏"""
        try:
            # 获取所有活跃对话
            result = await self.orchestrator.conversation_manager.list_active_conversations()

            if result.success and result.data:
                conversations = result.data
                self.logger.info(f"Loading {len(conversations)} conversations to sidebar")

                # 为每个对话添加助手ID信息
                enhanced_conversations = []
                for conv in conversations:
                    # 从对话文件中读取助手ID
                    assistant_id = self._get_conversation_assistant_id(conv["id"])

                    enhanced_conv = conv.copy()
                    enhanced_conv["assistant_id"] = assistant_id
                    enhanced_conv["last_message"] = self._get_last_message_preview(conv)
                    enhanced_conversations.append(enhanced_conv)

                # 发布事件，通知侧边栏加载对话数据（包含助手ID信息）
                self.event_bus.publish_simple("conversations_loaded", {
                    "conversations": enhanced_conversations,
                    "timestamp": datetime.now().isoformat()
                })

                # 移除重复的topic_added事件发布，避免重复添加话题

                self.logger.info(f"Successfully loaded {len(enhanced_conversations)} conversations to sidebar")
            else:
                self.logger.info("No conversations to load to sidebar")

        except Exception as e:
            self.logger.error(f"Error loading conversations to sidebar: {e}")

    async def _load_assistants_to_sidebar(self):
        """加载助手配置到侧边栏"""
        try:
            # Phase 3: 助手与模型分离 - 不再将模型作为助手加载
            # 现在只加载真正的助手配置
            import json
            import os

            assistant_config_file = "data/assistant_configs.json"
            if os.path.exists(assistant_config_file):
                with open(assistant_config_file, 'r', encoding='utf-8') as f:
                    assistant_data = json.load(f)

                assistants = assistant_data.get("assistants", {})
                active_assistant_id = assistant_data.get("active_assistant_id", "")

                self.logger.info(f"Loading {len(assistants)} assistants to sidebar")

                # 设置当前助手ID（重要：确保ChatController知道当前活跃的助手）
                if active_assistant_id and active_assistant_id in assistants:
                    self.current_assistant_id = active_assistant_id
                    self.logger.info(f"Set current assistant ID to: {active_assistant_id}")

                # 加载真正的助手到侧边栏
                for assistant_id, assistant_info in assistants.items():
                    assistant_event_data = {
                        "assistant_id": assistant_id,
                        "name": assistant_info.get("name", assistant_id),
                        "description": assistant_info.get("description", ""),
                        "is_active": assistant_id == active_assistant_id
                    }
                    # 禁用冗余日志 - 太多了没用
                    # self.logger.info(f"Publishing assistant_added event for: {assistant_id} - {assistant_info.get('name', assistant_id)}")
                    self.event_bus.publish_simple("assistant_added", assistant_event_data)

                self.logger.info(f"Successfully loaded {len(assistants)} assistants to sidebar")
            else:
                self.logger.info("No assistant config file found, creating default assistants")
                # 创建默认助手
                await self._create_default_assistants()

        except Exception as e:
            self.logger.error(f"Error loading assistants to sidebar: {e}")

    async def _create_default_assistants(self):
        """创建默认助手"""
        try:
            # Phase 3: 助手与模型分离 - 助手不再包含model字段
            default_assistants = [
                {
                    "assistant_id": "default-assistant",
                    "name": "默认助手",
                    "description": "系统默认的AI助手",
                    "is_active": True
                },
                {
                    "assistant_id": "creative-assistant",
                    "name": "创意助手",
                    "description": "专注于创意和写作的AI助手",
                    "is_active": False
                }
            ]

            # 设置第一个助手为当前助手
            if default_assistants:
                first_assistant = default_assistants[0]
                self.current_assistant_id = first_assistant["assistant_id"]
                self.logger.info(f"Set current assistant ID to default: {self.current_assistant_id}")

            for assistant in default_assistants:
                self.event_bus.publish_simple("assistant_added", assistant)

            self.logger.info("Created default assistants")

        except Exception as e:
            self.logger.error(f"Error creating default assistants: {e}")

    async def _load_most_recent_conversation(self):
        """加载最近的对话"""
        try:
            # 获取所有活跃对话
            result = await self.orchestrator.conversation_manager.list_active_conversations()

            if result.success and result.data:
                conversations = result.data
                if conversations:
                    # 选择最近更新的对话
                    most_recent = conversations[0]  # 已经按更新时间排序
                    conversation_id = most_recent["id"]

                    self.logger.info(f"Loading most recent conversation: {conversation_id}")

                    # 设置为当前对话
                    await self.orchestrator.conversation_manager.set_current_conversation(conversation_id)
                    self.current_conversation_id = conversation_id

                    # 加载对话历史
                    await self._load_conversation_history(conversation_id)

                    # 设置侧边栏中的活跃话题
                    self.event_bus.publish_simple("topic_activated", {
                        "topic_id": conversation_id
                    })

                    self.logger.info(f"Successfully loaded most recent conversation: {conversation_id}")

        except Exception as e:
            self.logger.error(f"Error loading most recent conversation: {e}")

    def _get_last_message_preview(self, conversation_data: Dict[str, Any]) -> str:
        """获取对话的最后一条消息预览"""
        try:
            conversation_id = conversation_data["id"]

            # 方法1：先尝试从内存中获取
            conversations = self.orchestrator.conversation_manager._active_conversations
            if conversation_id in conversations:
                conversation = conversations[conversation_id]
                if hasattr(conversation, 'messages') and conversation.messages:
                    last_message = conversation.messages[-1]
                    if hasattr(last_message, 'content') and last_message.content:
                        preview = last_message.content[:50]
                        return preview + "..." if len(last_message.content) > 50 else preview

            # 方法2：如果内存中没有，直接从文件读取
            import json
            import os

            conversation_file = f"data/conversations/{conversation_id}.json"
            if os.path.exists(conversation_file):
                with open(conversation_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                messages = data.get("messages", [])
                if messages:
                    # 获取最后一条消息
                    last_message = messages[-1]
                    content = last_message.get("content", "")
                    if content:
                        # 截取前50个字符作为预览
                        preview = content[:50]
                        return preview + "..." if len(content) > 50 else preview
                    else:
                        return "无内容"
                else:
                    return "暂无消息"

            # 方法3：如果文件也不存在，尝试从conversation_data中获取
            if "last_message" in conversation_data:
                last_msg = conversation_data["last_message"]
                if last_msg:
                    preview = last_msg[:50]
                    return preview + "..." if len(last_msg) > 50 else preview

            return "暂无消息"

        except Exception as e:
            self.logger.error(f"Error getting last message preview for {conversation_data.get('id', 'unknown')}: {e}")
            return "暂无消息"

    def _get_conversation_assistant_id(self, conversation_id: str) -> Optional[str]:
        """获取对话的助手ID"""
        try:
            import json
            import os

            conversation_file = f"data/conversations/{conversation_id}.json"
            if os.path.exists(conversation_file):
                with open(conversation_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                metadata = data.get("metadata", {})
                assistant_id = metadata.get("assistant_id")

                return assistant_id

            return None

        except Exception as e:
            self.logger.error(f"Error getting assistant ID for conversation {conversation_id}: {e}")
            return None

    async def shutdown(self):
        """关闭控制器"""
        try:
            self.logger.info("Shutting down ChatController")
            await self.orchestrator.shutdown()
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")

    def get_current_conversation_id(self) -> Optional[str]:
        """获取当前对话ID"""
        return self.current_conversation_id

    async def _load_conversations(self):
        """加载对话列表到UI"""
        try:
            self.logger.info("Loading conversations to UI...")

            # 通过编排器获取对话列表
            result = await self.orchestrator.list_conversations()

            if result.success:
                conversations = result.data or []

                # 转换为UI需要的格式
                ui_conversations = []
                for conv in conversations:
                    ui_conv = {
                        "id": conv.get("id"),
                        "title": conv.get("title", "新对话"),
                        "lastMessage": conv.get("last_message", "暂无消息"),
                        "lastMessageTime": conv.get("updated_at"),
                        "messageCount": conv.get("message_count", 0),
                        "hasUnread": False,  # 可以根据实际需求设置
                        "assistantId": conv.get("assistant_id"),
                        "isActive": conv.get("id") == self.current_conversation_id
                    }
                    ui_conversations.append(ui_conv)

                # 发布对话列表加载事件
                self.event_bus.publish_simple("conversations_loaded", {
                    "conversations": ui_conversations
                })

                self.logger.info(f"Loaded {len(ui_conversations)} conversations to UI")
            else:
                self.logger.error(f"Failed to load conversations: {result.error}")

        except Exception as e:
            self.logger.error(f"Error loading conversations: {e}")