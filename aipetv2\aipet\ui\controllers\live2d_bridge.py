"""
Live2D WebEngine桥接类
负责QML与Live2D Web组件之间的通信
"""

import logging
import json
import os
from pathlib import Path
from typing import Optional, Dict, Any, List
from PySide6.QtCore import QObject, Signal, Slot, QTimer
from PySide6.QtWebEngineCore import QWebEngineProfile


class Live2DBridge(QObject):
    """Live2D WebEngine桥接类"""
    
    # ===== 信号定义 =====
    
    # Live2D状态信号
    ready = Signal()  # Live2D准备就绪
    modelLoaded = Signal(str)  # 模型加载完成
    modelLoadFailed = Signal(str, str)  # 模型加载失败 (path, error)
    modelSwitched = Signal(str)  # 模型切换完成 (modelName)

    # 表情和动作信号
    expressionChanged = Signal(str, float)  # 表情变化 (name, intensity)
    motionStarted = Signal(str)  # 动作开始 (name)
    motionFinished = Signal(str)  # 动作完成 (name)

    # 交互信号
    modelClicked = Signal()  # 模型被点击
    modelHovered = Signal(bool)  # 模型悬停状态 (isHovered)

    # 模型管理信号
    availableModelsChanged = Signal(list)  # 可用模型列表变化
    
    # 系统信号
    fpsChanged = Signal(int)  # FPS变化
    errorOccurred = Signal(str)  # 错误发生
    
    # ===== Qt到Web的信号 =====
    
    # 模型控制信号
    modelLoadRequested = Signal(str)  # 请求加载模型
    expressionChangeRequested = Signal(str, float)  # 请求表情变化
    motionPlayRequested = Signal(str, int)  # 请求播放动作 (name, priority)
    
    # 系统控制信号
    debugModeChanged = Signal(bool)  # 调试模式变化
    scaleChanged = Signal(float)  # 缩放变化
    positionChanged = Signal(float, float)  # 位置变化 (x, y)

    # 口型同步信号
    lipSyncStartRequested = Signal(list)  # 请求开始口型同步 (mouthData)
    lipSyncStopRequested = Signal()  # 请求停止口型同步
    mouthOpeningChanged = Signal(float)  # 口型开合度变化
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 状态管理
        self._is_ready = False
        self._current_model = None
        self._current_expression = "默认"
        self._current_motion = "待机"
        self._debug_mode = False
        self._scale = 1.0
        self._position = (0.5, 0.5)  # 相对位置 (0-1)
        
        # 性能监控
        self._fps = 0
        self._fps_timer = QTimer()
        self._fps_timer.timeout.connect(self._update_performance)
        self._fps_timer.start(1000)  # 每秒更新一次
        
        # 模型配置
        self._models_dir = Path("aipet/assets/live2d/models")
        self._available_models = []
        self._load_available_models()

        self.logger.info("Live2D桥接器初始化完成")

    # ===== Web端回调方法 (由JavaScript调用) =====

    @Slot()
    def notifyReady(self):
        """Web端通知已准备就绪"""
        self._is_ready = True
        self.ready.emit()
        self.logger.info("✅ Live2D Web组件已准备就绪")

    @Slot(str)
    def notifyModelLoaded(self, model_path: str):
        """Web端通知模型加载完成"""
        self._current_model = model_path
        self.modelLoaded.emit(model_path)
        self.logger.info(f"✅ Live2D模型加载完成: {model_path}")

    @Slot(str, str)
    def notifyModelLoadFailed(self, model_path: str, error: str):
        """Web端通知模型加载失败"""
        self.modelLoadFailed.emit(model_path, error)
        self.logger.error(f"❌ Live2D模型加载失败: {model_path}, 错误: {error}")

    @Slot(str, float)
    def notifyExpressionChanged(self, expression_name: str, intensity: float):
        """Web端通知表情已变化"""
        self._current_expression = expression_name
        self.expressionChanged.emit(expression_name, intensity)
        self.logger.debug(f"🎭 表情已变化: {expression_name} (强度: {intensity})")

    @Slot(str)
    def notifyMotionStarted(self, motion_name: str):
        """Web端通知动作已开始"""
        self._current_motion = motion_name
        self.motionStarted.emit(motion_name)
        self.logger.debug(f"🎬 动作已开始: {motion_name}")

    @Slot(str)
    def notifyMotionFinished(self, motion_name: str):
        """Web端通知动作已完成"""
        self.motionFinished.emit(motion_name)
        self.logger.debug(f"✅ 动作已完成: {motion_name}")

    @Slot()
    def notifyModelClicked(self):
        """Web端通知模型被点击"""
        self.modelClicked.emit()
        self.logger.debug("🖱️ Live2D模型被点击")

    @Slot(bool)
    def notifyModelHovered(self, is_hovered: bool):
        """Web端通知模型悬停状态"""
        self.modelHovered.emit(is_hovered)
        self.logger.debug(f"🖱️ Live2D模型悬停: {is_hovered}")

    @Slot(int)
    def notifyFpsChanged(self, fps: int):
        """Web端通知FPS变化"""
        self._fps = fps
        self.fpsChanged.emit(fps)

    @Slot(str)
    def notifyError(self, error_message: str):
        """Web端通知错误发生"""
        self.errorOccurred.emit(error_message)
        self.logger.error(f"❌ Live2D错误: {error_message}")

    # ===== Qt端控制方法 (供QML调用) =====

    @Slot(str)
    def loadModel(self, model_path: str):
        """加载Live2D模型"""
        if not self._is_ready:
            self.logger.warning("Live2D组件尚未准备就绪")
            return
            
        self.logger.info(f"🎭 请求加载Live2D模型: {model_path}")
        self.modelLoadRequested.emit(model_path)

    @Slot(str)
    @Slot(str, float)
    def setExpression(self, expression_name: str, intensity: float = 1.0):
        """设置表情"""
        if not self._is_ready:
            self.logger.warning("Live2D组件尚未准备就绪")
            return
            
        self.logger.debug(f"🎭 设置表情: {expression_name} (强度: {intensity})")
        self.expressionChangeRequested.emit(expression_name, intensity)

    @Slot(str)
    @Slot(str, int)
    def playMotion(self, motion_name: str, priority: int = 1):
        """播放动作"""
        if not self._is_ready:
            self.logger.warning("Live2D组件尚未准备就绪")
            return
            
        self.logger.debug(f"🎬 播放动作: {motion_name} (优先级: {priority})")
        self.motionPlayRequested.emit(motion_name, priority)

    @Slot(bool)
    def setDebugMode(self, enabled: bool):
        """设置调试模式"""
        self._debug_mode = enabled
        self.debugModeChanged.emit(enabled)
        self.logger.info(f"🔧 调试模式: {'开启' if enabled else '关闭'}")

    @Slot(float)
    def setScale(self, scale: float):
        """设置缩放"""
        self._scale = max(0.1, min(3.0, scale))  # 限制缩放范围
        self.scaleChanged.emit(self._scale)
        self.logger.debug(f"📏 设置缩放: {self._scale}")

    @Slot(float, float)
    def setPosition(self, x: float, y: float):
        """设置位置 (相对坐标 0-1)"""
        self._position = (max(0, min(1, x)), max(0, min(1, y)))
        self.positionChanged.emit(self._position[0], self._position[1])
        self.logger.debug(f"📍 设置位置: {self._position}")

    # ===== 口型同步控制方法 =====

    @Slot(list)
    def startLipSync(self, mouth_data: list):
        """开始口型同步

        Args:
            mouth_data: 口型数据列表，格式: [{"timestamp": ms, "value": 0-1}, ...]
        """
        if not self._is_ready:
            self.logger.warning("Live2D组件尚未准备就绪")
            return

        self.logger.info(f"🎤 开始口型同步，数据点数: {len(mouth_data)}")
        self.lipSyncStartRequested.emit(mouth_data)

    @Slot()
    def stopLipSync(self):
        """停止口型同步"""
        if not self._is_ready:
            self.logger.warning("Live2D组件尚未准备就绪")
            return

        self.logger.info("🎤 停止口型同步")
        self.lipSyncStopRequested.emit()

    @Slot(float)
    def setMouthOpening(self, value: float):
        """设置口型开合度

        Args:
            value: 开合度 (0-1)
        """
        if not self._is_ready:
            self.logger.warning("Live2D组件尚未准备就绪")
            return

        value = max(0, min(1, value))
        self.logger.debug(f"🎤 设置口型开合度: {value}")
        self.mouthOpeningChanged.emit(value)

    # ===== 状态查询方法 =====

    @Slot(result=bool)
    def isReady(self) -> bool:
        """检查是否准备就绪"""
        return self._is_ready

    @Slot(result=str)
    def getCurrentModel(self) -> str:
        """获取当前模型路径"""
        return self._current_model or ""

    @Slot(result=str)
    def getCurrentExpression(self) -> str:
        """获取当前表情"""
        return self._current_expression

    @Slot(result=str)
    def getCurrentMotion(self) -> str:
        """获取当前动作"""
        return self._current_motion

    @Slot(result=int)
    def getFps(self) -> int:
        """获取当前FPS"""
        return self._fps

    @Slot(result=bool)
    def isDebugMode(self) -> bool:
        """获取调试模式状态"""
        return self._debug_mode

    @Slot(result=float)
    def getScale(self) -> float:
        """获取当前缩放"""
        return self._scale

    @Slot(result=list)
    def getPosition(self) -> list:
        """获取当前位置"""
        return list(self._position)

    # ===== 模型管理方法 =====

    @Slot(result=list)
    def getAvailableModels(self) -> list:
        """获取可用模型列表"""
        self._scan_models()
        return self._available_models

    @Slot(str)
    def refreshModels(self, models_dir: str = ""):
        """刷新模型列表"""
        if models_dir:
            self._models_dir = Path(models_dir)
        self._scan_models()
        self.logger.info(f"🔄 模型列表已刷新，找到 {len(self._available_models)} 个模型")

    def _scan_models(self):
        """扫描可用模型"""
        self._available_models = []
        
        if not self._models_dir.exists():
            self.logger.warning(f"模型目录不存在: {self._models_dir}")
            return
            
        # 扫描.model3.json文件
        for model_file in self._models_dir.rglob("*.model3.json"):
            relative_path = model_file.relative_to(self._models_dir)
            self._available_models.append(str(relative_path))
            
        self.logger.debug(f"扫描到 {len(self._available_models)} 个Live2D模型")

    def _load_available_models(self):
        """加载可用模型列表"""
        try:
            if not self._models_dir.exists():
                self.logger.warning(f"模型目录不存在: {self._models_dir}")
                return

            self._available_models = []

            # 扫描模型目录
            for model_dir in self._models_dir.iterdir():
                if model_dir.is_dir():
                    # 查找.model3.json文件
                    model_files = list(model_dir.glob("*.model3.json"))
                    if model_files:
                        model_info = {
                            'name': model_dir.name,
                            'displayName': model_dir.name.title(),
                            'path': str(model_files[0].relative_to(self._models_dir.parent)),
                            'directory': str(model_dir)
                        }
                        self._available_models.append(model_info)
                        self.logger.debug(f"发现模型: {model_info['name']}")

            self.logger.info(f"加载了 {len(self._available_models)} 个模型")
            self.availableModelsChanged.emit(self._available_models)

        except Exception as e:
            self.logger.error(f"加载模型列表失败: {e}")

    # ===== 性能监控 =====

    def _update_performance(self):
        """更新性能信息"""
        # 这里可以添加更多性能监控逻辑
        pass

    # ===== 工具方法 =====

    @Slot(str)
    def logMessage(self, message: str):
        """记录来自Web端的日志"""
        self.logger.debug(f"[Web] {message}")

    @Slot(str)
    def logError(self, error: str):
        """记录来自Web端的错误"""
        self.logger.error(f"[Web] {error}")

    # ===== 模型管理方法 =====

    def get_available_models(self) -> List[Dict[str, str]]:
        """获取可用模型列表"""
        return self._available_models.copy()

    def refresh_models(self):
        """刷新模型列表"""
        self._load_available_models()

    @Slot(str)
    def switch_model(self, model_name: str):
        """切换模型"""
        try:
            # 查找模型信息
            model_info = None
            for model in self._available_models:
                if model['name'] == model_name:
                    model_info = model
                    break

            if not model_info:
                self.logger.error(f"未找到模型: {model_name}")
                self.modelLoadFailed.emit(model_name, "模型不存在")
                return

            self.logger.info(f"切换到模型: {model_name}")
            self._current_model = model_name

            # 发送切换请求到Web端
            self.modelLoadRequested.emit(model_info['path'])

        except Exception as e:
            self.logger.error(f"模型切换失败: {e}")
            self.modelLoadFailed.emit(model_name, str(e))

    def cleanup(self):
        """清理资源"""
        if self._fps_timer:
            self._fps_timer.stop()
        self.logger.info("Live2D桥接器已清理")
