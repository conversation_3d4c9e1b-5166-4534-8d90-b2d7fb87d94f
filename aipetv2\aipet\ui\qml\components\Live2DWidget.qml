import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtWebEngine 1.10
import QtWebChannel 1.15

/**
 * Live2D桌宠组件
 * 基于WebEngine + Live2D Web SDK实现
 */
Rectangle {
    id: root
    
    // ===== 公共属性 =====
    property string modelPath: ""
    property string currentExpression: "默认"
    property string currentMotion: "待机"
    property bool debugMode: false
    property real modelScale: 1.0
    property point modelPosition: Qt.point(0.5, 0.5)
    property bool interactive: true
    property bool transparent: true
    
    // ===== 状态属性 =====
    property bool isReady: false
    property bool isLoading: false
    property int fps: 0
    property string lastError: ""
    
    // ===== 信号定义 =====
    signal ready()
    signal modelLoaded(string path)
    signal modelLoadFailed(string path, string error)
    signal expressionChanged(string name, real intensity)
    signal motionStarted(string name)
    signal motionFinished(string name)
    signal modelClicked()
    signal modelHovered(bool isHovered)
    signal errorOccurred(string error)
    
    // ===== 外观设置 =====
    color: transparent ? "transparent" : "#f0f0f0"
    border.width: debugMode ? 2 : 0
    border.color: debugMode ? "#3498db" : "transparent"
    radius: 8
    
    // ===== WebChannel设置 =====
    WebChannel {
        id: webChannel
        registeredObjects: [live2dBridge]
    }
    
    // ===== Live2D WebEngine视图 =====
    WebEngineView {
        id: webEngineView
        anchors.fill: parent
        
        // 设置WebChannel
        webChannel: webChannel
        
        // 加载Live2D HTML页面
        url: Qt.resolvedUrl("../../web/live2d_viewer.html")
        
        // 背景透明
        backgroundColor: "transparent"
        
        // 设置
        settings {
            // 启用JavaScript
            javascriptEnabled: true
            // 允许本地文件访问
            localContentCanAccessFileUrls: true
            localContentCanAccessRemoteUrls: true
            // 启用WebGL
            webGLEnabled: true
            // 启用硬件加速
            accelerated2dCanvasEnabled: true
        }
        
        // 加载完成处理
        onLoadingChanged: {
            if (loadRequest.status === WebEngineLoadRequest.LoadSucceededStatus) {
                console.log("✅ Live2D页面加载成功")
                root.isLoading = false
            } else if (loadRequest.status === WebEngineLoadRequest.LoadFailedStatus) {
                console.error("❌ Live2D页面加载失败:", loadRequest.errorString)
                root.lastError = loadRequest.errorString
                root.errorOccurred(loadRequest.errorString)
                root.isLoading = false
            } else if (loadRequest.status === WebEngineLoadRequest.LoadStartedStatus) {
                console.log("🔄 开始加载Live2D页面")
                root.isLoading = true
            }
        }
        
        // JavaScript控制台消息
        onJavaScriptConsoleMessage: {
            var prefix = level === WebEngineView.InfoMessageLevel ? "[INFO]" : 
                        level === WebEngineView.WarningMessageLevel ? "[WARN]" : "[ERROR]"
            console.log("Live2D " + prefix + " " + message + " (line: " + lineNumber + ")")
        }
        
        // 右键菜单
        onContextMenuRequested: {
            if (debugMode) {
                contextMenu.popup()
            }
        }
    }
    
    // ===== 加载指示器 =====
    Rectangle {
        id: loadingIndicator
        anchors.centerIn: parent
        width: 200
        height: 100
        color: "#34495e"
        radius: 8
        visible: root.isLoading
        opacity: 0.9
        
        Column {
            anchors.centerIn: parent
            spacing: 10
            
            BusyIndicator {
                anchors.horizontalCenter: parent.horizontalCenter
                running: root.isLoading
            }
            
            Text {
                anchors.horizontalCenter: parent.horizontalCenter
                text: "正在加载Live2D..."
                color: "white"
                font.pixelSize: 14
            }
        }
    }
    
    // ===== 错误显示 =====
    Rectangle {
        id: errorIndicator
        anchors.centerIn: parent
        width: 300
        height: 120
        color: "#e74c3c"
        radius: 8
        visible: root.lastError !== "" && !root.isLoading && !root.isReady
        opacity: 0.9
        
        Column {
            anchors.centerIn: parent
            spacing: 10
            
            Text {
                anchors.horizontalCenter: parent.horizontalCenter
                text: "❌ Live2D加载失败"
                color: "white"
                font.pixelSize: 16
                font.bold: true
            }
            
            Text {
                anchors.horizontalCenter: parent.horizontalCenter
                text: root.lastError
                color: "white"
                font.pixelSize: 12
                wrapMode: Text.WordWrap
                width: parent.parent.width - 20
                horizontalAlignment: Text.AlignHCenter
            }
            
            Button {
                anchors.horizontalCenter: parent.horizontalCenter
                text: "重试"
                onClicked: {
                    root.lastError = ""
                    webEngineView.reload()
                }
            }
        }
    }
    
    // ===== 调试面板 =====
    Rectangle {
        id: debugPanel
        anchors.top: parent.top
        anchors.right: parent.right
        anchors.margins: 10
        width: 200
        height: 150
        color: "#2c3e50"
        radius: 4
        opacity: 0.9
        visible: root.debugMode && root.isReady
        
        Column {
            anchors.fill: parent
            anchors.margins: 10
            spacing: 5
            
            Text {
                text: "🔧 Live2D调试"
                color: "white"
                font.bold: true
                font.pixelSize: 12
            }
            
            Text {
                text: "FPS: " + root.fps
                color: root.fps > 30 ? "#2ecc71" : root.fps > 15 ? "#f39c12" : "#e74c3c"
                font.pixelSize: 11
            }
            
            Text {
                text: "模型: " + (root.modelPath ? root.modelPath.split('/').pop() : "无")
                color: "white"
                font.pixelSize: 11
                elide: Text.ElideMiddle
                width: parent.width
            }
            
            Text {
                text: "表情: " + root.currentExpression
                color: "#3498db"
                font.pixelSize: 11
            }
            
            Text {
                text: "动作: " + root.currentMotion
                color: "#9b59b6"
                font.pixelSize: 11
            }
            
            Text {
                text: "缩放: " + root.modelScale.toFixed(2)
                color: "white"
                font.pixelSize: 11
            }
        }
    }
    
    // ===== 右键菜单 =====
    Menu {
        id: contextMenu
        
        MenuItem {
            text: "重新加载"
            onTriggered: webEngineView.reload()
        }
        
        MenuItem {
            text: "开发者工具"
            onTriggered: webEngineView.triggerWebAction(WebEngineView.InspectElement)
        }
        
        MenuSeparator {}
        
        MenuItem {
            text: root.debugMode ? "关闭调试" : "开启调试"
            onTriggered: root.debugMode = !root.debugMode
        }
    }
    
    // ===== 公共方法 =====
    
    /**
     * 加载Live2D模型
     */
    function loadModel(path) {
        if (!root.isReady) {
            console.warn("Live2D组件尚未准备就绪")
            return false
        }
        
        console.log("🎭 加载Live2D模型:", path)
        root.modelPath = path
        live2dBridge.loadModel(path)
        return true
    }
    
    /**
     * 设置表情
     */
    function setExpression(name, intensity) {
        if (!root.isReady) {
            console.warn("Live2D组件尚未准备就绪")
            return false
        }
        
        intensity = intensity || 1.0
        console.log("🎭 设置表情:", name, "强度:", intensity)
        live2dBridge.setExpression(name, intensity)
        return true
    }
    
    /**
     * 播放动作
     */
    function playMotion(name, priority) {
        if (!root.isReady) {
            console.warn("Live2D组件尚未准备就绪")
            return false
        }
        
        priority = priority || 1
        console.log("🎬 播放动作:", name, "优先级:", priority)
        live2dBridge.playMotion(name, priority)
        return true
    }
    
    /**
     * 设置缩放
     */
    function setScale(scale) {
        root.modelScale = scale
        if (root.isReady) {
            live2dBridge.setScale(scale)
        }
    }
    
    /**
     * 设置位置
     */
    function setPosition(x, y) {
        root.modelPosition = Qt.point(x, y)
        if (root.isReady) {
            live2dBridge.setPosition(x, y)
        }
    }
    
    /**
     * 获取可用模型列表
     */
    function getAvailableModels() {
        if (!root.isReady) {
            return []
        }
        return live2dBridge.getAvailableModels()
    }
    
    // ===== 属性监听 =====
    
    onDebugModeChanged: {
        if (root.isReady) {
            live2dBridge.setDebugMode(debugMode)
        }
    }
    
    onModelScaleChanged: {
        if (root.isReady) {
            live2dBridge.setScale(modelScale)
        }
    }
    
    onModelPositionChanged: {
        if (root.isReady) {
            live2dBridge.setPosition(modelPosition.x, modelPosition.y)
        }
    }
    
    // ===== 组件初始化 =====
    Component.onCompleted: {
        console.log("🎭 Live2D组件初始化完成")
        
        // 连接桥接器信号
        live2dBridge.ready.connect(function() {
            root.isReady = true
            root.ready()
            console.log("✅ Live2D组件准备就绪")
        })
        
        live2dBridge.modelLoaded.connect(function(path) {
            root.modelLoaded(path)
        })
        
        live2dBridge.modelLoadFailed.connect(function(path, error) {
            root.lastError = error
            root.modelLoadFailed(path, error)
        })
        
        live2dBridge.expressionChanged.connect(function(name, intensity) {
            root.currentExpression = name
            root.expressionChanged(name, intensity)
        })
        
        live2dBridge.motionStarted.connect(function(name) {
            root.currentMotion = name
            root.motionStarted(name)
        })
        
        live2dBridge.motionFinished.connect(function(name) {
            root.motionFinished(name)
        })
        
        live2dBridge.modelClicked.connect(function() {
            root.modelClicked()
        })
        
        live2dBridge.modelHovered.connect(function(isHovered) {
            root.modelHovered(isHovered)
        })
        
        live2dBridge.fpsChanged.connect(function(fps) {
            root.fps = fps
        })
        
        live2dBridge.errorOccurred.connect(function(error) {
            root.lastError = error
            root.errorOccurred(error)
        })
    }
    
    Component.onDestruction: {
        console.log("🎭 Live2D组件正在销毁")
        if (live2dBridge) {
            live2dBridge.cleanup()
        }
    }
}
