/**
 * Live2D模型管理器
 * 负责模型的加载、缓存、切换和资源管理
 */

class Live2DModelManager {
    constructor() {
        this.models = new Map(); // 模型缓存
        this.currentModel = null;
        this.modelsConfig = null;
        this.basePath = 'aipet/assets/live2d/models/';
        
        // 支持的模型列表
        this.availableModels = [
            {
                name: 'ganyu',
                displayName: '甘雨',
                path: 'ganyu/ganyu.model3.json',
                preview: 'ganyu/preview.png'
            },
            {
                name: 'linghu', 
                displayName: '灵狐',
                path: 'linghu/芊芊.model3.json',
                preview: 'linghu/preview.png'
            },
            {
                name: 'baimeimo',
                displayName: '白美魔',
                path: 'baimeimo_by_令吾05_671ced21afd775e7bb09387bbdb26fca/baimeimo.model3.json',
                preview: 'baimeimo_by_令吾05_671ced21afd775e7bb09387bbdb26fca/preview.png'
            },
            {
                name: 'murasame',
                displayName: '丛雨',
                path: 'Murasame丛雨/Murasame.model3.json', 
                preview: 'Murasame丛雨/preview.png'
            }
        ];
        
        this.loadingPromises = new Map(); // 防止重复加载
        this.logger = console;
    }

    /**
     * 初始化模型管理器
     */
    async initialize() {
        try {
            this.logger.log('🎭 初始化Live2D模型管理器...');
            
            // 扫描可用模型
            await this.scanAvailableModels();
            
            this.logger.log('✅ Live2D模型管理器初始化完成');
            return true;
            
        } catch (error) {
            this.logger.error('❌ Live2D模型管理器初始化失败:', error);
            return false;
        }
    }

    /**
     * 扫描可用的模型文件
     */
    async scanAvailableModels() {
        const validModels = [];
        
        for (const modelInfo of this.availableModels) {
            // 暂时跳过网络检查，直接添加所有模型
            // 在实际部署时，模型文件应该已经正确放置
            validModels.push(modelInfo);
            this.logger.log(`📋 注册模型: ${modelInfo.displayName} -> ${modelInfo.path}`);
        }
        
        this.availableModels = validModels;
        this.logger.log(`📊 共发现 ${validModels.length} 个可用模型`);
    }

    /**
     * 加载指定模型
     * @param {string} modelName - 模型名称
     * @returns {Promise<Object>} 模型对象
     */
    async loadModel(modelName) {
        try {
            // 检查是否已在加载中
            if (this.loadingPromises.has(modelName)) {
                this.logger.log(`⏳ 模型 ${modelName} 正在加载中，等待完成...`);
                return await this.loadingPromises.get(modelName);
            }

            // 检查缓存
            if (this.models.has(modelName)) {
                this.logger.log(`📦 从缓存加载模型: ${modelName}`);
                return this.models.get(modelName);
            }

            // 查找模型配置
            const modelInfo = this.availableModels.find(m => m.name === modelName);
            if (!modelInfo) {
                throw new Error(`未找到模型: ${modelName}`);
            }

            // 开始加载
            this.logger.log(`🔄 开始加载模型: ${modelInfo.displayName}`);
            const loadPromise = this._loadModelFromPath(modelInfo);
            this.loadingPromises.set(modelName, loadPromise);

            const model = await loadPromise;
            
            // 缓存模型
            this.models.set(modelName, model);
            this.loadingPromises.delete(modelName);
            
            this.logger.log(`✅ 模型加载完成: ${modelInfo.displayName}`);
            return model;

        } catch (error) {
            this.loadingPromises.delete(modelName);
            this.logger.error(`❌ 模型加载失败: ${modelName}`, error);
            throw error;
        }
    }

    /**
     * 从路径加载模型
     * @private
     */
    async _loadModelFromPath(modelInfo) {
        const modelPath = this.basePath + modelInfo.path;
        const modelDir = modelPath.substring(0, modelPath.lastIndexOf('/') + 1);
        
        // 加载模型配置
        const response = await fetch(modelPath);
        if (!response.ok) {
            throw new Error(`无法加载模型配置: ${response.status}`);
        }
        
        const modelConfig = await response.json();
        
        // 创建Live2D模型实例
        const model = await PIXI.live2d.Live2DModel.from(modelPath);
        
        // 设置模型属性
        model.modelInfo = modelInfo;
        model.config = modelConfig;
        model.basePath = modelDir;
        
        // 预加载表情和动作
        await this._preloadExpressions(model, modelConfig, modelDir);
        await this._preloadMotions(model, modelConfig, modelDir);
        
        return model;
    }

    /**
     * 预加载表情文件
     * @private
     */
    async _preloadExpressions(model, config, basePath) {
        if (!config.FileReferences?.Expressions) return;
        
        model.expressions = new Map();
        
        for (const exp of config.FileReferences.Expressions) {
            try {
                const expPath = basePath + exp.File;
                const response = await fetch(expPath);
                if (response.ok) {
                    const expData = await response.json();
                    model.expressions.set(exp.Name, expData);
                    this.logger.log(`📝 预加载表情: ${exp.Name}`);
                }
            } catch (error) {
                this.logger.warn(`⚠️ 表情加载失败: ${exp.Name}`, error);
            }
        }
    }

    /**
     * 预加载动作文件
     * @private
     */
    async _preloadMotions(model, config, basePath) {
        if (!config.FileReferences?.Motions) return;
        
        model.motions = new Map();
        
        for (const [group, motions] of Object.entries(config.FileReferences.Motions)) {
            for (const motion of motions) {
                try {
                    const motionPath = basePath + motion.File;
                    const response = await fetch(motionPath);
                    if (response.ok) {
                        const motionData = await response.json();
                        const key = group ? `${group}_${motion.Name || motion.File}` : (motion.Name || motion.File);
                        model.motions.set(key, motionData);
                        this.logger.log(`🎬 预加载动作: ${key}`);
                    }
                } catch (error) {
                    this.logger.warn(`⚠️ 动作加载失败: ${motion.File}`, error);
                }
            }
        }
    }

    /**
     * 切换到指定模型
     * @param {string} modelName - 模型名称
     * @returns {Promise<Object>} 切换后的模型
     */
    async switchModel(modelName) {
        try {
            this.logger.log(`🔄 切换模型: ${modelName}`);
            
            const model = await this.loadModel(modelName);
            
            // 清理当前模型
            if (this.currentModel && this.currentModel !== model) {
                this.currentModel.destroy();
            }
            
            this.currentModel = model;
            this.logger.log(`✅ 模型切换完成: ${model.modelInfo.displayName}`);
            
            return model;
            
        } catch (error) {
            this.logger.error(`❌ 模型切换失败: ${modelName}`, error);
            throw error;
        }
    }

    /**
     * 获取可用模型列表
     */
    getAvailableModels() {
        return this.availableModels.map(model => ({
            name: model.name,
            displayName: model.displayName,
            preview: model.preview
        }));
    }

    /**
     * 获取当前模型
     */
    getCurrentModel() {
        return this.currentModel;
    }

    /**
     * 清理资源
     */
    dispose() {
        // 清理所有缓存的模型
        for (const model of this.models.values()) {
            if (model.destroy) {
                model.destroy();
            }
        }
        
        this.models.clear();
        this.loadingPromises.clear();
        this.currentModel = null;
        
        this.logger.log('🧹 Live2D模型管理器资源已清理');
    }
}

// 导出模型管理器
window.Live2DModelManager = Live2DModelManager;
