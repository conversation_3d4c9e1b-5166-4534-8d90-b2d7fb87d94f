/**
 * Live2D工具函数库
 * 提供坐标转换、动画插值、表情混合等实用工具
 */

class Live2DUtils {
    /**
     * 坐标转换工具
     */
    static coordinate = {
        /**
         * 屏幕坐标转换为Live2D模型坐标
         * @param {number} screenX - 屏幕X坐标
         * @param {number} screenY - 屏幕Y坐标
         * @param {Object} model - Live2D模型对象
         * @param {Object} viewport - 视口信息
         * @returns {Object} Live2D坐标 {x, y}
         */
        screenToModel(screenX, screenY, model, viewport) {
            if (!model || !viewport) return { x: 0, y: 0 };
            
            // 转换为相对坐标 (-1 到 1)
            const relativeX = (screenX / viewport.width) * 2 - 1;
            const relativeY = -((screenY / viewport.height) * 2 - 1); // Y轴翻转
            
            return { x: relativeX, y: relativeY };
        },

        /**
         * Live2D模型坐标转换为屏幕坐标
         * @param {number} modelX - 模型X坐标
         * @param {number} modelY - 模型Y坐标
         * @param {Object} viewport - 视口信息
         * @returns {Object} 屏幕坐标 {x, y}
         */
        modelToScreen(modelX, modelY, viewport) {
            if (!viewport) return { x: 0, y: 0 };
            
            const screenX = (modelX + 1) * viewport.width / 2;
            const screenY = (-modelY + 1) * viewport.height / 2;
            
            return { x: screenX, y: screenY };
        },

        /**
         * 计算模型边界框
         * @param {Object} model - Live2D模型对象
         * @returns {Object} 边界框 {left, right, top, bottom}
         */
        getModelBounds(model) {
            if (!model) return { left: 0, right: 0, top: 0, bottom: 0 };
            
            const bounds = model.getBounds();
            return {
                left: bounds.x,
                right: bounds.x + bounds.width,
                top: bounds.y,
                bottom: bounds.y + bounds.height
            };
        }
    };

    /**
     * 动画插值工具
     */
    static animation = {
        /**
         * 线性插值
         * @param {number} start - 起始值
         * @param {number} end - 结束值
         * @param {number} t - 插值参数 (0-1)
         * @returns {number} 插值结果
         */
        lerp(start, end, t) {
            return start + (end - start) * Math.max(0, Math.min(1, t));
        },

        /**
         * 缓动函数 - 缓入缓出
         * @param {number} t - 时间参数 (0-1)
         * @returns {number} 缓动值
         */
        easeInOut(t) {
            return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
        },

        /**
         * 缓动函数 - 弹性效果
         * @param {number} t - 时间参数 (0-1)
         * @returns {number} 缓动值
         */
        easeElastic(t) {
            if (t === 0 || t === 1) return t;
            const p = 0.3;
            const s = p / 4;
            return Math.pow(2, -10 * t) * Math.sin((t - s) * (2 * Math.PI) / p) + 1;
        },

        /**
         * 创建动画时间轴
         * @param {number} duration - 动画持续时间(毫秒)
         * @param {Function} easing - 缓动函数
         * @returns {Object} 动画时间轴对象
         */
        createTimeline(duration, easing = this.easeInOut) {
            const startTime = performance.now();
            
            return {
                getProgress() {
                    const elapsed = performance.now() - startTime;
                    const rawProgress = Math.min(elapsed / duration, 1);
                    return easing(rawProgress);
                },
                
                isComplete() {
                    return performance.now() - startTime >= duration;
                },
                
                reset() {
                    startTime = performance.now();
                }
            };
        }
    };

    /**
     * 表情混合工具
     */
    static expression = {
        /**
         * 混合两个表情
         * @param {Object} expr1 - 表情1数据
         * @param {Object} expr2 - 表情2数据
         * @param {number} weight - 混合权重 (0-1)
         * @returns {Object} 混合后的表情数据
         */
        blend(expr1, expr2, weight) {
            if (!expr1 || !expr2) return expr1 || expr2;
            
            const blended = { Parameters: [] };
            const params1 = expr1.Parameters || [];
            const params2 = expr2.Parameters || [];
            
            // 创建参数映射
            const paramMap = new Map();
            
            params1.forEach(param => {
                paramMap.set(param.Id, { ...param, weight: 1 - weight });
            });
            
            params2.forEach(param => {
                if (paramMap.has(param.Id)) {
                    const existing = paramMap.get(param.Id);
                    existing.Value = Live2DUtils.animation.lerp(existing.Value, param.Value, weight);
                } else {
                    paramMap.set(param.Id, { ...param, weight: weight });
                }
            });
            
            blended.Parameters = Array.from(paramMap.values());
            return blended;
        },

        /**
         * 应用表情到模型
         * @param {Object} model - Live2D模型
         * @param {Object} expression - 表情数据
         * @param {number} intensity - 表情强度 (0-1)
         */
        apply(model, expression, intensity = 1.0) {
            if (!model || !expression || !expression.Parameters) return;
            
            expression.Parameters.forEach(param => {
                const value = param.Value * intensity;
                model.internalModel.coreModel.setParameterValueById(param.Id, value);
            });
        }
    };

    /**
     * 性能优化工具
     */
    static performance = {
        /**
         * 帧率监控器
         */
        createFPSMonitor() {
            let frameCount = 0;
            let lastTime = performance.now();
            let fps = 0;
            
            return {
                update() {
                    frameCount++;
                    const currentTime = performance.now();
                    
                    if (currentTime - lastTime >= 1000) {
                        fps = Math.round(frameCount * 1000 / (currentTime - lastTime));
                        frameCount = 0;
                        lastTime = currentTime;
                    }
                    
                    return fps;
                },
                
                getFPS() {
                    return fps;
                }
            };
        },

        /**
         * 内存使用监控
         */
        getMemoryUsage() {
            if (performance.memory) {
                return {
                    used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                    total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                    limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
                };
            }
            return null;
        },

        /**
         * 节流函数
         * @param {Function} func - 要节流的函数
         * @param {number} delay - 延迟时间(毫秒)
         * @returns {Function} 节流后的函数
         */
        throttle(func, delay) {
            let lastCall = 0;
            return function(...args) {
                const now = performance.now();
                if (now - lastCall >= delay) {
                    lastCall = now;
                    return func.apply(this, args);
                }
            };
        },

        /**
         * 防抖函数
         * @param {Function} func - 要防抖的函数
         * @param {number} delay - 延迟时间(毫秒)
         * @returns {Function} 防抖后的函数
         */
        debounce(func, delay) {
            let timeoutId;
            return function(...args) {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(() => func.apply(this, args), delay);
            };
        }
    };

    /**
     * 数学工具
     */
    static math = {
        /**
         * 角度转弧度
         * @param {number} degrees - 角度
         * @returns {number} 弧度
         */
        degToRad(degrees) {
            return degrees * Math.PI / 180;
        },

        /**
         * 弧度转角度
         * @param {number} radians - 弧度
         * @returns {number} 角度
         */
        radToDeg(radians) {
            return radians * 180 / Math.PI;
        },

        /**
         * 限制数值范围
         * @param {number} value - 输入值
         * @param {number} min - 最小值
         * @param {number} max - 最大值
         * @returns {number} 限制后的值
         */
        clamp(value, min, max) {
            return Math.max(min, Math.min(max, value));
        },

        /**
         * 生成随机数
         * @param {number} min - 最小值
         * @param {number} max - 最大值
         * @returns {number} 随机数
         */
        random(min, max) {
            return Math.random() * (max - min) + min;
        },

        /**
         * 计算两点距离
         * @param {number} x1 - 点1 X坐标
         * @param {number} y1 - 点1 Y坐标
         * @param {number} x2 - 点2 X坐标
         * @param {number} y2 - 点2 Y坐标
         * @returns {number} 距离
         */
        distance(x1, y1, x2, y2) {
            const dx = x2 - x1;
            const dy = y2 - y1;
            return Math.sqrt(dx * dx + dy * dy);
        }
    };

    /**
     * 调试工具
     */
    static debug = {
        /**
         * 显示模型信息
         * @param {Object} model - Live2D模型
         */
        logModelInfo(model) {
            if (!model) {
                console.log('❌ 模型为空');
                return;
            }
            
            console.group('🎭 Live2D模型信息');
            console.log('模型名称:', model.modelInfo?.displayName || '未知');
            console.log('模型路径:', model.modelInfo?.path || '未知');
            console.log('参数数量:', model.internalModel?.coreModel?.getParameterCount() || 0);
            console.log('部件数量:', model.internalModel?.coreModel?.getPartCount() || 0);
            console.log('可绘制数量:', model.internalModel?.coreModel?.getDrawableCount() || 0);
            console.log('表情数量:', model.expressions?.size || 0);
            console.log('动作数量:', model.motions?.size || 0);
            console.groupEnd();
        },

        /**
         * 显示性能信息
         * @param {Object} stats - 性能统计
         */
        logPerformanceInfo(stats) {
            console.group('📊 性能信息');
            console.log('FPS:', stats.fps || 0);
            console.log('内存使用:', stats.memory || '未知');
            console.log('渲染时间:', stats.renderTime || 0, 'ms');
            console.groupEnd();
        }
    };
}

// 导出工具类
window.Live2DUtils = Live2DUtils;
