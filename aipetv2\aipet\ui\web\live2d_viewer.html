<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live2D桌宠</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: transparent;
            overflow: hidden;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        #live2d-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        
        .debug-panel {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            display: none;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <canvas id="live2d-canvas"></canvas>
    <div class="loading" id="loading">正在加载Live2D模型...</div>
    <div class="debug-panel" id="debug-panel">
        <div>FPS: <span id="fps">0</span></div>
        <div>表情: <span id="current-expression">默认</span></div>
        <div>动作: <span id="current-motion">待机</span></div>
    </div>

    <!-- Live2D Web SDK - 使用本地库 -->
    <script src="libs/pixi.min.js"></script>
    <script src="libs/pixi-live2d-display.min.js"></script>

    <!-- Live2D工具库 -->
    <script src="live2d_models.js"></script>
    <script src="live2d_utils.js"></script>

    <!-- Qt WebChannel -->
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>

    <script>
        class Live2DManager {
            constructor() {
                this.app = null;
                this.model = null;
                this.bridge = null;
                this.isReady = false;
                this.currentExpression = '默认';
                this.currentMotion = '待机';
                this.fps = 0;

                // 口型同步相关
                this.mouthSyncData = [];
                this.mouthSyncTimer = null;
                this.currentMouthValue = 0;

                // 模型管理器
                this.modelManager = new Live2DModelManager();

                // 性能监控
                this.fpsMonitor = Live2DUtils.performance.createFPSMonitor();

                // 表情动作管理
                this.expressionManager = null;
                this.motionManager = null;

                this.init();
            }

            async init() {
                try {
                    console.log('🎭 初始化Live2D管理器...');

                    // 初始化PIXI应用
                    await this.initPixiApp();

                    // 初始化模型管理器
                    await this.modelManager.initialize();

                    // 建立WebChannel连接
                    await this.setupWebChannel();

                    // 加载默认模型
                    await this.loadDefaultModel();

                    // 启动渲染循环
                    this.startRenderLoop();

                    console.log('✅ Live2D管理器初始化完成');

                } catch (error) {
                    console.error('❌ Live2D初始化失败:', error);
                    this.showError('Live2D初始化失败: ' + error.message);
                }
            }

            async initPixiApp() {
                console.log('🎨 初始化PIXI应用...');

                const canvas = document.getElementById('live2d-canvas');

                this.app = new PIXI.Application({
                    view: canvas,
                    width: window.innerWidth,
                    height: window.innerHeight,
                    backgroundColor: 0x000000,
                    backgroundAlpha: 0, // 透明背景
                    antialias: true,
                    resolution: window.devicePixelRatio || 1,
                    autoDensity: true
                });

                // 启用Live2D插件
                if (window.PIXI && window.PIXI.live2d && window.PIXI.live2d.Live2DModel) {
                    window.PIXI.live2d.Live2DModel.registerTicker(PIXI.Ticker);
                    console.log('✅ Live2D插件已启用');
                } else {
                    console.warn('⚠️ Live2D插件未找到，将使用占位符模式');
                }

                // 响应窗口大小变化
                window.addEventListener('resize', () => {
                    this.app.renderer.resize(window.innerWidth, window.innerHeight);
                    this.adjustModelScale();
                });

                console.log('✅ PIXI应用初始化完成');
            }

            async setupWebChannel() {
                return new Promise((resolve, reject) => {
                    if (typeof qt !== 'undefined' && qt.webChannelTransport) {
                        new QWebChannel(qt.webChannelTransport, (channel) => {
                            this.bridge = channel.objects.live2dBridge;
                            
                            if (this.bridge) {
                                // 注册回调函数
                                this.bridge.modelLoadRequested.connect(this.loadModel.bind(this));
                                this.bridge.expressionChangeRequested.connect(this.setExpression.bind(this));
                                this.bridge.motionPlayRequested.connect(this.playMotion.bind(this));
                                this.bridge.debugModeChanged.connect(this.toggleDebug.bind(this));
                                
                                // 通知Qt端已准备就绪
                                this.bridge.notifyReady();
                                
                                console.log('✅ WebChannel连接成功');
                                resolve();
                            } else {
                                reject(new Error('无法获取Live2D桥接对象'));
                            }
                        });
                    } else {
                        // 开发模式，无WebChannel
                        console.log('⚠️ 开发模式：无WebChannel连接');
                        resolve();
                    }
                });
            }

            async loadDefaultModel() {
                console.log('📦 加载默认Live2D模型...');

                try {
                    // 检查是否有Live2D库支持
                    if (!window.PIXI || !window.PIXI.live2d || !window.PIXI.live2d.Live2DModel) {
                        console.warn('⚠️ Live2D库未加载，使用占位符模式');
                        this.showPlaceholder();
                        this.hideLoading();

                        this.isReady = true;
                        if (this.bridge) {
                            this.bridge.notifyReady();
                        }
                        return;
                    }

                    console.log('✅ Live2D库已加载，尝试加载真实模型');

                    // 获取可用模型列表
                    const availableModels = this.modelManager.getAvailableModels();

                    if (availableModels.length === 0) {
                        console.warn('⚠️ 没有找到可用的Live2D模型，显示占位符');
                        this.showPlaceholder();
                        this.hideLoading();

                        this.isReady = true;
                        if (this.bridge) {
                            this.bridge.notifyReady();
                        }
                        return;
                    }

                    // 加载第一个可用模型（默认为甘雨）
                    const defaultModelName = 'ganyu';
                    const fallbackModel = availableModels[0].name;
                    const modelToLoad = availableModels.find(m => m.name === defaultModelName) ? defaultModelName : fallbackModel;

                    console.log(`🎭 加载模型: ${modelToLoad}`);

                    // 尝试使用模型管理器加载模型
                    try {
                        this.model = await this.modelManager.switchModel(modelToLoad);
                    } catch (modelError) {
                        console.warn('⚠️ 真实模型加载失败，使用占位符:', modelError);
                        this.showPlaceholder();
                        this.hideLoading();

                        this.isReady = true;
                        if (this.bridge) {
                            this.bridge.notifyReady();
                        }
                        return;
                    }

                    // 设置模型属性
                    this.setupModel(this.model);

                    // 添加到舞台
                    this.app.stage.addChild(this.model);

                    // 调整模型缩放和位置
                    this.adjustModelScale();

                    this.isReady = true;
                    this.hideLoading();

                    if (this.bridge) {
                        this.bridge.notifyReady();
                        this.bridge.notifyModelLoaded(modelToLoad);
                    }

                    console.log('✅ 默认模型加载完成');

                } catch (error) {
                    console.error('❌ 模型加载失败:', error);
                    this.showError('模型加载失败: ' + error.message);

                    // 降级到占位符
                    this.showPlaceholder();
                    this.hideLoading();

                    this.isReady = true;
                    if (this.bridge) {
                        this.bridge.notifyReady();
                    }
                }
            }

            setupModel(model) {
                if (!model) return;

                // 设置模型交互
                model.interactive = true;
                model.buttonMode = true;

                // 添加点击事件
                model.on('pointerdown', () => {
                    this.onModelClick();
                });

                // 添加悬停事件
                model.on('pointerover', () => {
                    if (this.bridge) {
                        this.bridge.notifyModelHovered(true);
                    }
                });

                model.on('pointerout', () => {
                    if (this.bridge) {
                        this.bridge.notifyModelHovered(false);
                    }
                });

                console.log('✅ 模型设置完成');
            }

            adjustModelScale() {
                if (!this.model) return;

                const canvas = this.app.view;
                const canvasWidth = canvas.width;
                const canvasHeight = canvas.height;

                // 计算合适的缩放比例
                const modelBounds = this.model.getBounds();
                const scaleX = canvasWidth * 0.8 / modelBounds.width;
                const scaleY = canvasHeight * 0.8 / modelBounds.height;
                const scale = Math.min(scaleX, scaleY);

                // 应用缩放
                this.model.scale.set(scale);

                // 居中显示
                this.model.x = canvasWidth / 2;
                this.model.y = canvasHeight / 2;

                console.log(`📏 模型缩放调整: ${scale.toFixed(2)}`);
            }

            showPlaceholder() {
                // 创建一个简单的占位符图形
                const graphics = new PIXI.Graphics();
                graphics.beginFill(0x3498db, 0.3);
                graphics.drawRoundedRect(0, 0, 200, 300, 20);
                graphics.endFill();
                
                graphics.beginFill(0x2c3e50);
                graphics.drawCircle(100, 80, 30); // 头部
                graphics.endFill();
                
                graphics.beginFill(0xe74c3c);
                graphics.drawCircle(85, 75, 5); // 左眼
                graphics.drawCircle(115, 75, 5); // 右眼
                graphics.endFill();
                
                graphics.beginFill(0xe67e22);
                graphics.drawRoundedRect(85, 90, 30, 8, 4); // 嘴巴
                graphics.endFill();
                
                // 居中显示
                graphics.x = (this.app.screen.width - 200) / 2;
                graphics.y = (this.app.screen.height - 300) / 2;
                
                // 添加交互
                graphics.interactive = true;
                graphics.buttonMode = true;
                graphics.on('pointerdown', () => {
                    this.onModelClick();
                });
                
                this.app.stage.addChild(graphics);
                this.model = graphics;
                
                console.log('✅ 占位符模型已加载');
            }

            async loadModel(modelPath) {
                try {
                    console.log('🎭 加载Live2D模型:', modelPath);
                    
                    // 这里实现真正的Live2D模型加载
                    // 使用Live2D Web SDK加载.model3.json文件
                    
                    this.showLoading();
                    
                    // 模拟加载过程
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    
                    this.hideLoading();
                    this.currentExpression = '默认';
                    this.currentMotion = '待机';
                    
                    // 通知Qt端加载完成
                    if (this.bridge) {
                        this.bridge.notifyModelLoaded(modelPath);
                    }
                    
                    console.log('✅ 模型加载完成');
                    
                } catch (error) {
                    console.error('❌ 模型加载失败:', error);
                    this.showError('模型加载失败: ' + error.message);
                }
            }

            setExpression(expressionName, intensity = 1.0) {
                console.log(`🎭 设置表情: ${expressionName}, 强度: ${intensity}`);

                this.currentExpression = expressionName;

                // 真实Live2D表情控制（当有真实模型时）
                if (this.model && this.model.internalModel) {
                    try {
                        // 使用Live2D表情管理器
                        const expressionManager = this.model.internalModel.motionManager.expressionManager;
                        if (expressionManager) {
                            expressionManager.setExpression(expressionName);
                        }

                        // 或者直接设置参数
                        const coreModel = this.model.internalModel.coreModel;
                        if (coreModel) {
                            // 根据表情名称设置对应参数
                            this.setExpressionParameters(coreModel, expressionName, intensity);
                        }
                    } catch (error) {
                        console.warn('Live2D表情设置失败:', error);
                    }
                }

                // 占位符模型的视觉效果
                if (this.model && this.model instanceof PIXI.Graphics) {
                    const colors = {
                        '开心': 0xf1c40f,
                        '难过': 0x3498db,
                        '生气': 0xe74c3c,
                        '惊讶': 0x9b59b6,
                        '默认': 0x3498db
                    };

                    const color = colors[expressionName] || colors['默认'];
                    this.model.tint = color;
                }

                // 通知Qt端表情已变化
                if (this.bridge) {
                    this.bridge.notifyExpressionChanged(expressionName, intensity);
                }
            }

            setExpressionParameters(coreModel, expressionName, intensity) {
                // Live2D标准表情参数映射
                const expressionParams = {
                    '开心': {
                        'ParamEyeLOpen': 1.0,
                        'ParamEyeROpen': 1.0,
                        'ParamMouthForm': 1.0,
                        'ParamMouthOpenY': 0.3
                    },
                    '难过': {
                        'ParamEyeLOpen': 0.3,
                        'ParamEyeROpen': 0.3,
                        'ParamMouthForm': -0.8,
                        'ParamBrowLY': -0.5,
                        'ParamBrowRY': -0.5
                    },
                    '生气': {
                        'ParamBrowLY': -1.0,
                        'ParamBrowRY': -1.0,
                        'ParamMouthForm': -0.5
                    },
                    '惊讶': {
                        'ParamEyeLOpen': 1.5,
                        'ParamEyeROpen': 1.5,
                        'ParamMouthOpenY': 0.8,
                        'ParamBrowLY': 0.5,
                        'ParamBrowRY': 0.5
                    }
                };

                const params = expressionParams[expressionName];
                if (params) {
                    Object.entries(params).forEach(([paramId, value]) => {
                        try {
                            coreModel.setParameterValueById(paramId, value * intensity);
                        } catch (error) {
                            console.warn(`参数设置失败: ${paramId}`, error);
                        }
                    });
                }
            }

            playMotion(motionName, priority = 1) {
                console.log(`🎬 播放动作: ${motionName}, 优先级: ${priority}`);
                
                this.currentMotion = motionName;
                
                // 这里实现动作播放的视觉效果
                if (this.model) {
                    // 简单的缩放动画示例
                    const originalScale = this.model.scale.x;
                    this.model.scale.set(originalScale * 1.1);
                    
                    setTimeout(() => {
                        if (this.model) {
                            this.model.scale.set(originalScale);
                        }
                    }, 300);
                }
                
                // 通知Qt端动作已开始
                if (this.bridge) {
                    this.bridge.notifyMotionStarted(motionName);
                }
            }

            onModelClick() {
                console.log('🖱️ 模型被点击');
                
                // 随机播放表情
                const expressions = ['开心', '难过', '生气', '惊讶', '默认'];
                const randomExpression = expressions[Math.floor(Math.random() * expressions.length)];
                this.setExpression(randomExpression);
                
                // 通知Qt端模型被点击
                if (this.bridge) {
                    this.bridge.notifyModelClicked();
                }
            }

            startRenderLoop() {
                const updateFPS = () => {
                    // 使用工具类的FPS监控器
                    this.fps = this.fpsMonitor.update();

                    // 更新调试面板
                    this.updateDebugPanel();
                };

                this.app.ticker.add(updateFPS);
                console.log('✅ 渲染循环已启动');
            }

            toggleDebug(enabled) {
                const debugPanel = document.getElementById('debug-panel');
                debugPanel.style.display = enabled ? 'block' : 'none';
            }

            updateDebugPanel() {
                document.getElementById('fps').textContent = this.fps;
                document.getElementById('current-expression').textContent = this.currentExpression;
                document.getElementById('current-motion').textContent = this.currentMotion;

                // 显示内存使用情况（每5秒一次）
                if (this.fps % 5 === 0) {
                    const memoryUsage = Live2DUtils.performance.getMemoryUsage();
                    if (memoryUsage) {
                        console.log(`💾 内存使用: ${memoryUsage.used}MB / ${memoryUsage.total}MB`);
                    }
                }
            }

            async switchModel(modelName) {
                console.log(`🔄 切换模型: ${modelName}`);

                try {
                    this.showLoading();

                    // 移除当前模型
                    if (this.model && this.app.stage.children.includes(this.model)) {
                        this.app.stage.removeChild(this.model);
                    }

                    // 使用模型管理器切换模型
                    this.model = await this.modelManager.switchModel(modelName);

                    if (this.model) {
                        // 设置新模型
                        this.setupModel(this.model);

                        // 添加到舞台
                        this.app.stage.addChild(this.model);

                        // 调整缩放和位置
                        this.adjustModelScale();

                        // 通知Qt端模型已切换
                        if (this.bridge) {
                            this.bridge.notifyModelLoaded(modelName);
                        }

                        console.log(`✅ 模型切换成功: ${modelName}`);
                    } else {
                        throw new Error('模型加载失败');
                    }

                } catch (error) {
                    console.error('❌ 模型切换失败:', error);
                    this.showError('模型切换失败: ' + error.message);
                } finally {
                    this.hideLoading();
                }
            }

            showLoading() {
                document.getElementById('loading').style.display = 'block';
            }

            hideLoading() {
                document.getElementById('loading').style.display = 'none';
            }

            showError(message) {
                const loading = document.getElementById('loading');
                loading.textContent = '❌ ' + message;
                loading.style.color = '#e74c3c';
                loading.style.display = 'block';
            }

            // ===== 口型同步功能 =====

            startLipSync(mouthData) {
                console.log('🎤 开始口型同步，数据点数:', mouthData.length);

                this.mouthSyncData = mouthData;
                this.stopLipSync(); // 停止之前的同步

                let dataIndex = 0;
                const startTime = performance.now();

                this.mouthSyncTimer = setInterval(() => {
                    const currentTime = performance.now() - startTime;

                    // 找到当前时间对应的口型数据
                    while (dataIndex < this.mouthSyncData.length &&
                           this.mouthSyncData[dataIndex].timestamp <= currentTime) {

                        const mouthValue = this.mouthSyncData[dataIndex].value;
                        this.setMouthOpening(mouthValue);
                        dataIndex++;
                    }

                    // 如果数据播放完毕，停止同步
                    if (dataIndex >= this.mouthSyncData.length) {
                        this.stopLipSync();
                    }
                }, 16); // 约60FPS更新频率
            }

            stopLipSync() {
                if (this.mouthSyncTimer) {
                    clearInterval(this.mouthSyncTimer);
                    this.mouthSyncTimer = null;
                }

                // 重置口型
                this.setMouthOpening(0);
                console.log('🎤 口型同步已停止');
            }

            setMouthOpening(value) {
                this.currentMouthValue = Math.max(0, Math.min(1, value));

                // 真实Live2D模型的口型控制
                if (this.model && this.model.internalModel) {
                    try {
                        const coreModel = this.model.internalModel.coreModel;
                        if (coreModel) {
                            // 设置嘴巴开合度
                            coreModel.setParameterValueById('ParamMouthOpenY', this.currentMouthValue);

                            // 可选：根据开合度调整嘴型
                            const mouthForm = this.currentMouthValue > 0.5 ? 0.5 : 0;
                            coreModel.setParameterValueById('ParamMouthForm', mouthForm);
                        }
                    } catch (error) {
                        console.warn('Live2D口型设置失败:', error);
                    }
                }

                // 占位符模型的视觉反馈
                if (this.model && this.model instanceof PIXI.Graphics) {
                    // 通过缩放模拟口型变化
                    const scaleY = 1 + this.currentMouthValue * 0.1;
                    this.model.scale.y = scaleY;
                }
            }
        }

        // 启动Live2D管理器
        window.live2dManager = new Live2DManager();
        
        // 全局函数供Qt调用
        window.setExpression = (name, intensity) => window.live2dManager.setExpression(name, intensity);
        window.playMotion = (name, priority) => window.live2dManager.playMotion(name, priority);
        window.loadModel = (path) => window.live2dManager.loadModel(path);
        window.switchModel = (modelName) => window.live2dManager.switchModel(modelName);
        window.getAvailableModels = () => window.live2dManager.modelManager.getAvailableModels();
        window.startLipSync = (mouthData) => window.live2dManager.startLipSync(mouthData);
        window.stopLipSync = () => window.live2dManager.stopLipSync();
        window.setMouthOpening = (value) => window.live2dManager.setMouthOpening(value);
        
    </script>
</body>
</html>
