#!/usr/bin/env python3
"""
Live2D WebEngine方案技术验证测试
验证透明背景、鼠标事件、口型同步、表情动作等核心功能
"""

import sys
import json
import time
import asyncio
from pathlib import Path
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout, QLabel, QSlider
from PySide6.QtCore import Qt, QTimer, QUrl
from PySide6.QtGui import QColor
from PySide6.QtWebEngineWidgets import QWebEngineView
from PySide6.QtWebChannel import QWebChannel

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from aipet.ui.controllers.live2d_bridge import Live2DBridge


class Live2DTestWindow(QMainWindow):
    """Live2D技术验证窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Live2D WebEngine技术验证")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建Live2D桥接器
        self.live2d_bridge = Live2DBridge()
        
        # 创建WebChannel
        self.web_channel = QWebChannel()
        self.web_channel.registerObject('live2dBridge', self.live2d_bridge)
        
        self.setup_ui()
        self.setup_connections()
        
        print("🎭 Live2D技术验证窗口初始化完成")

    def setup_ui(self):
        """设置UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 控制面板
        control_panel = self.create_control_panel()
        layout.addWidget(control_panel)
        
        # Live2D显示区域
        self.live2d_view = QWebEngineView()
        self.live2d_view.setMinimumHeight(400)
        
        # 设置WebChannel
        self.live2d_view.page().setWebChannel(self.web_channel)
        
        # 加载Live2D页面
        html_path = Path(__file__).parent / "aipet" / "ui" / "web" / "live2d_viewer.html"
        if html_path.exists():
            self.live2d_view.load(QUrl.fromLocalFile(str(html_path.absolute())))
        else:
            print(f"❌ Live2D HTML文件不存在: {html_path}")
        
        layout.addWidget(self.live2d_view)
        
        # 状态显示
        self.status_label = QLabel("状态: 初始化中...")
        layout.addWidget(self.status_label)

    def create_control_panel(self):
        """创建控制面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 表情控制
        expression_layout = QHBoxLayout()
        expression_layout.addWidget(QLabel("表情测试:"))
        
        expressions = ["默认", "开心", "难过", "生气", "惊讶"]
        for expr in expressions:
            btn = QPushButton(expr)
            btn.clicked.connect(lambda checked, e=expr: self.test_expression(e))
            expression_layout.addWidget(btn)
        
        layout.addLayout(expression_layout)
        
        # 动作控制
        motion_layout = QHBoxLayout()
        motion_layout.addWidget(QLabel("动作测试:"))
        
        motions = ["待机", "挥手", "点头", "摇头", "鞠躬"]
        for motion in motions:
            btn = QPushButton(motion)
            btn.clicked.connect(lambda checked, m=motion: self.test_motion(m))
            motion_layout.addWidget(btn)
        
        layout.addLayout(motion_layout)
        
        # 口型同步控制
        lipsync_layout = QHBoxLayout()
        lipsync_layout.addWidget(QLabel("口型测试:"))
        
        # 口型开合度滑块
        self.mouth_slider = QSlider(Qt.Horizontal)
        self.mouth_slider.setRange(0, 100)
        self.mouth_slider.setValue(0)
        self.mouth_slider.valueChanged.connect(self.test_mouth_opening)
        lipsync_layout.addWidget(self.mouth_slider)
        
        # 口型同步按钮
        lipsync_btn = QPushButton("测试口型同步")
        lipsync_btn.clicked.connect(self.test_lipsync)
        lipsync_layout.addWidget(lipsync_btn)
        
        layout.addLayout(lipsync_layout)
        
        # 透明度测试
        transparency_layout = QHBoxLayout()
        transparency_layout.addWidget(QLabel("透明度测试:"))
        
        transparent_btn = QPushButton("切换透明背景")
        transparent_btn.clicked.connect(self.test_transparency)
        transparency_layout.addWidget(transparent_btn)
        
        mouse_btn = QPushButton("测试鼠标穿透")
        mouse_btn.clicked.connect(self.test_mouse_events)
        transparency_layout.addWidget(mouse_btn)
        
        layout.addLayout(transparency_layout)
        
        return panel

    def setup_connections(self):
        """设置信号连接"""
        # Live2D状态信号
        self.live2d_bridge.ready.connect(self.on_live2d_ready)
        self.live2d_bridge.modelLoaded.connect(self.on_model_loaded)
        self.live2d_bridge.expressionChanged.connect(self.on_expression_changed)
        self.live2d_bridge.motionStarted.connect(self.on_motion_started)
        self.live2d_bridge.modelClicked.connect(self.on_model_clicked)
        self.live2d_bridge.errorOccurred.connect(self.on_error)
        
        # WebEngine加载完成
        self.live2d_view.loadFinished.connect(self.on_page_loaded)

    def on_live2d_ready(self):
        """Live2D准备就绪"""
        self.status_label.setText("状态: Live2D已准备就绪 ✅")
        print("✅ Live2D组件已准备就绪")

    def on_model_loaded(self, model_path):
        """模型加载完成"""
        self.status_label.setText(f"状态: 模型已加载 - {model_path}")
        print(f"✅ Live2D模型已加载: {model_path}")

    def on_expression_changed(self, name, intensity):
        """表情变化"""
        print(f"🎭 表情已变化: {name} (强度: {intensity})")

    def on_motion_started(self, name):
        """动作开始"""
        print(f"🎬 动作已开始: {name}")

    def on_model_clicked(self):
        """模型被点击"""
        print("🖱️ Live2D模型被点击")
        self.status_label.setText("状态: 模型被点击 🖱️")

    def on_error(self, error):
        """错误发生"""
        print(f"❌ Live2D错误: {error}")
        self.status_label.setText(f"状态: 错误 - {error}")

    def on_page_loaded(self, success):
        """页面加载完成"""
        if success:
            print("✅ Live2D页面加载成功")
            self.status_label.setText("状态: 页面已加载")
        else:
            print("❌ Live2D页面加载失败")
            self.status_label.setText("状态: 页面加载失败")

    # ===== 测试方法 =====

    def test_expression(self, expression_name):
        """测试表情"""
        print(f"🧪 测试表情: {expression_name}")
        self.live2d_bridge.setExpression(expression_name, 1.0)

    def test_motion(self, motion_name):
        """测试动作"""
        print(f"🧪 测试动作: {motion_name}")
        self.live2d_bridge.playMotion(motion_name, 1)

    def test_mouth_opening(self, value):
        """测试口型开合度"""
        normalized_value = value / 100.0
        print(f"🧪 测试口型开合度: {normalized_value}")
        self.live2d_bridge.setMouthOpening(normalized_value)

    def test_lipsync(self):
        """测试口型同步"""
        print("🧪 测试口型同步")
        
        # 生成测试口型数据（模拟说话）
        mouth_data = []
        duration = 3000  # 3秒
        
        for i in range(0, duration, 50):  # 每50ms一个数据点
            # 生成波浪形口型数据
            import math
            value = (math.sin(i / 200) + 1) / 2 * 0.8  # 0-0.8范围
            mouth_data.append({
                "timestamp": i,
                "value": value
            })
        
        self.live2d_bridge.startLipSync(mouth_data)
        
        # 3秒后停止
        QTimer.singleShot(duration + 500, self.live2d_bridge.stopLipSync)

    def test_transparency(self):
        """测试透明背景"""
        print("🧪 测试透明背景")
        
        # 切换WebEngine背景色
        current_color = self.live2d_view.page().backgroundColor()
        if current_color.alpha() == 0:
            # 当前透明，设为不透明
            self.live2d_view.page().setBackgroundColor(QColor(255, 255, 255, 255))
            print("设置为不透明背景")
        else:
            # 当前不透明，设为透明
            self.live2d_view.page().setBackgroundColor(QColor(0, 0, 0, 0))
            print("设置为透明背景")

    def test_mouse_events(self):
        """测试鼠标事件"""
        print("🧪 测试鼠标事件")
        print("请点击Live2D模型区域测试鼠标交互")
        self.status_label.setText("状态: 请点击Live2D模型测试鼠标事件")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置WebEngine环境
    import os
    os.environ['QTWEBENGINE_CHROMIUM_FLAGS'] = '--disable-web-security --allow-running-insecure-content'
    
    window = Live2DTestWindow()
    window.show()
    
    print("🚀 Live2D技术验证程序启动")
    print("=" * 50)
    print("测试项目:")
    print("1. ✅ 透明背景支持")
    print("2. ✅ 表情切换功能") 
    print("3. ✅ 动作播放功能")
    print("4. ✅ 口型同步功能")
    print("5. ✅ 鼠标交互事件")
    print("6. ✅ WebEngine集成")
    print("=" * 50)
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
