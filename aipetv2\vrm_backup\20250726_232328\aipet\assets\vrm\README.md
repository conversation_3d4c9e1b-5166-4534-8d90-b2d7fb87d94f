# VRM资源目录结构说明

## 📁 目录结构

```
vrm/
├── renderer/           # VRM渲染器文件
│   ├── enhanced_vrm_renderer.html    # 主渲染页面
│   ├── enhanced_vrm_renderer.js      # 渲染逻辑
│   ├── universal_vrm_loader.js       # 通用VRM加载器
│   ├── layered_mouse_tracking.js     # 分层鼠标跟踪
│   ├── audio_monitor.js              # 音频监控器
│   └── lip_sync_analyzer.js          # 口型分析器
├── libs/               # JavaScript库文件
│   ├── three.min.js                  # Three.js核心库
│   ├── three-vrm.min.js              # VRM 1.0加载器
│   └── three-vrm-v0.min.js           # VRM 0.x加载器
├── models/             # VRM模型文件
│   ├── Zome.vrm                      # 主要测试模型(VRM 0.x)
│   ├── Alice.vrm                     # VRM 1.0兼容测试
│   ├── aldina.vrm                    # 备选测试模型
│   ├── model_configs.json            # 模型配置
│   └── validate_vrm_models.py        # 模型验证脚本
├── shaders/            # 自定义着色器
│   └── (待添加自定义着色器文件)
├── animations/         # 动画文件
│   ├── dance_animations.json         # 舞蹈动画配置
│   └── idle_animations.json          # 待机动画配置
├── configs/            # VRM配置文件
│   ├── expression_presets.json       # 表情预设
│   └── render_settings.json          # 渲染设置
└── README.md           # 本说明文件
```

## 🎯 各目录用途

### renderer/
存放VRM渲染相关的HTML和JavaScript文件，包括：
- 主渲染页面和渲染逻辑
- VRM加载器和版本兼容处理
- 鼠标跟踪和交互处理
- 音频监控和口型同步

### libs/
存放第三方JavaScript库文件：
- Three.js: 3D渲染引擎
- @pixiv/three-vrm: VRM格式支持库
- 其他依赖库

### models/
存放VRM模型文件和相关配置：
- 已验证的VRM模型文件
- 模型配置和元数据
- 模型验证工具

### shaders/
存放自定义着色器文件（如需要）

### animations/
存放动画配置文件：
- 舞蹈动画定义
- 待机动画定义
- 其他动画预设

### configs/
存放VRM系统配置文件：
- 表情映射配置
- 渲染参数设置
- 其他系统配置

## 📋 当前状态

- ✅ 目录结构已创建
- ✅ VRM模型文件已就绪 (3个模型，100%验证通过)
- ⏳ 待添加JavaScript库文件
- ⏳ 待创建渲染器文件
- ⏳ 待添加配置文件

## 🚀 下一步

1. 下载和配置Three.js依赖库
2. 创建基础VRM渲染器
3. 实现独立透明窗口
4. 建立Qt-JS通信桥接
