{"vrm_renderer": {"version": "1.0.0", "description": "VRM渲染器配置文件", "window": {"width": 400, "height": 600, "position": "auto", "always_on_top": true, "transparent_background": true, "mouse_transparent": false, "enable_resize": false, "enable_drag": true}, "rendering": {"enable_shadows": true, "enable_antialiasing": true, "pixel_ratio": "auto", "background_color": "#000000", "background_alpha": 0.0, "tone_mapping": "ACESFilmic", "tone_mapping_exposure": 1.0}, "camera": {"fov": 30, "near": 0.1, "far": 20.0, "position": {"x": 0, "y": 1.4, "z": 1.5}, "look_at": {"x": 0, "y": 1.4, "z": 0}}, "lighting": {"directional_light": {"color": "#ffffff", "intensity": 1.0, "position": {"x": 1, "y": 1, "z": 1}, "cast_shadow": true}, "ambient_light": {"color": "#ffffff", "intensity": 0.4}, "fill_light": {"color": "#ffffff", "intensity": 0.3, "position": {"x": -1, "y": -1, "z": 1}}}, "model": {"default_scale": 1.0, "default_position": {"x": 0, "y": 0, "z": 0}, "enable_shadows": true, "auto_center": true}, "mouse_tracking": {"enabled": true, "smoothing_factor": 0.1, "sensitivity": 1.0, "tracking_bounds": {"left": -1.0, "right": 1.0, "top": 1.0, "bottom": -1.0}, "dead_zone": {"enabled": true, "radius": 0.1}, "layers": {"head": {"enabled": true, "sensitivity": 1.0, "max_angle": 0.5236, "smoothing": 0.15}, "eyes": {"enabled": true, "sensitivity": 1.2, "max_angle": 0.7854, "smoothing": 0.2}, "body": {"enabled": false, "sensitivity": 0.3, "max_angle": 0.2618, "smoothing": 0.05}}}, "expressions": {"default_expression": "neutral", "transition_time": 0.5, "auto_blink": {"enabled": true, "interval_min": 2.0, "interval_max": 6.0, "duration": 0.15}, "auto_breath": {"enabled": true, "intensity": 0.02, "frequency": 0.3}, "presets": {"neutral": {"weight": 1.0, "description": "默认中性表情"}, "happy": {"weight": 1.0, "description": "开心表情"}, "sad": {"weight": 1.0, "description": "悲伤表情"}, "surprised": {"weight": 1.0, "description": "惊讶表情"}, "angry": {"weight": 1.0, "description": "愤怒表情"}, "thinking": {"weight": 0.8, "description": "思考表情"}, "confused": {"weight": 0.9, "description": "困惑表情"}}}, "animations": {"idle_animations": [{"name": "idle_breath", "enabled": true, "loop": true, "weight": 0.5}, {"name": "idle_sway", "enabled": false, "loop": true, "weight": 0.3}], "gesture_animations": [{"name": "wave", "trigger": "greeting", "duration": 2.0, "loop": false}, {"name": "nod", "trigger": "agreement", "duration": 1.0, "loop": false}, {"name": "shake_head", "trigger": "disagreement", "duration": 1.5, "loop": false}]}, "performance": {"target_fps": 60, "enable_fps_limit": true, "enable_performance_monitor": true, "auto_quality_adjustment": true, "quality_levels": {"high": {"shadow_map_size": 2048, "antialiasing": true, "pixel_ratio": 2.0}, "medium": {"shadow_map_size": 1024, "antialiasing": true, "pixel_ratio": 1.5}, "low": {"shadow_map_size": 512, "antialiasing": false, "pixel_ratio": 1.0}}}, "debug": {"enable_debug_info": false, "enable_console_logging": true, "enable_performance_stats": false, "enable_wireframe": false, "enable_helper_objects": false, "log_level": "info"}, "models": {"search_paths": ["../models/", "./models/", "../../models/"], "supported_formats": [".vrm"], "default_models": ["Alice.vrm", "Zome.vrm", "aldina.vrm"], "model_cache": {"enabled": true, "max_size": 100, "cleanup_interval": 300}}, "integration": {"tts_sync": {"enabled": true, "lip_sync_intensity": 1.0, "viseme_mapping": "auto"}, "agent_status": {"enabled": true, "expression_mapping": {"thinking": "thinking", "working": "focused", "completed": "happy", "error": "confused"}}, "workflow_visualization": {"enabled": true, "progress_gestures": true, "status_expressions": true}}}}