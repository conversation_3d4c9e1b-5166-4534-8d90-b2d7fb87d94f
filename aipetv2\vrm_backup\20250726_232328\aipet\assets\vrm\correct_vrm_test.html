<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>正确的VRM测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #2c3e50;
            color: white;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .controls {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .file-input {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .canvas-container {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        canvas {
            border: 2px solid #3498db;
            border-radius: 4px;
            max-width: 100%;
        }
        
        #log {
            background: #1a1a1a;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Consolas', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .drop-zone {
            border: 2px dashed #3498db;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin: 10px 0;
            cursor: pointer;
        }
        
        .drop-zone.dragover {
            border-color: #e74c3c;
            background: rgba(231, 76, 60, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎭 正确的VRM测试</h1>
        <p>使用与参考项目相同的库版本和配置</p>
        
        <div class="controls">
            <input type="file" id="file-input" accept=".vrm" class="file-input">
            <button onclick="clearLog()" class="file-input">清空日志</button>
            <button onclick="testLibraries()" class="file-input">测试库</button>
            <button onclick="loadDefaultModel()" class="file-input">加载默认模型</button>
            
            <div id="drop-zone" class="drop-zone">
                将VRM文件拖拽到这里，或点击上方按钮选择文件
            </div>
        </div>
        
        <div class="canvas-container">
            <canvas id="vrm-canvas" width="800" height="600"></canvas>
        </div>
        
        <div>
            <h3>📝 调试日志</h3>
            <div id="log"></div>
        </div>
    </div>

    <!-- Import Map: 使用本地下载的库文件 -->
    <script type="importmap">
    {
      "imports": {
        "three": "./node_modules/three/build/three.module.js",
        "three/addons/": "./node_modules/three/examples/jsm/",
        "@pixiv/three-vrm": "./node_modules/@pixiv/three-vrm/lib/three-vrm.module.js"
      }
    }
    </script>

    <script type="module">
        console.log('开始加载VRM模块...');
        
        // 导入所需的模块
        import * as THREE from 'three';
        import { GLTFLoader } from 'three/addons/loaders/GLTFLoader.js';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        import { VRMLoaderPlugin, VRMUtils } from '@pixiv/three-vrm';

        console.log('所有模块导入完成');

        // 全局变量
        let scene, camera, renderer, controls, currentVRM;
        let loader;

        // 日志函数
        function log(message) {
            console.log(`[VRM Test] ${message}`);
            
            const logDiv = document.getElementById('log');
            if (logDiv) {
                const timestamp = new Date().toLocaleTimeString();
                logDiv.textContent += `[${timestamp}] ${message}\n`;
                logDiv.scrollTop = logDiv.scrollHeight;
            }
        }

        function clearLog() {
            const logDiv = document.getElementById('log');
            if (logDiv) {
                logDiv.textContent = '';
            }
        }

        function testLibraries() {
            log('=== 库测试 ===');
            log(`THREE版本: ${THREE.REVISION}`);
            log(`GLTFLoader: ${typeof GLTFLoader}`);
            log(`VRMLoaderPlugin: ${typeof VRMLoaderPlugin}`);
            log(`VRMUtils: ${typeof VRMUtils}`);
            
            // 测试创建实例
            try {
                const testLoader = new GLTFLoader();
                log('✅ GLTFLoader实例创建成功');
                
                const testPlugin = new VRMLoaderPlugin();
                log('✅ VRMLoaderPlugin实例创建成功');
                
                log('🎉 所有库测试通过！');
            } catch (error) {
                log(`❌ 库测试失败: ${error.message}`);
            }
        }

        // 初始化3D环境
        function init3D() {
            log('初始化3D环境...');
            
            try {
                const canvas = document.getElementById('vrm-canvas');
                
                // 创建场景
                scene = new THREE.Scene();
                scene.background = new THREE.Color(0x34495e);
                
                // 创建相机
                camera = new THREE.PerspectiveCamera(30, canvas.width / canvas.height, 0.1, 20);
                camera.position.set(0, 1.4, 1.5);
                
                // 创建渲染器
                renderer = new THREE.WebGLRenderer({ 
                    canvas: canvas, 
                    antialias: true 
                });
                renderer.setSize(canvas.width, canvas.height);
                renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
                renderer.shadowMap.enabled = true;
                renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                
                // 添加轨道控制器
                controls = new OrbitControls(camera, renderer.domElement);
                controls.enableDamping = true;
                controls.target.set(0, 1.4, 0);
                
                // 添加光照
                const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
                directionalLight.position.set(1, 1, 1);
                directionalLight.castShadow = true;
                scene.add(directionalLight);
                
                const ambientLight = new THREE.AmbientLight(0xffffff, 0.3);
                scene.add(ambientLight);
                
                // 创建VRM加载器（使用正确的配置）
                loader = new GLTFLoader();
                loader.crossOrigin = 'anonymous';
                
                // 注册VRM插件
                loader.register((parser) => {
                    return new VRMLoaderPlugin(parser);
                });
                
                log('✅ 3D环境初始化完成');
                
                // 开始渲染循环
                animate();
                
            } catch (error) {
                log(`❌ 3D环境初始化失败: ${error.message}`);
                console.error('3D初始化错误:', error);
            }
        }

        // 渲染循环
        function animate() {
            requestAnimationFrame(animate);
            
            // 更新控制器
            if (controls) {
                controls.update();
            }
            
            // 更新VRM
            if (currentVRM && currentVRM.update) {
                const deltaTime = 0.016; // 假设60fps
                currentVRM.update(deltaTime);
            }
            
            if (renderer && scene && camera) {
                renderer.render(scene, camera);
            }
        }

        // 加载默认模型
        function loadDefaultModel() {
            // 尝试加载models目录中的第一个VRM文件
            const defaultModels = ['models/Alice.vrm', 'models/Zome.vrm', 'models/aldina.vrm'];
            
            for (const modelPath of defaultModels) {
                log(`尝试加载默认模型: ${modelPath}`);
                loadVRMFromURL(modelPath);
                break; // 只加载第一个
            }
        }

        // 从URL加载VRM
        function loadVRMFromURL(url) {
            log(`开始加载VRM模型: ${url}`);
            
            if (!loader) {
                log('❌ 加载器未初始化');
                return;
            }
            
            loader.load(
                url,
                (gltf) => {
                    log('✅ GLTF加载成功');
                    
                    const vrm = gltf.userData.vrm;
                    if (vrm) {
                        log('✅ VRM数据解析成功');
                        
                        // 应用VRM优化（参考项目的做法）
                        VRMUtils.removeUnnecessaryVertices(gltf.scene);
                        VRMUtils.combineSkeletons(gltf.scene);
                        VRMUtils.combineMorphs(vrm);
                        
                        // 禁用视锥体剔除
                        vrm.scene.traverse((obj) => {
                            obj.frustumCulled = false;
                        });
                        
                        // 清除之前的模型
                        if (currentVRM) {
                            scene.remove(currentVRM.scene);
                            if (currentVRM.dispose) {
                                currentVRM.dispose();
                            }
                        }
                        
                        // 添加新模型
                        scene.add(vrm.scene);
                        currentVRM = vrm;
                        
                        // 调整相机位置
                        const box = new THREE.Box3().setFromObject(vrm.scene);
                        const center = box.getCenter(new THREE.Vector3());
                        const size = box.getSize(new THREE.Vector3());
                        
                        const maxDim = Math.max(size.x, size.y, size.z);
                        const fov = camera.fov * (Math.PI / 180);
                        let cameraZ = Math.abs(maxDim / 2 / Math.tan(fov / 2));
                        cameraZ *= 1.5;
                        
                        camera.position.set(0, center.y, center.z + cameraZ);
                        controls.target.copy(center);
                        controls.update();
                        
                        log(`🎉 VRM模型加载完成！`);
                        log(`模型信息: ${size.x.toFixed(2)} x ${size.y.toFixed(2)} x ${size.z.toFixed(2)}`);
                        log(`场景对象数: ${vrm.scene.children.length}`);
                        
                        // 输出VRM特有信息
                        if (vrm.expressionManager) {
                            const expressions = vrm.expressionManager.expressions;
                            log(`表情数量: ${Object.keys(expressions).length}`);
                        }
                        if (vrm.humanoid) {
                            log('✅ 包含人形骨骼数据');
                        }
                        if (vrm.springBoneManager) {
                            log('✅ 包含Spring Bone物理模拟');
                        }
                        
                    } else {
                        log('❌ 未找到VRM数据');
                    }
                },
                (progress) => {
                    if (progress.total > 0) {
                        const percent = (progress.loaded / progress.total * 100).toFixed(1);
                        log(`加载进度: ${percent}%`);
                    }
                },
                (error) => {
                    log(`❌ VRM加载失败: ${error.message}`);
                    console.error('VRM加载错误:', error);
                }
            );
        }

        // 处理文件
        function handleFile(file) {
            log(`开始处理文件: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB)`);
            
            const reader = new FileReader();
            reader.onload = function(e) {
                const arrayBuffer = e.target.result;
                loadVRMFromArrayBuffer(arrayBuffer, file.name);
            };
            reader.onerror = function() {
                log('❌ 文件读取失败');
            };
            reader.readAsArrayBuffer(file);
        }

        // 从ArrayBuffer加载VRM
        function loadVRMFromArrayBuffer(arrayBuffer, fileName) {
            log(`解析VRM文件: ${fileName}`);
            
            if (!loader) {
                log('❌ 加载器未初始化');
                return;
            }
            
            try {
                // 创建临时URL
                const blob = new Blob([arrayBuffer], { type: 'application/octet-stream' });
                const url = URL.createObjectURL(blob);
                
                loader.load(
                    url,
                    (gltf) => {
                        // 清理临时URL
                        URL.revokeObjectURL(url);
                        
                        log('✅ GLTF解析成功');
                        
                        const vrm = gltf.userData.vrm;
                        if (vrm) {
                            log('✅ VRM数据解析成功');
                            
                            // 应用VRM优化
                            VRMUtils.removeUnnecessaryVertices(gltf.scene);
                            VRMUtils.combineSkeletons(gltf.scene);
                            VRMUtils.combineMorphs(vrm);
                            
                            // 禁用视锥体剔除
                            vrm.scene.traverse((obj) => {
                                obj.frustumCulled = false;
                            });
                            
                            // 清除之前的模型
                            if (currentVRM) {
                                scene.remove(currentVRM.scene);
                                if (currentVRM.dispose) {
                                    currentVRM.dispose();
                                }
                            }
                            
                            // 添加新模型
                            scene.add(vrm.scene);
                            currentVRM = vrm;
                            
                            // 调整相机位置
                            const box = new THREE.Box3().setFromObject(vrm.scene);
                            const center = box.getCenter(new THREE.Vector3());
                            const size = box.getSize(new THREE.Vector3());
                            
                            const maxDim = Math.max(size.x, size.y, size.z);
                            const fov = camera.fov * (Math.PI / 180);
                            let cameraZ = Math.abs(maxDim / 2 / Math.tan(fov / 2));
                            cameraZ *= 1.5;
                            
                            camera.position.set(0, center.y, center.z + cameraZ);
                            controls.target.copy(center);
                            controls.update();
                            
                            log(`🎉 VRM模型 ${fileName} 加载完成！`);
                            log(`模型信息: ${size.x.toFixed(2)} x ${size.y.toFixed(2)} x ${size.z.toFixed(2)}`);
                            log(`场景对象数: ${vrm.scene.children.length}`);
                            
                            // 输出VRM特有信息
                            if (vrm.expressionManager) {
                                const expressions = vrm.expressionManager.expressions;
                                log(`表情数量: ${Object.keys(expressions).length}`);
                            }
                            if (vrm.humanoid) {
                                log('✅ 包含人形骨骼数据');
                            }
                            if (vrm.springBoneManager) {
                                log('✅ 包含Spring Bone物理模拟');
                            }
                            
                        } else {
                            log('❌ 未找到VRM数据');
                        }
                    },
                    (progress) => {
                        if (progress.total > 0) {
                            const percent = (progress.loaded / progress.total * 100).toFixed(1);
                            log(`加载进度: ${percent}%`);
                        }
                    },
                    (error) => {
                        // 清理临时URL
                        URL.revokeObjectURL(url);
                        log(`❌ VRM加载失败: ${error.message}`);
                        console.error('VRM加载错误:', error);
                    }
                );
                
            } catch (error) {
                log(`❌ VRM解析失败: ${error.message}`);
                console.error('VRM解析错误:', error);
            }
        }

        // 暴露函数到全局作用域
        window.clearLog = clearLog;
        window.testLibraries = testLibraries;
        window.loadDefaultModel = loadDefaultModel;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 正确的VRM测试开始');
            
            // 初始化3D环境
            init3D();
            
            // 文件输入事件
            document.getElementById('file-input').addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    handleFile(file);
                }
            });
            
            // 拖拽事件
            const dropZone = document.getElementById('drop-zone');
            
            dropZone.addEventListener('dragover', function(e) {
                e.preventDefault();
                dropZone.classList.add('dragover');
            });
            
            dropZone.addEventListener('dragleave', function(e) {
                e.preventDefault();
                dropZone.classList.remove('dragover');
            });
            
            dropZone.addEventListener('drop', function(e) {
                e.preventDefault();
                dropZone.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFile(files[0]);
                }
            });
            
            log('💡 请选择VRM文件或点击"加载默认模型"进行测试');
        });
    </script>
</body>
</html>
