/*! (c) 2019-2023 pixiv Inc. - https://github.com/pixiv/three-vrm/blob/release/LICENSE */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("three")):"function"==typeof define&&define.amd?define(["exports","three"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).THREE_VRM={},e.THREE)}(this,(function(e,t){"use strict";function i(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(i){if("default"!==i){var n=Object.getOwnPropertyDescriptor(e,i);Object.defineProperty(t,i,n.get?n:{enumerable:!0,get:function(){return e[i]}})}})),t.default=e,Object.freeze(t)}var n=i(t);
/*!
     * @pixiv/three-vrm-core v2.0.6
     * The implementation of core features of VRM, for @pixiv/three-vrm
     *
     * Copyright (c) 2020-2023 pixiv Inc.
     * @pixiv/three-vrm-core is distributed under MIT License
     * https://github.com/pixiv/three-vrm/blob/release/LICENSE
     */class r extends n.Object3D{get overrideBlinkAmount(){return"block"===this.overrideBlink?0<this.weight?1:0:"blend"===this.overrideBlink?this.weight:0}get overrideLookAtAmount(){return"block"===this.overrideLookAt?0<this.weight?1:0:"blend"===this.overrideLookAt?this.weight:0}get overrideMouthAmount(){return"block"===this.overrideMouth?0<this.weight?1:0:"blend"===this.overrideMouth?this.weight:0}constructor(e){super(),this.weight=0,this.isBinary=!1,this.overrideBlink="none",this.overrideLookAt="none",this.overrideMouth="none",this._binds=[],this.name=`VRMExpression_${e}`,this.expressionName=e,this.type="VRMExpression",this.visible=!1}addBind(e){this._binds.push(e)}applyWeight(e){var t;let i=this.isBinary?this.weight<=.5?0:1:this.weight;i*=null!==(t=null==e?void 0:e.multiplier)&&void 0!==t?t:1,this._binds.forEach((e=>e.applyWeight(i)))}clearAppliedWeight(){this._binds.forEach((e=>e.clearAppliedWeight()))}}function o(e,t,i,n){return new(i||(i=Promise))((function(r,o){function s(e){try{l(n.next(e))}catch(e){o(e)}}function a(e){try{l(n.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(s,a)}l((n=n.apply(e,t||[])).next())}))}function s(e,t,i){var n,r;const o=e.parser.json,s=null===(n=o.nodes)||void 0===n?void 0:n[t];if(null==s)return console.warn(`extractPrimitivesInternal: Attempt to use nodes[${t}] of glTF but the node doesn't exist`),null;const a=s.mesh;if(null==a)return null;const l=null===(r=o.meshes)||void 0===r?void 0:r[a];if(null==l)return console.warn(`extractPrimitivesInternal: Attempt to use meshes[${a}] of glTF but the mesh doesn't exist`),null;const u=l.primitives.length,d=[];return i.traverse((e=>{d.length<u&&e.isMesh&&d.push(e)})),d}function a(e,t){return o(this,void 0,void 0,(function*(){const i=yield e.parser.getDependency("node",t);return s(e,t,i)}))}function l(e){return o(this,void 0,void 0,(function*(){const t=yield e.parser.getDependencies("node"),i=new Map;return t.forEach(((t,n)=>{const r=s(e,n,t);null!=r&&i.set(n,r)})),i}))}function u(e,t){var i,r;let o=null;if(parseInt(n.REVISION,10)>=133)o=null!==(r=null===(i=e.associations.get(t))||void 0===i?void 0:i.materials)&&void 0!==r?r:null;else{const i=e.associations.get(t);"materials"===(null==i?void 0:i.type)&&(o=i.index)}return o}const d={Aa:"aa",Ih:"ih",Ou:"ou",Ee:"ee",Oh:"oh",Blink:"blink",Happy:"happy",Angry:"angry",Sad:"sad",Relaxed:"relaxed",LookUp:"lookUp",Surprised:"surprised",LookDown:"lookDown",LookLeft:"lookLeft",LookRight:"lookRight",BlinkLeft:"blinkLeft",BlinkRight:"blinkRight",Neutral:"neutral"};function h(e){return Math.max(Math.min(e,1),0)}class c{get expressions(){return this._expressions.concat()}get expressionMap(){return Object.assign({},this._expressionMap)}get presetExpressionMap(){const e={},t=new Set(Object.values(d));return Object.entries(this._expressionMap).forEach((([i,n])=>{t.has(i)&&(e[i]=n)})),e}get customExpressionMap(){const e={},t=new Set(Object.values(d));return Object.entries(this._expressionMap).forEach((([i,n])=>{t.has(i)||(e[i]=n)})),e}constructor(){this.blinkExpressionNames=["blink","blinkLeft","blinkRight"],this.lookAtExpressionNames=["lookLeft","lookRight","lookUp","lookDown"],this.mouthExpressionNames=["aa","ee","ih","oh","ou"],this._expressions=[],this._expressionMap={}}copy(e){return this._expressions.concat().forEach((e=>{this.unregisterExpression(e)})),e._expressions.forEach((e=>{this.registerExpression(e)})),this.blinkExpressionNames=e.blinkExpressionNames.concat(),this.lookAtExpressionNames=e.lookAtExpressionNames.concat(),this.mouthExpressionNames=e.mouthExpressionNames.concat(),this}clone(){return(new c).copy(this)}getExpression(e){var t;return null!==(t=this._expressionMap[e])&&void 0!==t?t:null}registerExpression(e){this._expressions.push(e),this._expressionMap[e.expressionName]=e}unregisterExpression(e){const t=this._expressions.indexOf(e);-1===t&&console.warn("VRMExpressionManager: The specified expressions is not registered"),this._expressions.splice(t,1),delete this._expressionMap[e.expressionName]}getValue(e){var t;const i=this.getExpression(e);return null!==(t=null==i?void 0:i.weight)&&void 0!==t?t:null}setValue(e,t){const i=this.getExpression(e);i&&(i.weight=h(t))}getExpressionTrackName(e){const t=this.getExpression(e);return t?`${t.name}.weight`:null}update(){const e=this._calculateWeightMultipliers();this._expressions.forEach((e=>{e.clearAppliedWeight()})),this._expressions.forEach((t=>{let i=1;const n=t.expressionName;-1!==this.blinkExpressionNames.indexOf(n)&&(i*=e.blink),-1!==this.lookAtExpressionNames.indexOf(n)&&(i*=e.lookAt),-1!==this.mouthExpressionNames.indexOf(n)&&(i*=e.mouth),t.applyWeight({multiplier:i})}))}_calculateWeightMultipliers(){let e=1,t=1,i=1;return this._expressions.forEach((n=>{e-=n.overrideBlinkAmount,t-=n.overrideLookAtAmount,i-=n.overrideMouthAmount})),e=Math.max(0,e),t=Math.max(0,t),i=Math.max(0,i),{blink:e,lookAt:t,mouth:i}}}const p={Color:"color",EmissionColor:"emissionColor",ShadeColor:"shadeColor",MatcapColor:"matcapColor",RimColor:"rimColor",OutlineColor:"outlineColor"},m={_Color:p.Color,_EmissionColor:p.EmissionColor,_ShadeColor:p.ShadeColor,_RimColor:p.RimColor,_OutlineColor:p.OutlineColor},f=new n.Color;class g{constructor({material:e,type:t,targetValue:i}){var r,o,s;this.material=e,this.type=t,this.targetValue=i;const a=null===(r=Object.entries(g._propertyNameMapMap).find((([t])=>!0===e[t])))||void 0===r?void 0:r[1],l=null!==(o=null==a?void 0:a[t])&&void 0!==o?o:null;if(null==l)console.warn(`Tried to add a material color bind to the material ${null!==(s=e.name)&&void 0!==s?s:"(no name)"}, the type ${t} but the material or the type is not supported.`),this._state=null;else{const t=e[l].clone(),r=new n.Color(i.r-t.r,i.g-t.g,i.b-t.b);this._state={propertyName:l,initialValue:t,deltaValue:r}}}applyWeight(e){if(null==this._state)return;const{propertyName:t,deltaValue:i}=this._state,n=this.material[t];void 0!==n&&(n.add(f.copy(i).multiplyScalar(e)),"boolean"==typeof this.material.shouldApplyUniforms&&(this.material.shouldApplyUniforms=!0))}clearAppliedWeight(){if(null==this._state)return;const{propertyName:e,initialValue:t}=this._state,i=this.material[e];void 0!==i&&(i.copy(t),"boolean"==typeof this.material.shouldApplyUniforms&&(this.material.shouldApplyUniforms=!0))}}g._propertyNameMapMap={isMeshStandardMaterial:{color:"color",emissionColor:"emissive"},isMeshBasicMaterial:{color:"color"},isMToonMaterial:{color:"color",emissionColor:"emissive",outlineColor:"outlineColorFactor",matcapColor:"matcapFactor",rimColor:"parametricRimColorFactor",shadeColor:"shadeColorFactor"}};class v{constructor({primitives:e,index:t,weight:i}){this.primitives=e,this.index=t,this.weight=i}applyWeight(e){this.primitives.forEach((t=>{var i;null!=(null===(i=t.morphTargetInfluences)||void 0===i?void 0:i[this.index])&&(t.morphTargetInfluences[this.index]+=this.weight*e)}))}clearAppliedWeight(){this.primitives.forEach((e=>{var t;null!=(null===(t=e.morphTargetInfluences)||void 0===t?void 0:t[this.index])&&(e.morphTargetInfluences[this.index]=0)}))}}const _=new n.Vector2;class M{constructor({material:e,scale:t,offset:i}){var n,r;this.material=e,this.scale=t,this.offset=i;const o=null===(n=Object.entries(M._propertyNamesMap).find((([t])=>!0===e[t])))||void 0===n?void 0:n[1];null==o?(console.warn(`Tried to add a texture transform bind to the material ${null!==(r=e.name)&&void 0!==r?r:"(no name)"} but the material is not supported.`),this._properties=[]):(this._properties=[],o.forEach((n=>{var r;const o=null===(r=e[n])||void 0===r?void 0:r.clone();if(!o)return null;e[n]=o;const s=o.offset.clone(),a=o.repeat.clone(),l=i.clone().sub(s),u=t.clone().sub(a);this._properties.push({name:n,initialOffset:s,deltaOffset:l,initialScale:a,deltaScale:u})})))}applyWeight(e){this._properties.forEach((t=>{const i=this.material[t.name];void 0!==i&&(i.offset.add(_.copy(t.deltaOffset).multiplyScalar(e)),i.repeat.add(_.copy(t.deltaScale).multiplyScalar(e)),i.needsUpdate=!0)}))}clearAppliedWeight(){this._properties.forEach((e=>{const t=this.material[e.name];void 0!==t&&(t.offset.copy(e.initialOffset),t.repeat.copy(e.initialScale),t.needsUpdate=!0)}))}}M._propertyNamesMap={isMeshStandardMaterial:["map","emissiveMap","bumpMap","normalMap","displacementMap","roughnessMap","metalnessMap","alphaMap"],isMeshBasicMaterial:["map","specularMap","alphaMap"],isMToonMaterial:["map","normalMap","emissiveMap","shadeMultiplyTexture","rimMultiplyTexture","outlineWidthMultiplyTexture","uvAnimationMaskTexture"]};const x=new Set(["1.0","1.0-beta"]);class y{get name(){return"VRMExpressionLoaderPlugin"}constructor(e){this.parser=e}afterRoot(e){return o(this,void 0,void 0,(function*(){e.userData.vrmExpressionManager=yield this._import(e)}))}_import(e){return o(this,void 0,void 0,(function*(){const t=yield this._v1Import(e);if(t)return t;const i=yield this._v0Import(e);return i||null}))}_v1Import(e){var t,i;return o(this,void 0,void 0,(function*(){const s=this.parser.json;if(!(-1!==(null===(t=s.extensionsUsed)||void 0===t?void 0:t.indexOf("VRMC_vrm"))))return null;const l=null===(i=s.extensions)||void 0===i?void 0:i.VRMC_vrm;if(!l)return null;const h=l.specVersion;if(!x.has(h))return console.warn(`VRMExpressionLoaderPlugin: Unknown VRMC_vrm specVersion "${h}"`),null;const p=l.expressions;if(!p)return null;const m=new Set(Object.values(d)),f=new Map;null!=p.preset&&Object.entries(p.preset).forEach((([e,t])=>{null!=t&&(m.has(e)?f.set(e,t):console.warn(`VRMExpressionLoaderPlugin: Unknown preset name "${e}" detected. Ignoring the expression`))})),null!=p.custom&&Object.entries(p.custom).forEach((([e,t])=>{m.has(e)?console.warn(`VRMExpressionLoaderPlugin: Custom expression cannot have preset name "${e}". Ignoring the expression`):f.set(e,t)}));const _=new c;return yield Promise.all(Array.from(f.entries()).map((([t,i])=>o(this,void 0,void 0,(function*(){var s,l,d,h,c,p,m;const f=new r(t);if(e.scene.add(f),f.isBinary=null!==(s=i.isBinary)&&void 0!==s&&s,f.overrideBlink=null!==(l=i.overrideBlink)&&void 0!==l?l:"none",f.overrideLookAt=null!==(d=i.overrideLookAt)&&void 0!==d?d:"none",f.overrideMouth=null!==(h=i.overrideMouth)&&void 0!==h?h:"none",null===(c=i.morphTargetBinds)||void 0===c||c.forEach((t=>o(this,void 0,void 0,(function*(){var n;if(void 0===t.node||void 0===t.index)return;const r=yield a(e,t.node),o=t.index;r.every((e=>Array.isArray(e.morphTargetInfluences)&&o<e.morphTargetInfluences.length))?f.addBind(new v({primitives:r,index:o,weight:null!==(n=t.weight)&&void 0!==n?n:1})):console.warn(`VRMExpressionLoaderPlugin: ${i.name} attempts to index morph #${o} but not found.`)})))),i.materialColorBinds||i.textureTransformBinds){const t=[];e.scene.traverse((e=>{const i=e.material;i&&t.push(i)})),null===(p=i.materialColorBinds)||void 0===p||p.forEach((e=>o(this,void 0,void 0,(function*(){t.filter((t=>{const i=u(this.parser,t);return e.material===i})).forEach((t=>{f.addBind(new g({material:t,type:e.type,targetValue:(new n.Color).fromArray(e.targetValue)}))}))})))),null===(m=i.textureTransformBinds)||void 0===m||m.forEach((e=>o(this,void 0,void 0,(function*(){t.filter((t=>{const i=u(this.parser,t);return e.material===i})).forEach((t=>{var i,r;f.addBind(new M({material:t,offset:(new n.Vector2).fromArray(null!==(i=e.offset)&&void 0!==i?i:[0,0]),scale:(new n.Vector2).fromArray(null!==(r=e.scale)&&void 0!==r?r:[1,1])}))}))}))))}_.registerExpression(f)}))))),_}))}_v0Import(e){var t;return o(this,void 0,void 0,(function*(){const i=this.parser.json,s=null===(t=i.extensions)||void 0===t?void 0:t.VRM;if(!s)return null;const l=s.blendShapeMaster;if(!l)return null;const u=new c,d=l.blendShapeGroups;if(!d)return u;const h=new Set;return yield Promise.all(d.map((t=>o(this,void 0,void 0,(function*(){var s;const l=t.presetName,d=null!=l&&y.v0v1PresetNameMap[l]||null,c=null!=d?d:t.name;if(null==c)return void console.warn("VRMExpressionLoaderPlugin: One of custom expressions has no name. Ignoring the expression");if(h.has(c))return void console.warn(`VRMExpressionLoaderPlugin: An expression preset ${l} has duplicated entries. Ignoring the expression`);h.add(c);const p=new r(c);e.scene.add(p),p.isBinary=null!==(s=t.isBinary)&&void 0!==s&&s,t.binds&&t.binds.forEach((n=>o(this,void 0,void 0,(function*(){var r;if(void 0===n.mesh||void 0===n.index)return;const s=[];null===(r=i.nodes)||void 0===r||r.forEach(((e,t)=>{e.mesh===n.mesh&&s.push(t)}));const l=n.index;yield Promise.all(s.map((i=>o(this,void 0,void 0,(function*(){var r;const o=yield a(e,i);o.every((e=>Array.isArray(e.morphTargetInfluences)&&l<e.morphTargetInfluences.length))?p.addBind(new v({primitives:o,index:l,weight:.01*(null!==(r=n.weight)&&void 0!==r?r:100)})):console.warn(`VRMExpressionLoaderPlugin: ${t.name} attempts to index ${l}th morph but not found.`)})))))}))));const f=t.materialValues;f&&0!==f.length&&f.forEach((t=>{if(void 0===t.materialName||void 0===t.propertyName||void 0===t.targetValue)return;const i=[];e.scene.traverse((e=>{if(e.material){const n=e.material;Array.isArray(n)?i.push(...n.filter((e=>(e.name===t.materialName||e.name===t.materialName+" (Outline)")&&-1===i.indexOf(e)))):n.name===t.materialName&&-1===i.indexOf(n)&&i.push(n)}}));const r=t.propertyName;i.forEach((e=>{if("_MainTex_ST"===r){const i=new n.Vector2(t.targetValue[0],t.targetValue[1]),r=new n.Vector2(t.targetValue[2],t.targetValue[3]);return r.y=1-r.y-i.y,void p.addBind(new M({material:e,scale:i,offset:r}))}const i=m[r];i?p.addBind(new g({material:e,type:i,targetValue:new n.Color(...t.targetValue.slice(0,3))})):console.warn(r+" is not supported")}))})),u.registerExpression(p)}))))),u}))}}y.v0v1PresetNameMap={a:"aa",e:"ee",i:"ih",o:"oh",u:"ou",blink:"blink",joy:"happy",angry:"angry",sorrow:"sad",fun:"relaxed",lookup:"lookUp",lookdown:"lookDown",lookleft:"lookLeft",lookright:"lookRight",blink_l:"blinkLeft",blink_r:"blinkRight",neutral:"neutral"};class R{constructor(e,t){this._firstPersonOnlyLayer=R.DEFAULT_FIRSTPERSON_ONLY_LAYER,this._thirdPersonOnlyLayer=R.DEFAULT_THIRDPERSON_ONLY_LAYER,this._initializedLayers=!1,this.humanoid=e,this.meshAnnotations=t}copy(e){if(this.humanoid!==e.humanoid)throw new Error("VRMFirstPerson: humanoid must be same in order to copy");return this.meshAnnotations=e.meshAnnotations.map((e=>({meshes:e.meshes.concat(),type:e.type}))),this}clone(){return new R(this.humanoid,this.meshAnnotations).copy(this)}get firstPersonOnlyLayer(){return this._firstPersonOnlyLayer}get thirdPersonOnlyLayer(){return this._thirdPersonOnlyLayer}setup({firstPersonOnlyLayer:e=R.DEFAULT_FIRSTPERSON_ONLY_LAYER,thirdPersonOnlyLayer:t=R.DEFAULT_THIRDPERSON_ONLY_LAYER}={}){this._initializedLayers||(this._firstPersonOnlyLayer=e,this._thirdPersonOnlyLayer=t,this.meshAnnotations.forEach((e=>{e.meshes.forEach((t=>{"firstPersonOnly"===e.type?(t.layers.set(this._firstPersonOnlyLayer),t.traverse((e=>e.layers.set(this._firstPersonOnlyLayer)))):"thirdPersonOnly"===e.type?(t.layers.set(this._thirdPersonOnlyLayer),t.traverse((e=>e.layers.set(this._thirdPersonOnlyLayer)))):"auto"===e.type&&this._createHeadlessModel(t)}))})),this._initializedLayers=!0)}_excludeTriangles(e,t,i,n){let r=0;if(null!=t&&t.length>0)for(let o=0;o<e.length;o+=3){const s=e[o],a=e[o+1],l=e[o+2],u=t[s],d=i[s];if(u[0]>0&&n.includes(d[0]))continue;if(u[1]>0&&n.includes(d[1]))continue;if(u[2]>0&&n.includes(d[2]))continue;if(u[3]>0&&n.includes(d[3]))continue;const h=t[a],c=i[a];if(h[0]>0&&n.includes(c[0]))continue;if(h[1]>0&&n.includes(c[1]))continue;if(h[2]>0&&n.includes(c[2]))continue;if(h[3]>0&&n.includes(c[3]))continue;const p=t[l],m=i[l];p[0]>0&&n.includes(m[0])||(p[1]>0&&n.includes(m[1])||p[2]>0&&n.includes(m[2])||p[3]>0&&n.includes(m[3])||(e[r++]=s,e[r++]=a,e[r++]=l))}return r}_createErasedMesh(e,t){const i=new n.SkinnedMesh(e.geometry.clone(),e.material);i.name=`${e.name}(erase)`,i.frustumCulled=e.frustumCulled,i.layers.set(this._firstPersonOnlyLayer);const r=i.geometry,o=r.getAttribute("skinIndex"),s=o instanceof n.GLBufferAttribute?[]:o.array,a=[];for(let e=0;e<s.length;e+=4)a.push([s[e],s[e+1],s[e+2],s[e+3]]);const l=r.getAttribute("skinWeight"),u=l instanceof n.GLBufferAttribute?[]:l.array,d=[];for(let e=0;e<u.length;e+=4)d.push([u[e],u[e+1],u[e+2],u[e+3]]);const h=r.getIndex();if(!h)throw new Error("The geometry doesn't have an index buffer");const c=Array.from(h.array),p=this._excludeTriangles(c,d,a,t),m=[];for(let e=0;e<p;e++)m[e]=c[e];return r.setIndex(m),e.onBeforeRender&&(i.onBeforeRender=e.onBeforeRender),i.bind(new n.Skeleton(e.skeleton.bones,e.skeleton.boneInverses),new n.Matrix4),i}_createHeadlessModelForSkinnedMesh(e,t){const i=[];if(t.skeleton.bones.forEach(((e,t)=>{this._isEraseTarget(e)&&i.push(t)})),!i.length)return t.layers.enable(this._thirdPersonOnlyLayer),void t.layers.enable(this._firstPersonOnlyLayer);t.layers.set(this._thirdPersonOnlyLayer);const n=this._createErasedMesh(t,i);e.add(n)}_createHeadlessModel(e){if("Group"===e.type)if(e.layers.set(this._thirdPersonOnlyLayer),this._isEraseTarget(e))e.traverse((e=>e.layers.set(this._thirdPersonOnlyLayer)));else{const t=new n.Group;t.name=`_headless_${e.name}`,t.layers.set(this._firstPersonOnlyLayer),e.parent.add(t),e.children.filter((e=>"SkinnedMesh"===e.type)).forEach((e=>{const i=e;this._createHeadlessModelForSkinnedMesh(t,i)}))}else if("SkinnedMesh"===e.type){const t=e;this._createHeadlessModelForSkinnedMesh(e.parent,t)}else this._isEraseTarget(e)&&(e.layers.set(this._thirdPersonOnlyLayer),e.traverse((e=>e.layers.set(this._thirdPersonOnlyLayer))))}_isEraseTarget(e){return e===this.humanoid.getRawBoneNode("head")||!!e.parent&&this._isEraseTarget(e.parent)}}R.DEFAULT_FIRSTPERSON_ONLY_LAYER=9,R.DEFAULT_THIRDPERSON_ONLY_LAYER=10;const T=new Set(["1.0","1.0-beta"]);class E{get name(){return"VRMFirstPersonLoaderPlugin"}constructor(e){this.parser=e}afterRoot(e){return o(this,void 0,void 0,(function*(){const t=e.userData.vrmHumanoid;if(null!==t){if(void 0===t)throw new Error("VRMFirstPersonLoaderPlugin: vrmHumanoid is undefined. VRMHumanoidLoaderPlugin have to be used first");e.userData.vrmFirstPerson=yield this._import(e,t)}}))}_import(e,t){return o(this,void 0,void 0,(function*(){if(null==t)return null;const i=yield this._v1Import(e,t);if(i)return i;const n=yield this._v0Import(e,t);return n||null}))}_v1Import(e,t){var i,n;return o(this,void 0,void 0,(function*(){const r=this.parser.json;if(!(-1!==(null===(i=r.extensionsUsed)||void 0===i?void 0:i.indexOf("VRMC_vrm"))))return null;const o=null===(n=r.extensions)||void 0===n?void 0:n.VRMC_vrm;if(!o)return null;const s=o.specVersion;if(!T.has(s))return console.warn(`VRMFirstPersonLoaderPlugin: Unknown VRMC_vrm specVersion "${s}"`),null;const a=o.firstPerson;if(!a)return null;const u=[],d=yield l(e);return Array.from(d.entries()).forEach((([e,t])=>{var i;const n=a.meshAnnotations?a.meshAnnotations.find((t=>t.node===e)):void 0;u.push({meshes:t,type:null!==(i=null==n?void 0:n.type)&&void 0!==i?i:"both"})})),new R(t,u)}))}_v0Import(e,t){var i;return o(this,void 0,void 0,(function*(){const n=this.parser.json,r=null===(i=n.extensions)||void 0===i?void 0:i.VRM;if(!r)return null;const o=r.firstPerson;if(!o)return null;const s=[],a=yield l(e);return Array.from(a.entries()).forEach((([e,t])=>{const i=n.nodes[e],r=o.meshAnnotations?o.meshAnnotations.find((e=>e.mesh===i.mesh)):void 0;s.push({meshes:t,type:this._convertV0FlagToV1Type(null==r?void 0:r.firstPersonFlag)})})),new R(t,s)}))}_convertV0FlagToV1Type(e){return"FirstPersonOnly"===e?"firstPersonOnly":"ThirdPersonOnly"===e?"thirdPersonOnly":"Auto"===e?"auto":"both"}}const w=new n.Vector3,P=new n.Vector3,S=new n.Quaternion;class L extends n.Group{constructor(e){super(),this.vrmHumanoid=e,this._boneAxesMap=new Map,Object.values(e.humanBones).forEach((e=>{const t=new n.AxesHelper(1);t.matrixAutoUpdate=!1,t.material.depthTest=!1,t.material.depthWrite=!1,this.add(t),this._boneAxesMap.set(e,t)}))}dispose(){Array.from(this._boneAxesMap.values()).forEach((e=>{e.geometry.dispose(),e.material.dispose()}))}updateMatrixWorld(e){Array.from(this._boneAxesMap.entries()).forEach((([e,t])=>{e.node.updateWorldMatrix(!0,!1),e.node.matrixWorld.decompose(w,S,P);const i=w.set(.1,.1,.1).divide(P);t.matrix.copy(e.node.matrixWorld).scale(i)})),super.updateMatrixWorld(e)}}const A=["hips","spine","chest","upperChest","neck","head","leftEye","rightEye","jaw","leftUpperLeg","leftLowerLeg","leftFoot","leftToes","rightUpperLeg","rightLowerLeg","rightFoot","rightToes","leftShoulder","leftUpperArm","leftLowerArm","leftHand","rightShoulder","rightUpperArm","rightLowerArm","rightHand","leftThumbMetacarpal","leftThumbProximal","leftThumbDistal","leftIndexProximal","leftIndexIntermediate","leftIndexDistal","leftMiddleProximal","leftMiddleIntermediate","leftMiddleDistal","leftRingProximal","leftRingIntermediate","leftRingDistal","leftLittleProximal","leftLittleIntermediate","leftLittleDistal","rightThumbMetacarpal","rightThumbProximal","rightThumbDistal","rightIndexProximal","rightIndexIntermediate","rightIndexDistal","rightMiddleProximal","rightMiddleIntermediate","rightMiddleDistal","rightRingProximal","rightRingIntermediate","rightRingDistal","rightLittleProximal","rightLittleIntermediate","rightLittleDistal"],I={hips:null,spine:"hips",chest:"spine",upperChest:"chest",neck:"upperChest",head:"neck",leftEye:"head",rightEye:"head",jaw:"head",leftUpperLeg:"hips",leftLowerLeg:"leftUpperLeg",leftFoot:"leftLowerLeg",leftToes:"leftFoot",rightUpperLeg:"hips",rightLowerLeg:"rightUpperLeg",rightFoot:"rightLowerLeg",rightToes:"rightFoot",leftShoulder:"upperChest",leftUpperArm:"leftShoulder",leftLowerArm:"leftUpperArm",leftHand:"leftLowerArm",rightShoulder:"upperChest",rightUpperArm:"rightShoulder",rightLowerArm:"rightUpperArm",rightHand:"rightLowerArm",leftThumbMetacarpal:"leftHand",leftThumbProximal:"leftThumbMetacarpal",leftThumbDistal:"leftThumbProximal",leftIndexProximal:"leftHand",leftIndexIntermediate:"leftIndexProximal",leftIndexDistal:"leftIndexIntermediate",leftMiddleProximal:"leftHand",leftMiddleIntermediate:"leftMiddleProximal",leftMiddleDistal:"leftMiddleIntermediate",leftRingProximal:"leftHand",leftRingIntermediate:"leftRingProximal",leftRingDistal:"leftRingIntermediate",leftLittleProximal:"leftHand",leftLittleIntermediate:"leftLittleProximal",leftLittleDistal:"leftLittleIntermediate",rightThumbMetacarpal:"rightHand",rightThumbProximal:"rightThumbMetacarpal",rightThumbDistal:"rightThumbProximal",rightIndexProximal:"rightHand",rightIndexIntermediate:"rightIndexProximal",rightIndexDistal:"rightIndexIntermediate",rightMiddleProximal:"rightHand",rightMiddleIntermediate:"rightMiddleProximal",rightMiddleDistal:"rightMiddleIntermediate",rightRingProximal:"rightHand",rightRingIntermediate:"rightRingProximal",rightRingDistal:"rightRingIntermediate",rightLittleProximal:"rightHand",rightLittleIntermediate:"rightLittleProximal",rightLittleDistal:"rightLittleIntermediate"};function b(e){return e.invert?e.invert():e.inverse(),e}const U=new n.Vector3,V=new n.Quaternion;class O{constructor(e){this.humanBones=e,this.restPose=this.getAbsolutePose()}getAbsolutePose(){const e={};return Object.keys(this.humanBones).forEach((t=>{const i=t,n=this.getBoneNode(i);n&&(U.copy(n.position),V.copy(n.quaternion),e[i]={position:U.toArray(),rotation:V.toArray()})})),e}getPose(){const e={};return Object.keys(this.humanBones).forEach((t=>{const i=t,n=this.getBoneNode(i);if(!n)return;U.set(0,0,0),V.identity();const r=this.restPose[i];(null==r?void 0:r.position)&&U.fromArray(r.position).negate(),(null==r?void 0:r.rotation)&&b(V.fromArray(r.rotation)),U.add(n.position),V.premultiply(n.quaternion),e[i]={position:U.toArray(),rotation:V.toArray()}})),e}setPose(e){Object.entries(e).forEach((([e,t])=>{const i=e,n=this.getBoneNode(i);if(!n)return;const r=this.restPose[i];r&&((null==t?void 0:t.position)&&(n.position.fromArray(t.position),r.position&&n.position.add(U.fromArray(r.position))),(null==t?void 0:t.rotation)&&(n.quaternion.fromArray(t.rotation),r.rotation&&n.quaternion.multiply(V.fromArray(r.rotation))))}))}resetPose(){Object.entries(this.restPose).forEach((([e,t])=>{const i=this.getBoneNode(e);i&&((null==t?void 0:t.position)&&i.position.fromArray(t.position),(null==t?void 0:t.rotation)&&i.quaternion.fromArray(t.rotation))}))}getBone(e){var t;return null!==(t=this.humanBones[e])&&void 0!==t?t:void 0}getBoneNode(e){var t,i;return null!==(i=null===(t=this.humanBones[e])||void 0===t?void 0:t.node)&&void 0!==i?i:null}}const N=new n.Vector3,C=new n.Quaternion,D=new n.Vector3;class F extends O{static _setupTransforms(e){const t=new n.Object3D;t.name="VRMHumanoidRig";const i={},r={},o={};A.forEach((t=>{const s=e.getBoneNode(t);if(s){const e=new n.Vector3,a=new n.Quaternion;s.updateWorldMatrix(!0,!1),s.matrixWorld.decompose(e,a,N),i[t]=e,r[t]=a,o[t]=s.quaternion.clone()}}));const s={},a={};return A.forEach((o=>{var l;const u=e.getBoneNode(o);if(u){const e=i[o];let d,h,c=o;for(;null==d&&(c=I[c],null!=c);)d=i[c],h=r[c];const p=new n.Object3D;p.name="Normalized_"+u.name;(c?null===(l=a[c])||void 0===l?void 0:l.node:t).add(p),p.position.copy(e),d&&p.position.sub(d),a[o]={node:p},s[o]=null!=h?h:new n.Quaternion}})),{rigBones:a,root:t,parentWorldRotations:s,boneRotations:o}}constructor(e){const{rigBones:t,root:i,parentWorldRotations:n,boneRotations:r}=F._setupTransforms(e);super(t),this.original=e,this.root=i,this._parentWorldRotations=n,this._boneRotations=r}update(){A.forEach((e=>{const t=this.original.getBoneNode(e);if(null!=t){const i=this.getBoneNode(e),n=this._parentWorldRotations[e],r=C.copy(n).invert(),o=this._boneRotations[e];if(t.quaternion.copy(i.quaternion).multiply(n).premultiply(r).multiply(o),"hips"===e){const e=i.getWorldPosition(D);t.parent.updateWorldMatrix(!0,!1);const n=t.parent.matrixWorld,r=e.applyMatrix4(n.invert());t.position.copy(r)}}}))}}class H{get restPose(){return console.warn("VRMHumanoid: restPose is deprecated. Use either rawRestPose or normalizedRestPose instead."),this.rawRestPose}get rawRestPose(){return this._rawHumanBones.restPose}get normalizedRestPose(){return this._normalizedHumanBones.restPose}get humanBones(){return this._rawHumanBones.humanBones}get rawHumanBones(){return this._rawHumanBones.humanBones}get normalizedHumanBones(){return this._normalizedHumanBones.humanBones}get normalizedHumanBonesRoot(){return this._normalizedHumanBones.root}constructor(e,t){var i;this.autoUpdateHumanBones=null===(i=null==t?void 0:t.autoUpdateHumanBones)||void 0===i||i,this._rawHumanBones=new O(e),this._normalizedHumanBones=new F(this._rawHumanBones)}copy(e){return this.autoUpdateHumanBones=e.autoUpdateHumanBones,this._rawHumanBones=new O(e.humanBones),this._normalizedHumanBones=new F(this._rawHumanBones),this}clone(){return new H(this.humanBones,{autoUpdateHumanBones:this.autoUpdateHumanBones}).copy(this)}getAbsolutePose(){return console.warn("VRMHumanoid: getAbsolutePose() is deprecated. Use either getRawAbsolutePose() or getNormalizedAbsolutePose() instead."),this.getRawAbsolutePose()}getRawAbsolutePose(){return this._rawHumanBones.getAbsolutePose()}getNormalizedAbsolutePose(){return this._normalizedHumanBones.getAbsolutePose()}getPose(){return console.warn("VRMHumanoid: getPose() is deprecated. Use either getRawPose() or getNormalizedPose() instead."),this.getRawPose()}getRawPose(){return this._rawHumanBones.getPose()}getNormalizedPose(){return this._normalizedHumanBones.getPose()}setPose(e){return console.warn("VRMHumanoid: setPose() is deprecated. Use either setRawPose() or setNormalizedPose() instead."),this.setRawPose(e)}setRawPose(e){return this._rawHumanBones.setPose(e)}setNormalizedPose(e){return this._normalizedHumanBones.setPose(e)}resetPose(){return console.warn("VRMHumanoid: resetPose() is deprecated. Use either resetRawPose() or resetNormalizedPose() instead."),this.resetRawPose()}resetRawPose(){return this._rawHumanBones.resetPose()}resetNormalizedPose(){return this._normalizedHumanBones.resetPose()}getBone(e){return console.warn("VRMHumanoid: getBone() is deprecated. Use either getRawBone() or getNormalizedBone() instead."),this.getRawBone(e)}getRawBone(e){return this._rawHumanBones.getBone(e)}getNormalizedBone(e){return this._normalizedHumanBones.getBone(e)}getBoneNode(e){return console.warn("VRMHumanoid: getBoneNode() is deprecated. Use either getRawBoneNode() or getNormalizedBoneNode() instead."),this.getRawBoneNode(e)}getRawBoneNode(e){return this._rawHumanBones.getBoneNode(e)}getNormalizedBoneNode(e){return this._normalizedHumanBones.getBoneNode(e)}update(){this.autoUpdateHumanBones&&this._normalizedHumanBones.update()}}const B={Hips:"hips",Spine:"spine",Head:"head",LeftUpperLeg:"leftUpperLeg",LeftLowerLeg:"leftLowerLeg",LeftFoot:"leftFoot",RightUpperLeg:"rightUpperLeg",RightLowerLeg:"rightLowerLeg",RightFoot:"rightFoot",LeftUpperArm:"leftUpperArm",LeftLowerArm:"leftLowerArm",LeftHand:"leftHand",RightUpperArm:"rightUpperArm",RightLowerArm:"rightLowerArm",RightHand:"rightHand"},k=new Set(["1.0","1.0-beta"]),W={leftThumbProximal:"leftThumbMetacarpal",leftThumbIntermediate:"leftThumbProximal",rightThumbProximal:"rightThumbMetacarpal",rightThumbIntermediate:"rightThumbProximal"};class z{get name(){return"VRMHumanoidLoaderPlugin"}constructor(e,t){this.parser=e,this.helperRoot=null==t?void 0:t.helperRoot,this.autoUpdateHumanBones=null==t?void 0:t.autoUpdateHumanBones}afterRoot(e){return o(this,void 0,void 0,(function*(){e.userData.vrmHumanoid=yield this._import(e)}))}_import(e){return o(this,void 0,void 0,(function*(){const t=yield this._v1Import(e);if(t)return t;const i=yield this._v0Import(e);return i||null}))}_v1Import(e){var t,i;return o(this,void 0,void 0,(function*(){const n=this.parser.json;if(!(-1!==(null===(t=n.extensionsUsed)||void 0===t?void 0:t.indexOf("VRMC_vrm"))))return null;const r=null===(i=n.extensions)||void 0===i?void 0:i.VRMC_vrm;if(!r)return null;const s=r.specVersion;if(!k.has(s))return console.warn(`VRMHumanoidLoaderPlugin: Unknown VRMC_vrm specVersion "${s}"`),null;const a=r.humanoid;if(!a)return null;const l=null!=a.humanBones.leftThumbIntermediate||null!=a.humanBones.rightThumbIntermediate,u={};null!=a.humanBones&&(yield Promise.all(Object.entries(a.humanBones).map((([e,t])=>o(this,void 0,void 0,(function*(){let i=e;const n=t.node;if(l){const e=W[i];null!=e&&(i=e)}const r=yield this.parser.getDependency("node",n);null!=r?u[i]={node:r}:console.warn(`A glTF node bound to the humanoid bone ${i} (index = ${n}) does not exist`)}))))));const d=new H(this._ensureRequiredBonesExist(u),{autoUpdateHumanBones:this.autoUpdateHumanBones});if(e.scene.add(d.normalizedHumanBonesRoot),this.helperRoot){const e=new L(d);this.helperRoot.add(e),e.renderOrder=this.helperRoot.renderOrder}return d}))}_v0Import(e){var t;return o(this,void 0,void 0,(function*(){const i=this.parser.json,n=null===(t=i.extensions)||void 0===t?void 0:t.VRM;if(!n)return null;const r=n.humanoid;if(!r)return null;const s={};null!=r.humanBones&&(yield Promise.all(r.humanBones.map((e=>o(this,void 0,void 0,(function*(){const t=e.bone,i=e.node;if(null==t||null==i)return;const n=yield this.parser.getDependency("node",i);if(null==n)return void console.warn(`A glTF node bound to the humanoid bone ${t} (index = ${i}) does not exist`);const r=W[t],o=null!=r?r:t;null==s[o]?s[o]={node:n}:console.warn(`Multiple bone entries for ${o} detected (index = ${i}), ignoring duplicated entries.`)}))))));const a=new H(this._ensureRequiredBonesExist(s),{autoUpdateHumanBones:this.autoUpdateHumanBones});if(e.scene.add(a.normalizedHumanBonesRoot),this.helperRoot){const e=new L(a);this.helperRoot.add(e),e.renderOrder=this.helperRoot.renderOrder}return a}))}_ensureRequiredBonesExist(e){const t=Object.values(B).filter((t=>null==e[t]));if(t.length>0)throw new Error(`VRMHumanoidLoaderPlugin: These humanoid bones are required but not exist: ${t.join(", ")}`);return e}}class j extends n.BufferGeometry{constructor(){super(),this._currentTheta=0,this._currentRadius=0,this.theta=0,this.radius=0,this._currentTheta=0,this._currentRadius=0,this._attrPos=new n.BufferAttribute(new Float32Array(195),3),this.setAttribute("position",this._attrPos),this._attrIndex=new n.BufferAttribute(new Uint16Array(189),1),this.setIndex(this._attrIndex),this._buildIndex(),this.update()}update(){let e=!1;this._currentTheta!==this.theta&&(this._currentTheta=this.theta,e=!0),this._currentRadius!==this.radius&&(this._currentRadius=this.radius,e=!0),e&&this._buildPosition()}_buildPosition(){this._attrPos.setXYZ(0,0,0,0);for(let e=0;e<64;e++){const t=e/63*this._currentTheta;this._attrPos.setXYZ(e+1,this._currentRadius*Math.sin(t),0,this._currentRadius*Math.cos(t))}this._attrPos.needsUpdate=!0}_buildIndex(){for(let e=0;e<63;e++)this._attrIndex.setXYZ(3*e,0,e+1,e+2);this._attrIndex.needsUpdate=!0}}class Q extends n.BufferGeometry{constructor(){super(),this.radius=0,this._currentRadius=0,this.tail=new n.Vector3,this._currentTail=new n.Vector3,this._attrPos=new n.BufferAttribute(new Float32Array(294),3),this.setAttribute("position",this._attrPos),this._attrIndex=new n.BufferAttribute(new Uint16Array(194),1),this.setIndex(this._attrIndex),this._buildIndex(),this.update()}update(){let e=!1;this._currentRadius!==this.radius&&(this._currentRadius=this.radius,e=!0),this._currentTail.equals(this.tail)||(this._currentTail.copy(this.tail),e=!0),e&&this._buildPosition()}_buildPosition(){for(let e=0;e<32;e++){const t=e/16*Math.PI;this._attrPos.setXYZ(e,Math.cos(t),Math.sin(t),0),this._attrPos.setXYZ(32+e,0,Math.cos(t),Math.sin(t)),this._attrPos.setXYZ(64+e,Math.sin(t),0,Math.cos(t))}this.scale(this._currentRadius,this._currentRadius,this._currentRadius),this.translate(this._currentTail.x,this._currentTail.y,this._currentTail.z),this._attrPos.setXYZ(96,0,0,0),this._attrPos.setXYZ(97,this._currentTail.x,this._currentTail.y,this._currentTail.z),this._attrPos.needsUpdate=!0}_buildIndex(){for(let e=0;e<32;e++){const t=(e+1)%32;this._attrIndex.setXY(2*e,e,t),this._attrIndex.setXY(64+2*e,32+e,32+t),this._attrIndex.setXY(128+2*e,64+e,64+t)}this._attrIndex.setXY(192,96,97),this._attrIndex.needsUpdate=!0}}const X=new n.Quaternion,G=new n.Quaternion,Y=new n.Vector3,q=new n.Vector3,$=Math.sqrt(2)/2,Z=new n.Quaternion(0,0,-$,$),J=new n.Vector3(0,1,0);class K extends n.Group{constructor(e){super(),this.matrixAutoUpdate=!1,this.vrmLookAt=e;{const e=new j;e.radius=.5;const t=new n.MeshBasicMaterial({color:65280,transparent:!0,opacity:.5,side:n.DoubleSide,depthTest:!1,depthWrite:!1});this._meshPitch=new n.Mesh(e,t),this.add(this._meshPitch)}{const e=new j;e.radius=.5;const t=new n.MeshBasicMaterial({color:16711680,transparent:!0,opacity:.5,side:n.DoubleSide,depthTest:!1,depthWrite:!1});this._meshYaw=new n.Mesh(e,t),this.add(this._meshYaw)}{const e=new Q;e.radius=.1;const t=new n.LineBasicMaterial({color:16777215,depthTest:!1,depthWrite:!1});this._lineTarget=new n.LineSegments(e,t),this._lineTarget.frustumCulled=!1,this.add(this._lineTarget)}}dispose(){this._meshYaw.geometry.dispose(),this._meshYaw.material.dispose(),this._meshPitch.geometry.dispose(),this._meshPitch.material.dispose(),this._lineTarget.geometry.dispose(),this._lineTarget.material.dispose()}updateMatrixWorld(e){const t=n.MathUtils.DEG2RAD*this.vrmLookAt.yaw;this._meshYaw.geometry.theta=t,this._meshYaw.geometry.update();const i=n.MathUtils.DEG2RAD*this.vrmLookAt.pitch;this._meshPitch.geometry.theta=i,this._meshPitch.geometry.update(),this.vrmLookAt.getLookAtWorldPosition(Y),this.vrmLookAt.getLookAtWorldQuaternion(X),X.multiply(this.vrmLookAt.getFaceFrontQuaternion(G)),this._meshYaw.position.copy(Y),this._meshYaw.quaternion.copy(X),this._meshPitch.position.copy(Y),this._meshPitch.quaternion.copy(X),this._meshPitch.quaternion.multiply(G.setFromAxisAngle(J,t)),this._meshPitch.quaternion.multiply(Z);const{target:r,autoUpdate:o}=this.vrmLookAt;null!=r&&o&&(r.getWorldPosition(q).sub(Y),this._lineTarget.geometry.tail.copy(q),this._lineTarget.geometry.update(),this._lineTarget.position.copy(Y)),super.updateMatrixWorld(e)}}const ee=new n.Vector3,te=new n.Vector3;function ie(e,t){return e.matrixWorld.decompose(ee,t,te),t}function ne(e){return[Math.atan2(-e.z,e.x),Math.atan2(e.y,Math.sqrt(e.x*e.x+e.z*e.z))]}function re(e){const t=Math.round(e/2/Math.PI);return e-2*Math.PI*t}const oe=new n.Vector3(0,0,1),se=new n.Vector3,ae=new n.Vector3,le=new n.Vector3,ue=new n.Quaternion,de=new n.Quaternion,he=new n.Quaternion,ce=new n.Quaternion,pe=new n.Euler;class me{get yaw(){return this._yaw}set yaw(e){this._yaw=e,this._needsUpdate=!0}get pitch(){return this._pitch}set pitch(e){this._pitch=e,this._needsUpdate=!0}get euler(){return console.warn("VRMLookAt: euler is deprecated. use getEuler() instead."),this.getEuler(new n.Euler)}constructor(e,t){this.offsetFromHeadBone=new n.Vector3,this.autoUpdate=!0,this.faceFront=new n.Vector3(0,0,1),this.humanoid=e,this.applier=t,this._yaw=0,this._pitch=0,this._needsUpdate=!0,this._restHeadWorldQuaternion=this.getLookAtWorldQuaternion(new n.Quaternion)}getEuler(e){return e.set(n.MathUtils.DEG2RAD*this._pitch,n.MathUtils.DEG2RAD*this._yaw,0,"YXZ")}copy(e){if(this.humanoid!==e.humanoid)throw new Error("VRMLookAt: humanoid must be same in order to copy");return this.offsetFromHeadBone.copy(e.offsetFromHeadBone),this.applier=e.applier,this.autoUpdate=e.autoUpdate,this.target=e.target,this.faceFront.copy(e.faceFront),this}clone(){return new me(this.humanoid,this.applier).copy(this)}reset(){this._yaw=0,this._pitch=0,this._needsUpdate=!0}getLookAtWorldPosition(e){const t=this.humanoid.getRawBoneNode("head");return e.copy(this.offsetFromHeadBone).applyMatrix4(t.matrixWorld)}getLookAtWorldQuaternion(e){return ie(this.humanoid.getRawBoneNode("head"),e)}getFaceFrontQuaternion(e){if(this.faceFront.distanceToSquared(oe)<.01)return e.copy(this._restHeadWorldQuaternion).invert();const[t,i]=ne(this.faceFront);return pe.set(0,.5*Math.PI+t,i,"YZX"),e.setFromEuler(pe).premultiply(ce.copy(this._restHeadWorldQuaternion).invert())}getLookAtWorldDirection(e){return this.getLookAtWorldQuaternion(de),this.getFaceFrontQuaternion(he),e.copy(oe).applyQuaternion(de).applyQuaternion(he).applyEuler(this.getEuler(pe))}lookAt(e){const t=ue.copy(this._restHeadWorldQuaternion).multiply(b(this.getLookAtWorldQuaternion(de))),i=this.getLookAtWorldPosition(ae),r=le.copy(e).sub(i).applyQuaternion(t).normalize(),[o,s]=ne(this.faceFront),[a,l]=ne(r),u=re(a-o),d=re(s-l);this._yaw=n.MathUtils.RAD2DEG*u,this._pitch=n.MathUtils.RAD2DEG*d,this._needsUpdate=!0}update(e){null!=this.target&&this.autoUpdate&&this.lookAt(this.target.getWorldPosition(se)),this._needsUpdate&&(this._needsUpdate=!1,this.applier.applyYawPitch(this._yaw,this._pitch))}}me.EULER_ORDER="YXZ";const fe=new n.Vector3(0,0,1),ge=new n.Quaternion,ve=new n.Quaternion,_e=new n.Euler(0,0,0,"YXZ");class Me{constructor(e,t,i,r,o){this.humanoid=e,this.rangeMapHorizontalInner=t,this.rangeMapHorizontalOuter=i,this.rangeMapVerticalDown=r,this.rangeMapVerticalUp=o,this.faceFront=new n.Vector3(0,0,1),this._restQuatLeftEye=new n.Quaternion,this._restQuatRightEye=new n.Quaternion,this._restLeftEyeParentWorldQuat=new n.Quaternion,this._restRightEyeParentWorldQuat=new n.Quaternion;const s=this.humanoid.getRawBoneNode("leftEye"),a=this.humanoid.getRawBoneNode("rightEye");s&&(this._restQuatLeftEye.copy(s.quaternion),ie(s.parent,this._restLeftEyeParentWorldQuat)),a&&(this._restQuatRightEye.copy(a.quaternion),ie(a.parent,this._restRightEyeParentWorldQuat))}applyYawPitch(e,t){const i=this.humanoid.getRawBoneNode("leftEye"),r=this.humanoid.getRawBoneNode("rightEye"),o=this.humanoid.getNormalizedBoneNode("leftEye"),s=this.humanoid.getNormalizedBoneNode("rightEye");i&&(_e.x=t<0?-n.MathUtils.DEG2RAD*this.rangeMapVerticalDown.map(-t):n.MathUtils.DEG2RAD*this.rangeMapVerticalUp.map(t),_e.y=e<0?-n.MathUtils.DEG2RAD*this.rangeMapHorizontalInner.map(-e):n.MathUtils.DEG2RAD*this.rangeMapHorizontalOuter.map(e),ge.setFromEuler(_e),this._getWorldFaceFrontQuat(ve),o.quaternion.copy(ve).multiply(ge).multiply(ve.invert()),ge.copy(this._restLeftEyeParentWorldQuat),i.quaternion.copy(o.quaternion).multiply(ge).premultiply(ge.invert()).multiply(this._restQuatLeftEye)),r&&(_e.x=t<0?-n.MathUtils.DEG2RAD*this.rangeMapVerticalDown.map(-t):n.MathUtils.DEG2RAD*this.rangeMapVerticalUp.map(t),_e.y=e<0?-n.MathUtils.DEG2RAD*this.rangeMapHorizontalOuter.map(-e):n.MathUtils.DEG2RAD*this.rangeMapHorizontalInner.map(e),ge.setFromEuler(_e),this._getWorldFaceFrontQuat(ve),s.quaternion.copy(ve).multiply(ge).multiply(ve.invert()),ge.copy(this._restRightEyeParentWorldQuat),r.quaternion.copy(s.quaternion).multiply(ge).premultiply(ge.invert()).multiply(this._restQuatRightEye))}lookAt(e){console.warn("VRMLookAtBoneApplier: lookAt() is deprecated. use apply() instead.");const t=n.MathUtils.RAD2DEG*e.y,i=n.MathUtils.RAD2DEG*e.x;this.applyYawPitch(t,i)}_getWorldFaceFrontQuat(e){if(this.faceFront.distanceToSquared(fe)<.01)return e.identity();const[t,i]=ne(this.faceFront);return _e.set(0,.5*Math.PI+t,i,"YZX"),e.setFromEuler(_e)}}Me.type="bone";class xe{constructor(e,t,i,n,r){this.expressions=e,this.rangeMapHorizontalInner=t,this.rangeMapHorizontalOuter=i,this.rangeMapVerticalDown=n,this.rangeMapVerticalUp=r}applyYawPitch(e,t){t<0?(this.expressions.setValue("lookDown",0),this.expressions.setValue("lookUp",this.rangeMapVerticalUp.map(-t))):(this.expressions.setValue("lookUp",0),this.expressions.setValue("lookDown",this.rangeMapVerticalDown.map(t))),e<0?(this.expressions.setValue("lookLeft",0),this.expressions.setValue("lookRight",this.rangeMapHorizontalOuter.map(-e))):(this.expressions.setValue("lookRight",0),this.expressions.setValue("lookLeft",this.rangeMapHorizontalOuter.map(e)))}lookAt(e){console.warn("VRMLookAtBoneApplier: lookAt() is deprecated. use apply() instead.");const t=n.MathUtils.RAD2DEG*e.y,i=n.MathUtils.RAD2DEG*e.x;this.applyYawPitch(t,i)}}xe.type="expression";class ye{constructor(e,t){this.inputMaxValue=e,this.outputScale=t}map(e){return this.outputScale*h(e/this.inputMaxValue)}}const Re=new Set(["1.0","1.0-beta"]),Te=.01;class Ee{get name(){return"VRMLookAtLoaderPlugin"}constructor(e,t){this.parser=e,this.helperRoot=null==t?void 0:t.helperRoot}afterRoot(e){return o(this,void 0,void 0,(function*(){const t=e.userData.vrmHumanoid;if(null===t)return;if(void 0===t)throw new Error("VRMLookAtLoaderPlugin: vrmHumanoid is undefined. VRMHumanoidLoaderPlugin have to be used first");const i=e.userData.vrmExpressionManager;if(null!==i){if(void 0===i)throw new Error("VRMLookAtLoaderPlugin: vrmExpressionManager is undefined. VRMExpressionLoaderPlugin have to be used first");e.userData.vrmLookAt=yield this._import(e,t,i)}}))}_import(e,t,i){return o(this,void 0,void 0,(function*(){if(null==t||null==i)return null;const n=yield this._v1Import(e,t,i);if(n)return n;const r=yield this._v0Import(e,t,i);return r||null}))}_v1Import(e,t,i){var n,r,s;return o(this,void 0,void 0,(function*(){const e=this.parser.json;if(!(-1!==(null===(n=e.extensionsUsed)||void 0===n?void 0:n.indexOf("VRMC_vrm"))))return null;const o=null===(r=e.extensions)||void 0===r?void 0:r.VRMC_vrm;if(!o)return null;const a=o.specVersion;if(!Re.has(a))return console.warn(`VRMLookAtLoaderPlugin: Unknown VRMC_vrm specVersion "${a}"`),null;const l=o.lookAt;if(!l)return null;const u="expression"===l.type?1:10,d=this._v1ImportRangeMap(l.rangeMapHorizontalInner,u),h=this._v1ImportRangeMap(l.rangeMapHorizontalOuter,u),c=this._v1ImportRangeMap(l.rangeMapVerticalDown,u),p=this._v1ImportRangeMap(l.rangeMapVerticalUp,u);let m;m="expression"===l.type?new xe(i,d,h,c,p):new Me(t,d,h,c,p);const f=this._importLookAt(t,m);return f.offsetFromHeadBone.fromArray(null!==(s=l.offsetFromHeadBone)&&void 0!==s?s:[0,.06,0]),f}))}_v1ImportRangeMap(e,t){var i,n;let r=null!==(i=null==e?void 0:e.inputMaxValue)&&void 0!==i?i:90;const o=null!==(n=null==e?void 0:e.outputScale)&&void 0!==n?n:t;return r<Te&&(console.warn("VRMLookAtLoaderPlugin: inputMaxValue of a range map is too small. Consider reviewing the range map!"),r=Te),new ye(r,o)}_v0Import(e,t,i){var n,r,s,a;return o(this,void 0,void 0,(function*(){const e=this.parser.json,o=null===(n=e.extensions)||void 0===n?void 0:n.VRM;if(!o)return null;const l=o.firstPerson;if(!l)return null;const u="BlendShape"===l.lookAtTypeName?1:10,d=this._v0ImportDegreeMap(l.lookAtHorizontalInner,u),h=this._v0ImportDegreeMap(l.lookAtHorizontalOuter,u),c=this._v0ImportDegreeMap(l.lookAtVerticalDown,u),p=this._v0ImportDegreeMap(l.lookAtVerticalUp,u);let m;m="BlendShape"===l.lookAtTypeName?new xe(i,d,h,c,p):new Me(t,d,h,c,p);const f=this._importLookAt(t,m);return l.firstPersonBoneOffset?f.offsetFromHeadBone.set(null!==(r=l.firstPersonBoneOffset.x)&&void 0!==r?r:0,null!==(s=l.firstPersonBoneOffset.y)&&void 0!==s?s:.06,-(null!==(a=l.firstPersonBoneOffset.z)&&void 0!==a?a:0)):f.offsetFromHeadBone.set(0,.06,0),f.faceFront.set(0,0,-1),m instanceof Me&&m.faceFront.set(0,0,-1),f}))}_v0ImportDegreeMap(e,t){var i,n;const r=null==e?void 0:e.curve;"[0,0,0,1,1,1,1,0]"!==JSON.stringify(r)&&console.warn("Curves of LookAtDegreeMap defined in VRM 0.0 are not supported");let o=null!==(i=null==e?void 0:e.xRange)&&void 0!==i?i:90;const s=null!==(n=null==e?void 0:e.yRange)&&void 0!==n?n:t;return o<Te&&(console.warn("VRMLookAtLoaderPlugin: xRange of a degree map is too small. Consider reviewing the degree map!"),o=Te),new ye(o,s)}_importLookAt(e,t){const i=new me(e,t);if(this.helperRoot){const e=new K(i);this.helperRoot.add(e),e.renderOrder=this.helperRoot.renderOrder}return i}}const we=new Set(["1.0","1.0-beta"]);class Pe{get name(){return"VRMMetaLoaderPlugin"}constructor(e,t){var i,n,r;this.parser=e,this.needThumbnailImage=null===(i=null==t?void 0:t.needThumbnailImage)||void 0===i||i,this.acceptLicenseUrls=null!==(n=null==t?void 0:t.acceptLicenseUrls)&&void 0!==n?n:["https://vrm.dev/licenses/1.0/"],this.acceptV0Meta=null===(r=null==t?void 0:t.acceptV0Meta)||void 0===r||r}afterRoot(e){return o(this,void 0,void 0,(function*(){e.userData.vrmMeta=yield this._import(e)}))}_import(e){return o(this,void 0,void 0,(function*(){const t=yield this._v1Import(e);if(null!=t)return t;const i=yield this._v0Import(e);return null!=i?i:null}))}_v1Import(e){var t,i,n;return o(this,void 0,void 0,(function*(){const e=this.parser.json;if(!(-1!==(null===(t=e.extensionsUsed)||void 0===t?void 0:t.indexOf("VRMC_vrm"))))return null;const r=null===(i=e.extensions)||void 0===i?void 0:i.VRMC_vrm;if(null==r)return null;const o=r.specVersion;if(!we.has(o))return console.warn(`VRMMetaLoaderPlugin: Unknown VRMC_vrm specVersion "${o}"`),null;const s=r.meta;if(!s)return null;const a=s.licenseUrl;if(!new Set(this.acceptLicenseUrls).has(a))throw new Error(`VRMMetaLoaderPlugin: The license url "${a}" is not accepted`);let l;return this.needThumbnailImage&&null!=s.thumbnailImage&&(l=null!==(n=yield this._extractGLTFImage(s.thumbnailImage))&&void 0!==n?n:void 0),{metaVersion:"1",name:s.name,version:s.version,authors:s.authors,copyrightInformation:s.copyrightInformation,contactInformation:s.contactInformation,references:s.references,thirdPartyLicenses:s.thirdPartyLicenses,thumbnailImage:l,licenseUrl:s.licenseUrl,avatarPermission:s.avatarPermission,allowExcessivelyViolentUsage:s.allowExcessivelyViolentUsage,allowExcessivelySexualUsage:s.allowExcessivelySexualUsage,commercialUsage:s.commercialUsage,allowPoliticalOrReligiousUsage:s.allowPoliticalOrReligiousUsage,allowAntisocialOrHateUsage:s.allowAntisocialOrHateUsage,creditNotation:s.creditNotation,allowRedistribution:s.allowRedistribution,modification:s.modification,otherLicenseUrl:s.otherLicenseUrl}}))}_v0Import(e){var t;return o(this,void 0,void 0,(function*(){const e=this.parser.json,i=null===(t=e.extensions)||void 0===t?void 0:t.VRM;if(!i)return null;const n=i.meta;if(!n)return null;if(!this.acceptV0Meta)throw new Error("VRMMetaLoaderPlugin: Attempted to load VRM0.0 meta but acceptV0Meta is false");let r;return this.needThumbnailImage&&null!=n.texture&&-1!==n.texture&&(r=yield this.parser.getDependency("texture",n.texture)),{metaVersion:"0",allowedUserName:n.allowedUserName,author:n.author,commercialUssageName:n.commercialUssageName,contactInformation:n.contactInformation,licenseName:n.licenseName,otherLicenseUrl:n.otherLicenseUrl,otherPermissionUrl:n.otherPermissionUrl,reference:n.reference,sexualUssageName:n.sexualUssageName,texture:null!=r?r:void 0,title:n.title,version:n.version,violentUssageName:n.violentUssageName}}))}_extractGLTFImage(e){var t;return o(this,void 0,void 0,(function*(){const i=this.parser.json,r=null===(t=i.images)||void 0===t?void 0:t[e];if(null==r)return console.warn(`VRMMetaLoaderPlugin: Attempt to use images[${e}] of glTF as a thumbnail but the image doesn't exist`),null;let o=r.uri;if(null!=r.bufferView){const e=yield this.parser.getDependency("bufferView",r.bufferView),t=new Blob([e],{type:r.mimeType});o=URL.createObjectURL(t)}if(null==o)return console.warn(`VRMMetaLoaderPlugin: Attempt to use images[${e}] of glTF as a thumbnail but the image couldn't load properly`),null;const s=new n.ImageLoader;return yield s.loadAsync((a=o,l=this.parser.options.path,"string"!=typeof a||""===a?"":(/^https?:\/\//i.test(l)&&/^\//.test(a)&&(l=l.replace(/(^https?:\/\/[^/]+).*/i,"$1")),/^(https?:)?\/\//i.test(a)||/^data:.*,.*$/i.test(a)||/^blob:.*$/i.test(a)?a:l+a))).catch((e=>(console.error(e),console.warn("VRMMetaLoaderPlugin: Failed to load a thumbnail image"),null)));var a,l}))}}class Se{constructor(e){this.scene=e.scene,this.meta=e.meta,this.humanoid=e.humanoid,this.expressionManager=e.expressionManager,this.firstPerson=e.firstPerson,this.lookAt=e.lookAt}update(e){this.humanoid.update(),this.lookAt&&this.lookAt.update(e),this.expressionManager&&this.expressionManager.update()}}class Le extends Se{constructor(e){super(e),this.materials=e.materials,this.springBoneManager=e.springBoneManager,this.nodeConstraintManager=e.nodeConstraintManager}update(e){super.update(e),this.nodeConstraintManager&&this.nodeConstraintManager.update(),this.springBoneManager&&this.springBoneManager.update(e),this.materials&&this.materials.forEach((t=>{t.update&&t.update(e)}))}}function Ae(e,t,i,n){return new(i||(i=Promise))((function(r,o){function s(e){try{l(n.next(e))}catch(e){o(e)}}function a(e){try{l(n.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(s,a)}l((n=n.apply(e,t||[])).next())}))}
/*!
     * @pixiv/three-vrm-materials-mtoon v2.0.6
     * MToon (toon material) module for @pixiv/three-vrm
     *
     * Copyright (c) 2020-2023 pixiv Inc.
     * @pixiv/three-vrm-materials-mtoon is distributed under MIT License
     * https://github.com/pixiv/three-vrm/blob/release/LICENSE
     */function Ie(e,t,i,n){return new(i||(i=Promise))((function(r,o){function s(e){try{l(n.next(e))}catch(e){o(e)}}function a(e){try{l(n.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(s,a)}l((n=n.apply(e,t||[])).next())}))}const be={None:"none",Normal:"normal",LitShadeRate:"litShadeRate",UV:"uv"},Ue={None:"none",WorldCoordinates:"worldCoordinates",ScreenCoordinates:"screenCoordinates"},Ve={3e3:"",3001:"srgb"};function Oe(e){return parseInt(n.REVISION,10)>=152?e.colorSpace:Ve[e.encoding]}class Ne extends n.ShaderMaterial{get color(){return this.uniforms.litFactor.value}set color(e){this.uniforms.litFactor.value=e}get map(){return this.uniforms.map.value}set map(e){this.uniforms.map.value=e}get normalMap(){return this.uniforms.normalMap.value}set normalMap(e){this.uniforms.normalMap.value=e}get normalScale(){return this.uniforms.normalScale.value}set normalScale(e){this.uniforms.normalScale.value=e}get emissive(){return this.uniforms.emissive.value}set emissive(e){this.uniforms.emissive.value=e}get emissiveIntensity(){return this.uniforms.emissiveIntensity.value}set emissiveIntensity(e){this.uniforms.emissiveIntensity.value=e}get emissiveMap(){return this.uniforms.emissiveMap.value}set emissiveMap(e){this.uniforms.emissiveMap.value=e}get shadeColorFactor(){return this.uniforms.shadeColorFactor.value}set shadeColorFactor(e){this.uniforms.shadeColorFactor.value=e}get shadeMultiplyTexture(){return this.uniforms.shadeMultiplyTexture.value}set shadeMultiplyTexture(e){this.uniforms.shadeMultiplyTexture.value=e}get shadingShiftFactor(){return this.uniforms.shadingShiftFactor.value}set shadingShiftFactor(e){this.uniforms.shadingShiftFactor.value=e}get shadingShiftTexture(){return this.uniforms.shadingShiftTexture.value}set shadingShiftTexture(e){this.uniforms.shadingShiftTexture.value=e}get shadingShiftTextureScale(){return this.uniforms.shadingShiftTextureScale.value}set shadingShiftTextureScale(e){this.uniforms.shadingShiftTextureScale.value=e}get shadingToonyFactor(){return this.uniforms.shadingToonyFactor.value}set shadingToonyFactor(e){this.uniforms.shadingToonyFactor.value=e}get giEqualizationFactor(){return this.uniforms.giEqualizationFactor.value}set giEqualizationFactor(e){this.uniforms.giEqualizationFactor.value=e}get matcapFactor(){return this.uniforms.matcapFactor.value}set matcapFactor(e){this.uniforms.matcapFactor.value=e}get matcapTexture(){return this.uniforms.matcapTexture.value}set matcapTexture(e){this.uniforms.matcapTexture.value=e}get parametricRimColorFactor(){return this.uniforms.parametricRimColorFactor.value}set parametricRimColorFactor(e){this.uniforms.parametricRimColorFactor.value=e}get rimMultiplyTexture(){return this.uniforms.rimMultiplyTexture.value}set rimMultiplyTexture(e){this.uniforms.rimMultiplyTexture.value=e}get rimLightingMixFactor(){return this.uniforms.rimLightingMixFactor.value}set rimLightingMixFactor(e){this.uniforms.rimLightingMixFactor.value=e}get parametricRimFresnelPowerFactor(){return this.uniforms.parametricRimFresnelPowerFactor.value}set parametricRimFresnelPowerFactor(e){this.uniforms.parametricRimFresnelPowerFactor.value=e}get parametricRimLiftFactor(){return this.uniforms.parametricRimLiftFactor.value}set parametricRimLiftFactor(e){this.uniforms.parametricRimLiftFactor.value=e}get outlineWidthMultiplyTexture(){return this.uniforms.outlineWidthMultiplyTexture.value}set outlineWidthMultiplyTexture(e){this.uniforms.outlineWidthMultiplyTexture.value=e}get outlineWidthFactor(){return this.uniforms.outlineWidthFactor.value}set outlineWidthFactor(e){this.uniforms.outlineWidthFactor.value=e}get outlineColorFactor(){return this.uniforms.outlineColorFactor.value}set outlineColorFactor(e){this.uniforms.outlineColorFactor.value=e}get outlineLightingMixFactor(){return this.uniforms.outlineLightingMixFactor.value}set outlineLightingMixFactor(e){this.uniforms.outlineLightingMixFactor.value=e}get uvAnimationMaskTexture(){return this.uniforms.uvAnimationMaskTexture.value}set uvAnimationMaskTexture(e){this.uniforms.uvAnimationMaskTexture.value=e}get uvAnimationScrollXOffset(){return this.uniforms.uvAnimationScrollXOffset.value}set uvAnimationScrollXOffset(e){this.uniforms.uvAnimationScrollXOffset.value=e}get uvAnimationScrollYOffset(){return this.uniforms.uvAnimationScrollYOffset.value}set uvAnimationScrollYOffset(e){this.uniforms.uvAnimationScrollYOffset.value=e}get uvAnimationRotationPhase(){return this.uniforms.uvAnimationRotationPhase.value}set uvAnimationRotationPhase(e){this.uniforms.uvAnimationRotationPhase.value=e}get ignoreVertexColor(){return this._ignoreVertexColor}set ignoreVertexColor(e){this._ignoreVertexColor=e,this.needsUpdate=!0}get v0CompatShade(){return this._v0CompatShade}set v0CompatShade(e){this._v0CompatShade=e,this.needsUpdate=!0}get debugMode(){return this._debugMode}set debugMode(e){this._debugMode=e,this.needsUpdate=!0}get outlineWidthMode(){return this._outlineWidthMode}set outlineWidthMode(e){this._outlineWidthMode=e,this.needsUpdate=!0}get isOutline(){return this._isOutline}set isOutline(e){this._isOutline=e,this.needsUpdate=!0}get isMToonMaterial(){return!0}constructor(e={}){super({vertexShader:"// #define PHONG\n\nvarying vec3 vViewPosition;\n\n#ifndef FLAT_SHADED\n  varying vec3 vNormal;\n#endif\n\n#include <common>\n\n// #include <uv_pars_vertex>\n#ifdef MTOON_USE_UV\n  varying vec2 vUv;\n\n  // COMPAT: pre-r151 uses a common uvTransform\n  #if THREE_VRM_THREE_REVISION < 151\n    uniform mat3 uvTransform;\n  #endif\n#endif\n\n// #include <uv2_pars_vertex>\n// COMAPT: pre-r151 uses uv2 for lightMap and aoMap\n#if THREE_VRM_THREE_REVISION < 151\n  #if defined( USE_LIGHTMAP ) || defined( USE_AOMAP )\n    attribute vec2 uv2;\n    varying vec2 vUv2;\n    uniform mat3 uv2Transform;\n  #endif\n#endif\n\n// #include <displacementmap_pars_vertex>\n// #include <envmap_pars_vertex>\n#include <color_pars_vertex>\n#include <fog_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <shadowmap_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\n\n#ifdef USE_OUTLINEWIDTHMULTIPLYTEXTURE\n  uniform sampler2D outlineWidthMultiplyTexture;\n  uniform mat3 outlineWidthMultiplyTextureUvTransform;\n#endif\n\nuniform float outlineWidthFactor;\n\nvoid main() {\n\n  // #include <uv_vertex>\n  #ifdef MTOON_USE_UV\n    // COMPAT: pre-r151 uses a common uvTransform\n    #if THREE_VRM_THREE_REVISION >= 151\n      vUv = uv;\n    #else\n      vUv = ( uvTransform * vec3( uv, 1 ) ).xy;\n    #endif\n  #endif\n\n  // #include <uv2_vertex>\n  // COMAPT: pre-r151 uses uv2 for lightMap and aoMap\n  #if THREE_VRM_THREE_REVISION < 151\n    #if defined( USE_LIGHTMAP ) || defined( USE_AOMAP )\n      vUv2 = ( uv2Transform * vec3( uv2, 1 ) ).xy;\n    #endif\n  #endif\n\n  #include <color_vertex>\n\n  #include <beginnormal_vertex>\n  #include <morphnormal_vertex>\n  #include <skinbase_vertex>\n  #include <skinnormal_vertex>\n\n  // we need this to compute the outline properly\n  objectNormal = normalize( objectNormal );\n\n  #include <defaultnormal_vertex>\n\n  #ifndef FLAT_SHADED // Normal computed with derivatives when FLAT_SHADED\n    vNormal = normalize( transformedNormal );\n  #endif\n\n  #include <begin_vertex>\n\n  #include <morphtarget_vertex>\n  #include <skinning_vertex>\n  // #include <displacementmap_vertex>\n  #include <project_vertex>\n  #include <logdepthbuf_vertex>\n  #include <clipping_planes_vertex>\n\n  vViewPosition = - mvPosition.xyz;\n\n  float outlineTex = 1.0;\n\n  #ifdef OUTLINE\n    #ifdef USE_OUTLINEWIDTHMULTIPLYTEXTURE\n      vec2 outlineWidthMultiplyTextureUv = ( outlineWidthMultiplyTextureUvTransform * vec3( vUv, 1 ) ).xy;\n      outlineTex = texture2D( outlineWidthMultiplyTexture, outlineWidthMultiplyTextureUv ).g;\n    #endif\n\n    #ifdef OUTLINE_WIDTH_WORLD\n      float worldNormalLength = length( transformedNormal );\n      vec3 outlineOffset = outlineWidthFactor * outlineTex * worldNormalLength * objectNormal;\n      gl_Position = projectionMatrix * modelViewMatrix * vec4( outlineOffset + transformed, 1.0 );\n    #endif\n\n    #ifdef OUTLINE_WIDTH_SCREEN\n      vec3 clipNormal = ( projectionMatrix * modelViewMatrix * vec4( objectNormal, 0.0 ) ).xyz;\n      vec2 projectedNormal = normalize( clipNormal.xy );\n      projectedNormal.x *= projectionMatrix[ 0 ].x / projectionMatrix[ 1 ].y;\n      gl_Position.xy += 2.0 * outlineWidthFactor * outlineTex * projectedNormal.xy;\n    #endif\n\n    gl_Position.z += 1E-6 * gl_Position.w; // anti-artifact magic\n  #endif\n\n  #include <worldpos_vertex>\n  // #include <envmap_vertex>\n  #include <shadowmap_vertex>\n  #include <fog_vertex>\n\n}",fragmentShader:'// #define PHONG\n\nuniform vec3 litFactor;\n\nuniform float opacity;\n\nuniform vec3 shadeColorFactor;\n#ifdef USE_SHADEMULTIPLYTEXTURE\n  uniform sampler2D shadeMultiplyTexture;\n  uniform mat3 shadeMultiplyTextureUvTransform;\n#endif\n\nuniform float shadingShiftFactor;\nuniform float shadingToonyFactor;\n\n#ifdef USE_SHADINGSHIFTTEXTURE\n  uniform sampler2D shadingShiftTexture;\n  uniform mat3 shadingShiftTextureUvTransform;\n  uniform float shadingShiftTextureScale;\n#endif\n\nuniform float giEqualizationFactor;\n\nuniform vec3 parametricRimColorFactor;\n#ifdef USE_RIMMULTIPLYTEXTURE\n  uniform sampler2D rimMultiplyTexture;\n  uniform mat3 rimMultiplyTextureUvTransform;\n#endif\nuniform float rimLightingMixFactor;\nuniform float parametricRimFresnelPowerFactor;\nuniform float parametricRimLiftFactor;\n\n#ifdef USE_MATCAPTEXTURE\n  uniform vec3 matcapFactor;\n  uniform sampler2D matcapTexture;\n  uniform mat3 matcapTextureUvTransform;\n#endif\n\nuniform vec3 emissive;\nuniform float emissiveIntensity;\n\nuniform vec3 outlineColorFactor;\nuniform float outlineLightingMixFactor;\n\n#ifdef USE_UVANIMATIONMASKTEXTURE\n  uniform sampler2D uvAnimationMaskTexture;\n  uniform mat3 uvAnimationMaskTextureUvTransform;\n#endif\n\nuniform float uvAnimationScrollXOffset;\nuniform float uvAnimationScrollYOffset;\nuniform float uvAnimationRotationPhase;\n\n#include <common>\n#include <packing>\n#include <dithering_pars_fragment>\n#include <color_pars_fragment>\n\n// #include <uv_pars_fragment>\n#if ( defined( MTOON_USE_UV ) && !defined( MTOON_UVS_VERTEX_ONLY ) )\n  varying vec2 vUv;\n#endif\n\n// #include <uv2_pars_fragment>\n// COMAPT: pre-r151 uses uv2 for lightMap and aoMap\n#if THREE_VRM_THREE_REVISION < 151\n  #if defined( USE_LIGHTMAP ) || defined( USE_AOMAP )\n    varying vec2 vUv2;\n  #endif\n#endif\n\n#include <map_pars_fragment>\n\n#ifdef USE_MAP\n  uniform mat3 mapUvTransform;\n#endif\n\n// #include <alphamap_pars_fragment>\n\n#if THREE_VRM_THREE_REVISION >= 132\n  #include <alphatest_pars_fragment>\n#endif\n\n#include <aomap_pars_fragment>\n// #include <lightmap_pars_fragment>\n#include <emissivemap_pars_fragment>\n\n#ifdef USE_EMISSIVEMAP\n  uniform mat3 emissiveMapUvTransform;\n#endif\n\n// #include <envmap_common_pars_fragment>\n// #include <envmap_pars_fragment>\n// #include <cube_uv_reflection_fragment>\n#include <fog_pars_fragment>\n\n// #include <bsdfs>\n// COMPAT: pre-r151 doesn\'t have BRDF_Lambert in <common>\n#if THREE_VRM_THREE_REVISION < 151\n  vec3 BRDF_Lambert( const in vec3 diffuseColor ) {\n    return RECIPROCAL_PI * diffuseColor;\n  }\n#endif\n\n#include <lights_pars_begin>\n\n#if THREE_VRM_THREE_REVISION >= 132\n  #include <normal_pars_fragment>\n#endif\n\n// #include <lights_phong_pars_fragment>\nvarying vec3 vViewPosition;\n\n#if THREE_VRM_THREE_REVISION < 132\n  #ifndef FLAT_SHADED\n    varying vec3 vNormal;\n  #endif\n#endif\n\nstruct MToonMaterial {\n  vec3 diffuseColor;\n  vec3 shadeColor;\n  float shadingShift;\n};\n\nfloat linearstep( float a, float b, float t ) {\n  return clamp( ( t - a ) / ( b - a ), 0.0, 1.0 );\n}\n\n/**\n * Convert NdotL into toon shading factor using shadingShift and shadingToony\n */\nfloat getShading(\n  const in float dotNL,\n  const in float shadow,\n  const in float shadingShift\n) {\n  float shading = dotNL;\n  shading = shading + shadingShift;\n  shading = linearstep( -1.0 + shadingToonyFactor, 1.0 - shadingToonyFactor, shading );\n  shading *= shadow;\n  return shading;\n}\n\n/**\n * Mix diffuseColor and shadeColor using shading factor and light color\n */\nvec3 getDiffuse(\n  const in MToonMaterial material,\n  const in float shading,\n  in vec3 lightColor\n) {\n  #ifdef DEBUG_LITSHADERATE\n    return vec3( BRDF_Lambert( shading * lightColor ) );\n  #endif\n\n  #if THREE_VRM_THREE_REVISION < 132\n    #ifndef PHYSICALLY_CORRECT_LIGHTS\n      lightColor *= PI;\n    #endif\n  #endif\n\n  vec3 col = lightColor * BRDF_Lambert( mix( material.shadeColor, material.diffuseColor, shading ) );\n\n  // The "comment out if you want to PBR absolutely" line\n  #ifdef V0_COMPAT_SHADE\n    col = min( col, material.diffuseColor );\n  #endif\n\n  return col;\n}\n\n#if THREE_VRM_THREE_REVISION >= 157\n  void RE_Direct_MToon( const in IncidentLight directLight, const in vec3 geometryPosition, const in vec3 geometryNormal, const in vec3 geometryViewDir, const in vec3 geometryClearcoatNormal, const in MToonMaterial material, const in float shadow, inout ReflectedLight reflectedLight ) {\n    float dotNL = clamp( dot( geometryNormal, directLight.direction ), -1.0, 1.0 );\n    vec3 irradiance = directLight.color;\n\n    #if THREE_VRM_THREE_REVISION < 132\n      #ifndef PHYSICALLY_CORRECT_LIGHTS\n        irradiance *= PI;\n      #endif\n    #endif\n\n    // directSpecular will be used for rim lighting, not an actual specular\n    reflectedLight.directSpecular += irradiance;\n\n    irradiance *= dotNL;\n\n    float shading = getShading( dotNL, shadow, material.shadingShift );\n\n    // toon shaded diffuse\n    reflectedLight.directDiffuse += getDiffuse( material, shading, directLight.color );\n  }\n\n  void RE_IndirectDiffuse_MToon( const in vec3 irradiance, const in vec3 geometryPosition, const in vec3 geometryNormal, const in vec3 geometryViewDir, const in vec3 geometryClearcoatNormal, const in MToonMaterial material, inout ReflectedLight reflectedLight ) {\n    // indirect diffuse will use diffuseColor, no shadeColor involved\n    reflectedLight.indirectDiffuse += irradiance * BRDF_Lambert( material.diffuseColor );\n\n    // directSpecular will be used for rim lighting, not an actual specular\n    reflectedLight.directSpecular += irradiance;\n  }\n#else\n  void RE_Direct_MToon( const in IncidentLight directLight, const in GeometricContext geometry, const in MToonMaterial material, const in float shadow, inout ReflectedLight reflectedLight ) {\n    float dotNL = clamp( dot( geometry.normal, directLight.direction ), -1.0, 1.0 );\n    vec3 irradiance = directLight.color;\n\n    #if THREE_VRM_THREE_REVISION < 132\n      #ifndef PHYSICALLY_CORRECT_LIGHTS\n        irradiance *= PI;\n      #endif\n    #endif\n\n    // directSpecular will be used for rim lighting, not an actual specular\n    reflectedLight.directSpecular += irradiance;\n\n    irradiance *= dotNL;\n\n    float shading = getShading( dotNL, shadow, material.shadingShift );\n\n    // toon shaded diffuse\n    reflectedLight.directDiffuse += getDiffuse( material, shading, directLight.color );\n  }\n\n  void RE_IndirectDiffuse_MToon( const in vec3 irradiance, const in GeometricContext geometry, const in MToonMaterial material, inout ReflectedLight reflectedLight ) {\n    // indirect diffuse will use diffuseColor, no shadeColor involved\n    reflectedLight.indirectDiffuse += irradiance * BRDF_Lambert( material.diffuseColor );\n\n    // directSpecular will be used for rim lighting, not an actual specular\n    reflectedLight.directSpecular += irradiance;\n  }\n#endif\n\n#define RE_Direct RE_Direct_MToon\n#define RE_IndirectDiffuse RE_IndirectDiffuse_MToon\n#define Material_LightProbeLOD( material ) (0)\n\n#include <shadowmap_pars_fragment>\n// #include <bumpmap_pars_fragment>\n\n// #include <normalmap_pars_fragment>\n#ifdef USE_NORMALMAP\n\n  uniform sampler2D normalMap;\n  uniform mat3 normalMapUvTransform;\n  uniform vec2 normalScale;\n\n#endif\n\n// COMPAT: USE_NORMALMAP_OBJECTSPACE used to be OBJECTSPACE_NORMALMAP in pre-r151\n#if defined( USE_NORMALMAP_OBJECTSPACE ) || defined( OBJECTSPACE_NORMALMAP )\n\n  uniform mat3 normalMatrix;\n\n#endif\n\n// COMPAT: USE_NORMALMAP_TANGENTSPACE used to be TANGENTSPACE_NORMALMAP in pre-r151\n#if ! defined ( USE_TANGENT ) && ( defined ( USE_NORMALMAP_TANGENTSPACE ) || defined ( TANGENTSPACE_NORMALMAP ) )\n\n  // Per-Pixel Tangent Space Normal Mapping\n  // http://hacksoflife.blogspot.ch/2009/11/per-pixel-tangent-space-normal-mapping.html\n\n  // three-vrm specific change: it requires `uv` as an input in order to support uv scrolls\n\n  // Temporary compat against shader change @ Three.js r126, r151\n  #if THREE_VRM_THREE_REVISION >= 151\n\n    mat3 getTangentFrame( vec3 eye_pos, vec3 surf_norm, vec2 uv ) {\n\n      vec3 q0 = dFdx( eye_pos.xyz );\n      vec3 q1 = dFdy( eye_pos.xyz );\n      vec2 st0 = dFdx( uv.st );\n      vec2 st1 = dFdy( uv.st );\n\n      vec3 N = surf_norm;\n\n      vec3 q1perp = cross( q1, N );\n      vec3 q0perp = cross( N, q0 );\n\n      vec3 T = q1perp * st0.x + q0perp * st1.x;\n      vec3 B = q1perp * st0.y + q0perp * st1.y;\n\n      float det = max( dot( T, T ), dot( B, B ) );\n      float scale = ( det == 0.0 ) ? 0.0 : inversesqrt( det );\n\n      return mat3( T * scale, B * scale, N );\n\n    }\n\n  #elif THREE_VRM_THREE_REVISION >= 126\n\n    vec3 perturbNormal2Arb( vec2 uv, vec3 eye_pos, vec3 surf_norm, vec3 mapN, float faceDirection ) {\n\n      vec3 q0 = vec3( dFdx( eye_pos.x ), dFdx( eye_pos.y ), dFdx( eye_pos.z ) );\n      vec3 q1 = vec3( dFdy( eye_pos.x ), dFdy( eye_pos.y ), dFdy( eye_pos.z ) );\n      vec2 st0 = dFdx( uv.st );\n      vec2 st1 = dFdy( uv.st );\n\n      vec3 N = normalize( surf_norm );\n\n      vec3 q1perp = cross( q1, N );\n      vec3 q0perp = cross( N, q0 );\n\n      vec3 T = q1perp * st0.x + q0perp * st1.x;\n      vec3 B = q1perp * st0.y + q0perp * st1.y;\n\n      // three-vrm specific change: Workaround for the issue that happens when delta of uv = 0.0\n      // TODO: Is this still required? Or shall I make a PR about it?\n      if ( length( T ) == 0.0 || length( B ) == 0.0 ) {\n        return surf_norm;\n      }\n\n      float det = max( dot( T, T ), dot( B, B ) );\n      float scale = ( det == 0.0 ) ? 0.0 : faceDirection * inversesqrt( det );\n\n      return normalize( T * ( mapN.x * scale ) + B * ( mapN.y * scale ) + N * mapN.z );\n\n    }\n\n  #else\n\n    vec3 perturbNormal2Arb( vec2 uv, vec3 eye_pos, vec3 surf_norm, vec3 mapN ) {\n\n      // Workaround for Adreno 3XX dFd*( vec3 ) bug. See #9988\n\n      vec3 q0 = vec3( dFdx( eye_pos.x ), dFdx( eye_pos.y ), dFdx( eye_pos.z ) );\n      vec3 q1 = vec3( dFdy( eye_pos.x ), dFdy( eye_pos.y ), dFdy( eye_pos.z ) );\n      vec2 st0 = dFdx( uv.st );\n      vec2 st1 = dFdy( uv.st );\n\n      float scale = sign( st1.t * st0.s - st0.t * st1.s ); // we do not care about the magnitude\n\n      vec3 S = ( q0 * st1.t - q1 * st0.t ) * scale;\n      vec3 T = ( - q0 * st1.s + q1 * st0.s ) * scale;\n\n      // three-vrm specific change: Workaround for the issue that happens when delta of uv = 0.0\n      // TODO: Is this still required? Or shall I make a PR about it?\n\n      if ( length( S ) == 0.0 || length( T ) == 0.0 ) {\n        return surf_norm;\n      }\n\n      S = normalize( S );\n      T = normalize( T );\n      vec3 N = normalize( surf_norm );\n\n      #ifdef DOUBLE_SIDED\n\n        // Workaround for Adreno GPUs gl_FrontFacing bug. See #15850 and #10331\n\n        bool frontFacing = dot( cross( S, T ), N ) > 0.0;\n\n        mapN.xy *= ( float( frontFacing ) * 2.0 - 1.0 );\n\n      #else\n\n        mapN.xy *= ( float( gl_FrontFacing ) * 2.0 - 1.0 );\n\n      #endif\n\n      mat3 tsn = mat3( S, T, N );\n      return normalize( tsn * mapN );\n\n    }\n\n  #endif\n\n#endif\n\n// #include <specularmap_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\n\n// == post correction ==========================================================\nvoid postCorrection() {\n  #include <tonemapping_fragment>\n  #include <colorspace_fragment>\n  #include <fog_fragment>\n  #include <premultiplied_alpha_fragment>\n  #include <dithering_fragment>\n}\n\n// == main procedure ===========================================================\nvoid main() {\n  #include <clipping_planes_fragment>\n\n  vec2 uv = vec2(0.5, 0.5);\n\n  #if ( defined( MTOON_USE_UV ) && !defined( MTOON_UVS_VERTEX_ONLY ) )\n    uv = vUv;\n\n    float uvAnimMask = 1.0;\n    #ifdef USE_UVANIMATIONMASKTEXTURE\n      vec2 uvAnimationMaskTextureUv = ( uvAnimationMaskTextureUvTransform * vec3( uv, 1 ) ).xy;\n      uvAnimMask = texture2D( uvAnimationMaskTexture, uvAnimationMaskTextureUv ).b;\n    #endif\n\n    uv = uv + vec2( uvAnimationScrollXOffset, uvAnimationScrollYOffset ) * uvAnimMask;\n    float uvRotCos = cos( uvAnimationRotationPhase * uvAnimMask );\n    float uvRotSin = sin( uvAnimationRotationPhase * uvAnimMask );\n    uv = mat2( uvRotCos, -uvRotSin, uvRotSin, uvRotCos ) * ( uv - 0.5 ) + 0.5;\n  #endif\n\n  #ifdef DEBUG_UV\n    gl_FragColor = vec4( 0.0, 0.0, 0.0, 1.0 );\n    #if ( defined( MTOON_USE_UV ) && !defined( MTOON_UVS_VERTEX_ONLY ) )\n      gl_FragColor = vec4( uv, 0.0, 1.0 );\n    #endif\n    return;\n  #endif\n\n  vec4 diffuseColor = vec4( litFactor, opacity );\n  ReflectedLight reflectedLight = ReflectedLight( vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ) );\n  vec3 totalEmissiveRadiance = emissive * emissiveIntensity;\n\n  #include <logdepthbuf_fragment>\n\n  // #include <map_fragment>\n  #ifdef USE_MAP\n    vec2 mapUv = ( mapUvTransform * vec3( uv, 1 ) ).xy;\n    vec4 sampledDiffuseColor = texture2D( map, mapUv );\n    #ifdef DECODE_VIDEO_TEXTURE\n      sampledDiffuseColor = vec4( mix( pow( sampledDiffuseColor.rgb * 0.9478672986 + vec3( 0.0521327014 ), vec3( 2.4 ) ), sampledDiffuseColor.rgb * 0.0773993808, vec3( lessThanEqual( sampledDiffuseColor.rgb, vec3( 0.04045 ) ) ) ), sampledDiffuseColor.w );\n    #endif\n    diffuseColor *= sampledDiffuseColor;\n  #endif\n\n  // #include <color_fragment>\n  #if ( defined( USE_COLOR ) && !defined( IGNORE_VERTEX_COLOR ) )\n    diffuseColor.rgb *= vColor;\n  #endif\n\n  // #include <alphamap_fragment>\n\n  #include <alphatest_fragment>\n\n  // #include <specularmap_fragment>\n\n  // #include <normal_fragment_begin>\n  float faceDirection = gl_FrontFacing ? 1.0 : -1.0;\n\n  #ifdef FLAT_SHADED\n\n    vec3 fdx = dFdx( vViewPosition );\n    vec3 fdy = dFdy( vViewPosition );\n    vec3 normal = normalize( cross( fdx, fdy ) );\n\n  #else\n\n    vec3 normal = normalize( vNormal );\n\n    #ifdef DOUBLE_SIDED\n\n      normal *= faceDirection;\n\n    #endif\n\n  #endif\n\n  #ifdef USE_NORMALMAP\n\n    vec2 normalMapUv = ( normalMapUvTransform * vec3( uv, 1 ) ).xy;\n\n  #endif\n\n  #ifdef USE_NORMALMAP_TANGENTSPACE\n\n    #ifdef USE_TANGENT\n\n      mat3 tbn = mat3( normalize( vTangent ), normalize( vBitangent ), normal );\n\n    #else\n\n      mat3 tbn = getTangentFrame( - vViewPosition, normal, normalMapUv );\n\n    #endif\n\n    #if defined( DOUBLE_SIDED ) && ! defined( FLAT_SHADED )\n\n      tbn[0] *= faceDirection;\n      tbn[1] *= faceDirection;\n\n    #endif\n\n  #endif\n\n  #ifdef USE_CLEARCOAT_NORMALMAP\n\n    #ifdef USE_TANGENT\n\n      mat3 tbn2 = mat3( normalize( vTangent ), normalize( vBitangent ), normal );\n\n    #else\n\n      mat3 tbn2 = getTangentFrame( - vViewPosition, normal, vClearcoatNormalMapUv );\n\n    #endif\n\n    #if defined( DOUBLE_SIDED ) && ! defined( FLAT_SHADED )\n\n      tbn2[0] *= faceDirection;\n      tbn2[1] *= faceDirection;\n\n    #endif\n\n  #endif\n\n  // non perturbed normal for clearcoat among others\n\n  vec3 nonPerturbedNormal = normal;\n\n  #ifdef OUTLINE\n    normal *= -1.0;\n  #endif\n\n  // #include <normal_fragment_maps>\n\n  // COMPAT: USE_NORMALMAP_OBJECTSPACE used to be OBJECTSPACE_NORMALMAP in pre-r151\n  #if defined( USE_NORMALMAP_OBJECTSPACE ) || defined( OBJECTSPACE_NORMALMAP )\n\n    normal = texture2D( normalMap, normalMapUv ).xyz * 2.0 - 1.0; // overrides both flatShading and attribute normals\n\n    #ifdef FLIP_SIDED\n\n      normal = - normal;\n\n    #endif\n\n    #ifdef DOUBLE_SIDED\n\n      // Temporary compat against shader change @ Three.js r126\n      // See: #21205, #21307, #21299\n      #if THREE_VRM_THREE_REVISION >= 126\n\n        normal = normal * faceDirection;\n\n      #else\n\n        normal = normal * ( float( gl_FrontFacing ) * 2.0 - 1.0 );\n\n      #endif\n\n    #endif\n\n    normal = normalize( normalMatrix * normal );\n\n  // COMPAT: USE_NORMALMAP_TANGENTSPACE used to be TANGENTSPACE_NORMALMAP in pre-r151\n  #elif defined( USE_NORMALMAP_TANGENTSPACE ) || defined( TANGENTSPACE_NORMALMAP )\n\n    vec3 mapN = texture2D( normalMap, normalMapUv ).xyz * 2.0 - 1.0;\n    mapN.xy *= normalScale;\n\n    // COMPAT: pre-r151\n    #if THREE_VRM_THREE_REVISION >= 151 || defined( USE_TANGENT )\n\n      normal = normalize( tbn * mapN );\n\n    #else\n\n      // pre-r126\n      #if THREE_VRM_THREE_REVISION >= 126\n\n        normal = perturbNormal2Arb( uv, -vViewPosition, normal, mapN, faceDirection );\n\n      #else\n\n        normal = perturbNormal2Arb( uv, -vViewPosition, normal, mapN );\n\n      #endif\n\n    #endif\n\n  #endif\n\n  // #include <emissivemap_fragment>\n  #ifdef USE_EMISSIVEMAP\n    vec2 emissiveMapUv = ( emissiveMapUvTransform * vec3( uv, 1 ) ).xy;\n    totalEmissiveRadiance *= texture2D( emissiveMap, emissiveMapUv ).rgb;\n  #endif\n\n  #ifdef DEBUG_NORMAL\n    gl_FragColor = vec4( 0.5 + 0.5 * normal, 1.0 );\n    return;\n  #endif\n\n  // -- MToon: lighting --------------------------------------------------------\n  // accumulation\n  // #include <lights_phong_fragment>\n  MToonMaterial material;\n\n  material.diffuseColor = diffuseColor.rgb;\n\n  material.shadeColor = shadeColorFactor;\n  #ifdef USE_SHADEMULTIPLYTEXTURE\n    vec2 shadeMultiplyTextureUv = ( shadeMultiplyTextureUvTransform * vec3( uv, 1 ) ).xy;\n    material.shadeColor *= texture2D( shadeMultiplyTexture, shadeMultiplyTextureUv ).rgb;\n  #endif\n\n  #if ( defined( USE_COLOR ) && !defined( IGNORE_VERTEX_COLOR ) )\n    material.shadeColor.rgb *= vColor;\n  #endif\n\n  material.shadingShift = shadingShiftFactor;\n  #ifdef USE_SHADINGSHIFTTEXTURE\n    vec2 shadingShiftTextureUv = ( shadingShiftTextureUvTransform * vec3( uv, 1 ) ).xy;\n    material.shadingShift += texture2D( shadingShiftTexture, shadingShiftTextureUv ).r * shadingShiftTextureScale;\n  #endif\n\n  // #include <lights_fragment_begin>\n\n  // MToon Specific changes:\n  // Since we want to take shadows into account of shading instead of irradiance,\n  // we had to modify the codes that multiplies the results of shadowmap into color of direct lights.\n\n  #if THREE_VRM_THREE_REVISION >= 157\n    vec3 geometryPosition = - vViewPosition;\n    vec3 geometryNormal = normal;\n    vec3 geometryViewDir = ( isOrthographic ) ? vec3( 0, 0, 1 ) : normalize( vViewPosition );\n    \n    vec3 geometryClearcoatNormal;\n\n    #ifdef USE_CLEARCOAT\n\n      geometryClearcoatNormal = clearcoatNormal;\n\n    #endif\n  #else\n    GeometricContext geometry;\n\n    geometry.position = - vViewPosition;\n    geometry.normal = normal;\n    geometry.viewDir = ( isOrthographic ) ? vec3( 0, 0, 1 ) : normalize( vViewPosition );\n\n    #ifdef USE_CLEARCOAT\n\n      geometry.clearcoatNormal = clearcoatNormal;\n\n    #endif\n  #endif\n\n  IncidentLight directLight;\n\n  // since these variables will be used in unrolled loop, we have to define in prior\n  float shadow;\n\n  #if ( NUM_POINT_LIGHTS > 0 ) && defined( RE_Direct )\n\n    PointLight pointLight;\n    #if defined( USE_SHADOWMAP ) && NUM_POINT_LIGHT_SHADOWS > 0\n    PointLightShadow pointLightShadow;\n    #endif\n\n    #pragma unroll_loop_start\n    for ( int i = 0; i < NUM_POINT_LIGHTS; i ++ ) {\n\n      pointLight = pointLights[ i ];\n\n      #if THREE_VRM_THREE_REVISION >= 157\n        getPointLightInfo( pointLight, geometryPosition, directLight );\n      #elif THREE_VRM_THREE_REVISION >= 132\n        getPointLightInfo( pointLight, geometry, directLight );\n      #else\n        getPointDirectLightIrradiance( pointLight, geometry, directLight );\n      #endif\n\n      shadow = 1.0;\n      #if defined( USE_SHADOWMAP ) && ( UNROLLED_LOOP_INDEX < NUM_POINT_LIGHT_SHADOWS )\n      pointLightShadow = pointLightShadows[ i ];\n      shadow = all( bvec2( directLight.visible, receiveShadow ) ) ? getPointShadow( pointShadowMap[ i ], pointLightShadow.shadowMapSize, pointLightShadow.shadowBias, pointLightShadow.shadowRadius, vPointShadowCoord[ i ], pointLightShadow.shadowCameraNear, pointLightShadow.shadowCameraFar ) : 1.0;\n      #endif\n\n      #if THREE_VRM_THREE_REVISION >= 157\n        RE_Direct( directLight, geometryPosition, geometryNormal, geometryViewDir, geometryClearcoatNormal, material, shadow, reflectedLight );\n      #else\n        RE_Direct( directLight, geometry, material, shadow, reflectedLight );\n      #endif\n\n    }\n    #pragma unroll_loop_end\n\n  #endif\n\n  #if ( NUM_SPOT_LIGHTS > 0 ) && defined( RE_Direct )\n\n    SpotLight spotLight;\n    #if defined( USE_SHADOWMAP ) && NUM_SPOT_LIGHT_SHADOWS > 0\n    SpotLightShadow spotLightShadow;\n    #endif\n\n    #pragma unroll_loop_start\n    for ( int i = 0; i < NUM_SPOT_LIGHTS; i ++ ) {\n\n      spotLight = spotLights[ i ];\n\n      #if THREE_VRM_THREE_REVISION >= 157\n        getSpotLightInfo( spotLight, geometryPosition, directLight );\n      #elif THREE_VRM_THREE_REVISION >= 132\n        getSpotLightInfo( spotLight, geometry, directLight );\n      #else\n        getSpotDirectLightIrradiance( spotLight, geometry, directLight );\n      #endif\n\n      shadow = 1.0;\n      #if defined( USE_SHADOWMAP ) && ( UNROLLED_LOOP_INDEX < NUM_SPOT_LIGHT_SHADOWS )\n      spotLightShadow = spotLightShadows[ i ];\n      shadow = all( bvec2( directLight.visible, receiveShadow ) ) ? getShadow( spotShadowMap[ i ], spotLightShadow.shadowMapSize, spotLightShadow.shadowBias, spotLightShadow.shadowRadius, vSpotShadowCoord[ i ] ) : 1.0;\n      #endif\n\n      #if THREE_VRM_THREE_REVISION >= 157\n        RE_Direct( directLight, geometryPosition, geometryNormal, geometryViewDir, geometryClearcoatNormal, material, shadow, reflectedLight );\n      #else\n        RE_Direct( directLight, geometry, material, shadow, reflectedLight );\n      #endif\n\n    }\n    #pragma unroll_loop_end\n\n  #endif\n\n  #if ( NUM_DIR_LIGHTS > 0 ) && defined( RE_Direct )\n\n    DirectionalLight directionalLight;\n    #if defined( USE_SHADOWMAP ) && NUM_DIR_LIGHT_SHADOWS > 0\n    DirectionalLightShadow directionalLightShadow;\n    #endif\n\n    #pragma unroll_loop_start\n    for ( int i = 0; i < NUM_DIR_LIGHTS; i ++ ) {\n\n      directionalLight = directionalLights[ i ];\n\n      #if THREE_VRM_THREE_REVISION >= 157\n        getDirectionalLightInfo( directionalLight, directLight );\n      #elif THREE_VRM_THREE_REVISION >= 132\n        getDirectionalLightInfo( directionalLight, geometry, directLight );\n      #else\n        getDirectionalDirectLightIrradiance( directionalLight, geometry, directLight );\n      #endif\n\n      shadow = 1.0;\n      #if defined( USE_SHADOWMAP ) && ( UNROLLED_LOOP_INDEX < NUM_DIR_LIGHT_SHADOWS )\n      directionalLightShadow = directionalLightShadows[ i ];\n      shadow = all( bvec2( directLight.visible, receiveShadow ) ) ? getShadow( directionalShadowMap[ i ], directionalLightShadow.shadowMapSize, directionalLightShadow.shadowBias, directionalLightShadow.shadowRadius, vDirectionalShadowCoord[ i ] ) : 1.0;\n      #endif\n\n      #if THREE_VRM_THREE_REVISION >= 157\n        RE_Direct( directLight, geometryPosition, geometryNormal, geometryViewDir, geometryClearcoatNormal, material, shadow, reflectedLight );\n      #else\n        RE_Direct( directLight, geometry, material, shadow, reflectedLight );\n      #endif\n\n    }\n    #pragma unroll_loop_end\n\n  #endif\n\n  // #if ( NUM_RECT_AREA_LIGHTS > 0 ) && defined( RE_Direct_RectArea )\n\n  //   RectAreaLight rectAreaLight;\n\n  //   #pragma unroll_loop_start\n  //   for ( int i = 0; i < NUM_RECT_AREA_LIGHTS; i ++ ) {\n\n  //     rectAreaLight = rectAreaLights[ i ];\n  //     RE_Direct_RectArea( rectAreaLight, geometry, material, reflectedLight );\n\n  //   }\n  //   #pragma unroll_loop_end\n\n  // #endif\n\n  #if defined( RE_IndirectDiffuse )\n\n    vec3 iblIrradiance = vec3( 0.0 );\n\n    vec3 irradiance = getAmbientLightIrradiance( ambientLightColor );\n\n    #if THREE_VRM_THREE_REVISION >= 157\n      #if defined( USE_LIGHT_PROBES )\n        irradiance += getLightProbeIrradiance( lightProbe, geometryNormal );\n      #endif\n    #elif THREE_VRM_THREE_REVISION >= 133\n      irradiance += getLightProbeIrradiance( lightProbe, geometry.normal );\n    #else\n      irradiance += getLightProbeIrradiance( lightProbe, geometry );\n    #endif\n\n    #if ( NUM_HEMI_LIGHTS > 0 )\n\n      #pragma unroll_loop_start\n      for ( int i = 0; i < NUM_HEMI_LIGHTS; i ++ ) {\n\n        #if THREE_VRM_THREE_REVISION >= 157\n          irradiance += getHemisphereLightIrradiance( hemisphereLights[ i ], geometryNormal );\n        #elif THREE_VRM_THREE_REVISION >= 133\n          irradiance += getHemisphereLightIrradiance( hemisphereLights[ i ], geometry.normal );\n        #else\n          irradiance += getHemisphereLightIrradiance( hemisphereLights[ i ], geometry );\n        #endif\n\n      }\n      #pragma unroll_loop_end\n\n    #endif\n\n  #endif\n\n  // #if defined( RE_IndirectSpecular )\n\n  //   vec3 radiance = vec3( 0.0 );\n  //   vec3 clearcoatRadiance = vec3( 0.0 );\n\n  // #endif\n\n  #include <lights_fragment_maps>\n  #include <lights_fragment_end>\n\n  // modulation\n  #include <aomap_fragment>\n\n  vec3 col = reflectedLight.directDiffuse + reflectedLight.indirectDiffuse;\n\n  #ifdef DEBUG_LITSHADERATE\n    gl_FragColor = vec4( col, diffuseColor.a );\n    postCorrection();\n    return;\n  #endif\n\n  // -- MToon: rim lighting -----------------------------------------\n  vec3 viewDir = normalize( vViewPosition );\n\n  #ifndef PHYSICALLY_CORRECT_LIGHTS\n    reflectedLight.directSpecular /= PI;\n  #endif\n  vec3 rimMix = mix( vec3( 1.0 ), reflectedLight.directSpecular, 1.0 );\n\n  vec3 rim = parametricRimColorFactor * pow( saturate( 1.0 - dot( viewDir, normal ) + parametricRimLiftFactor ), parametricRimFresnelPowerFactor );\n\n  #ifdef USE_MATCAPTEXTURE\n    {\n      vec3 x = normalize( vec3( viewDir.z, 0.0, -viewDir.x ) );\n      vec3 y = cross( viewDir, x ); // guaranteed to be normalized\n      vec2 sphereUv = 0.5 + 0.5 * vec2( dot( x, normal ), -dot( y, normal ) );\n      sphereUv = ( matcapTextureUvTransform * vec3( sphereUv, 1 ) ).xy;\n      vec3 matcap = texture2D( matcapTexture, sphereUv ).rgb;\n      rim += matcapFactor * matcap;\n    }\n  #endif\n\n  #ifdef USE_RIMMULTIPLYTEXTURE\n    vec2 rimMultiplyTextureUv = ( rimMultiplyTextureUvTransform * vec3( uv, 1 ) ).xy;\n    rim *= texture2D( rimMultiplyTexture, rimMultiplyTextureUv ).rgb;\n  #endif\n\n  col += rimMix * rim;\n\n  // -- MToon: Emission --------------------------------------------------------\n  col += totalEmissiveRadiance;\n\n  // #include <envmap_fragment>\n\n  // -- Almost done! -----------------------------------------------------------\n  #if defined( OUTLINE )\n    col = outlineColorFactor.rgb * mix( vec3( 1.0 ), col, outlineLightingMixFactor );\n  #endif\n\n  #ifdef OPAQUE\n    diffuseColor.a = 1.0;\n  #endif\n\n  gl_FragColor = vec4( col, diffuseColor.a );\n  postCorrection();\n}\n'}),this.uvAnimationScrollXSpeedFactor=0,this.uvAnimationScrollYSpeedFactor=0,this.uvAnimationRotationSpeedFactor=0,this.fog=!0,this.normalMapType=n.TangentSpaceNormalMap,this._ignoreVertexColor=!0,this._v0CompatShade=!1,this._debugMode=be.None,this._outlineWidthMode=Ue.None,this._isOutline=!1,e.transparentWithZWrite&&(e.depthWrite=!0),delete e.transparentWithZWrite,e.fog=!0,e.lights=!0,e.clipping=!0,parseInt(n.REVISION,10)<129&&(e.skinning=e.skinning||!1),parseInt(n.REVISION,10)<131&&(e.morphTargets=e.morphTargets||!1,e.morphNormals=e.morphNormals||!1),this.uniforms=n.UniformsUtils.merge([n.UniformsLib.common,n.UniformsLib.normalmap,n.UniformsLib.emissivemap,n.UniformsLib.fog,n.UniformsLib.lights,{litFactor:{value:new n.Color(1,1,1)},mapUvTransform:{value:new n.Matrix3},colorAlpha:{value:1},normalMapUvTransform:{value:new n.Matrix3},shadeColorFactor:{value:new n.Color(.97,.81,.86)},shadeMultiplyTexture:{value:null},shadeMultiplyTextureUvTransform:{value:new n.Matrix3},shadingShiftFactor:{value:0},shadingShiftTexture:{value:null},shadingShiftTextureUvTransform:{value:new n.Matrix3},shadingShiftTextureScale:{value:1},shadingToonyFactor:{value:.9},giEqualizationFactor:{value:.9},matcapFactor:{value:new n.Color(0,0,0)},matcapTexture:{value:null},matcapTextureUvTransform:{value:new n.Matrix3},parametricRimColorFactor:{value:new n.Color(0,0,0)},rimMultiplyTexture:{value:null},rimMultiplyTextureUvTransform:{value:new n.Matrix3},rimLightingMixFactor:{value:0},parametricRimFresnelPowerFactor:{value:1},parametricRimLiftFactor:{value:0},emissive:{value:new n.Color(0,0,0)},emissiveIntensity:{value:1},emissiveMapUvTransform:{value:new n.Matrix3},outlineWidthMultiplyTexture:{value:null},outlineWidthMultiplyTextureUvTransform:{value:new n.Matrix3},outlineWidthFactor:{value:.5},outlineColorFactor:{value:new n.Color(0,0,0)},outlineLightingMixFactor:{value:1},uvAnimationMaskTexture:{value:null},uvAnimationMaskTextureUvTransform:{value:new n.Matrix3},uvAnimationScrollXOffset:{value:0},uvAnimationScrollYOffset:{value:0},uvAnimationRotationPhase:{value:0}},e.uniforms]),this.setValues(e),this._uploadUniformsWorkaround(),this.customProgramCacheKey=()=>[...Object.entries(this._generateDefines()).map((([e,t])=>`${e}:${t}`)),this.matcapTexture?`matcapTextureColorSpace:${Oe(this.matcapTexture)}`:"",this.shadeMultiplyTexture?`shadeMultiplyTextureColorSpace:${Oe(this.shadeMultiplyTexture)}`:"",this.rimMultiplyTexture?`rimMultiplyTextureColorSpace:${Oe(this.rimMultiplyTexture)}`:""].join(","),this.onBeforeCompile=e=>{const t=parseInt(n.REVISION,10),i=Object.entries(Object.assign(Object.assign({},this._generateDefines()),this.defines)).filter((([e,t])=>!!t)).map((([e,t])=>`#define ${e} ${t}`)).join("\n")+"\n";e.vertexShader=i+e.vertexShader,e.fragmentShader=i+e.fragmentShader,t<154&&(e.fragmentShader=e.fragmentShader.replace("#include <colorspace_fragment>","#include <encodings_fragment>")),t<132&&(e.fragmentShader=e.fragmentShader.replace("#include <normal_pars_fragment>",""),e.fragmentShader=e.fragmentShader.replace("#include <alphatest_pars_fragment>",""))}}update(e){this._uploadUniformsWorkaround(),this._updateUVAnimation(e)}copy(e){return super.copy(e),this.map=e.map,this.normalMap=e.normalMap,this.emissiveMap=e.emissiveMap,this.shadeMultiplyTexture=e.shadeMultiplyTexture,this.shadingShiftTexture=e.shadingShiftTexture,this.matcapTexture=e.matcapTexture,this.rimMultiplyTexture=e.rimMultiplyTexture,this.outlineWidthMultiplyTexture=e.outlineWidthMultiplyTexture,this.uvAnimationMaskTexture=e.uvAnimationMaskTexture,this.normalMapType=e.normalMapType,this.uvAnimationScrollXSpeedFactor=e.uvAnimationScrollXSpeedFactor,this.uvAnimationScrollYSpeedFactor=e.uvAnimationScrollYSpeedFactor,this.uvAnimationRotationSpeedFactor=e.uvAnimationRotationSpeedFactor,this.ignoreVertexColor=e.ignoreVertexColor,this.v0CompatShade=e.v0CompatShade,this.debugMode=e.debugMode,this.outlineWidthMode=e.outlineWidthMode,this.isOutline=e.isOutline,this.needsUpdate=!0,this}_updateUVAnimation(e){this.uniforms.uvAnimationScrollXOffset.value+=e*this.uvAnimationScrollXSpeedFactor,this.uniforms.uvAnimationScrollYOffset.value+=e*this.uvAnimationScrollYSpeedFactor,this.uniforms.uvAnimationRotationPhase.value+=e*this.uvAnimationRotationSpeedFactor,this.uniformsNeedUpdate=!0}_uploadUniformsWorkaround(){this.uniforms.opacity.value=this.opacity,this._updateTextureMatrix(this.uniforms.map,this.uniforms.mapUvTransform),this._updateTextureMatrix(this.uniforms.normalMap,this.uniforms.normalMapUvTransform),this._updateTextureMatrix(this.uniforms.emissiveMap,this.uniforms.emissiveMapUvTransform),this._updateTextureMatrix(this.uniforms.shadeMultiplyTexture,this.uniforms.shadeMultiplyTextureUvTransform),this._updateTextureMatrix(this.uniforms.shadingShiftTexture,this.uniforms.shadingShiftTextureUvTransform),this._updateTextureMatrix(this.uniforms.matcapTexture,this.uniforms.matcapTextureUvTransform),this._updateTextureMatrix(this.uniforms.rimMultiplyTexture,this.uniforms.rimMultiplyTextureUvTransform),this._updateTextureMatrix(this.uniforms.outlineWidthMultiplyTexture,this.uniforms.outlineWidthMultiplyTextureUvTransform),this._updateTextureMatrix(this.uniforms.uvAnimationMaskTexture,this.uniforms.uvAnimationMaskTextureUvTransform);parseInt(n.REVISION,10)>=132&&(this.uniforms.alphaTest.value=this.alphaTest),this.uniformsNeedUpdate=!0}_generateDefines(){const e=parseInt(n.REVISION,10),t=null!==this.outlineWidthMultiplyTexture,i=null!==this.map||null!==this.emissiveMap||null!==this.shadeMultiplyTexture||null!==this.shadingShiftTexture||null!==this.rimMultiplyTexture||null!==this.uvAnimationMaskTexture;return{THREE_VRM_THREE_REVISION:e,OUTLINE:this._isOutline,MTOON_USE_UV:t||i,MTOON_UVS_VERTEX_ONLY:t&&!i,V0_COMPAT_SHADE:this._v0CompatShade,USE_SHADEMULTIPLYTEXTURE:null!==this.shadeMultiplyTexture,USE_SHADINGSHIFTTEXTURE:null!==this.shadingShiftTexture,USE_MATCAPTEXTURE:null!==this.matcapTexture,USE_RIMMULTIPLYTEXTURE:null!==this.rimMultiplyTexture,USE_OUTLINEWIDTHMULTIPLYTEXTURE:this._isOutline&&null!==this.outlineWidthMultiplyTexture,USE_UVANIMATIONMASKTEXTURE:null!==this.uvAnimationMaskTexture,IGNORE_VERTEX_COLOR:!0===this._ignoreVertexColor,DEBUG_NORMAL:"normal"===this._debugMode,DEBUG_LITSHADERATE:"litShadeRate"===this._debugMode,DEBUG_UV:"uv"===this._debugMode,OUTLINE_WIDTH_WORLD:this._isOutline&&this._outlineWidthMode===Ue.WorldCoordinates,OUTLINE_WIDTH_SCREEN:this._isOutline&&this._outlineWidthMode===Ue.ScreenCoordinates}}_updateTextureMatrix(e,t){e.value&&(e.value.matrixAutoUpdate&&e.value.updateMatrix(),t.value.copy(e.value.matrix))}}const Ce={"":3e3,srgb:3001};class De{get pending(){return Promise.all(this._pendings)}constructor(e,t){this._parser=e,this._materialParams=t,this._pendings=[]}assignPrimitive(e,t){null!=t&&(this._materialParams[e]=t)}assignColor(e,t,i){null!=t&&(this._materialParams[e]=(new n.Color).fromArray(t),i&&this._materialParams[e].convertSRGBToLinear())}assignTexture(e,t,i){return Ie(this,void 0,void 0,(function*(){const r=(()=>Ie(this,void 0,void 0,(function*(){null!=t&&(yield this._parser.assignTexture(this._materialParams,e,t),i&&function(e,t){parseInt(n.REVISION,10)>=152?e.colorSpace=t:e.encoding=Ce[t]}(this._materialParams[e],"srgb"))})))();return this._pendings.push(r),r}))}assignTextureByIndex(e,t,i){return Ie(this,void 0,void 0,(function*(){return this.assignTexture(e,null!=t?{index:t}:void 0,i)}))}}const Fe=new Set(["1.0","1.0-beta"]);class He{get name(){return He.EXTENSION_NAME}constructor(e,t={}){var i,n,r;this.parser=e,this.renderOrderOffset=null!==(i=t.renderOrderOffset)&&void 0!==i?i:0,this.v0CompatShade=null!==(n=t.v0CompatShade)&&void 0!==n&&n,this.debugMode=null!==(r=t.debugMode)&&void 0!==r?r:"none",this._mToonMaterialSet=new Set}beforeRoot(){return Ie(this,void 0,void 0,(function*(){this._removeUnlitExtensionIfMToonExists()}))}afterRoot(e){return Ie(this,void 0,void 0,(function*(){e.userData.vrmMToonMaterials=Array.from(this._mToonMaterialSet)}))}getMaterialType(e){return this._getMToonExtension(e)?Ne:null}extendMaterialParams(e,t){const i=this._getMToonExtension(e);return i?this._extendMaterialParams(i,t):null}loadMesh(e){var t;return Ie(this,void 0,void 0,(function*(){const i=this.parser,n=i.json,r=null===(t=n.meshes)||void 0===t?void 0:t[e];if(null==r)throw new Error(`MToonMaterialLoaderPlugin: Attempt to use meshes[${e}] of glTF but the mesh doesn't exist`);const o=r.primitives,s=yield i.loadMesh(e);if(1===o.length){const e=s,t=o[0].material;null!=t&&this._setupPrimitive(e,t)}else{const e=s;for(let t=0;t<o.length;t++){const i=e.children[t],n=o[t].material;null!=n&&this._setupPrimitive(i,n)}}return s}))}_removeUnlitExtensionIfMToonExists(){const e=this.parser.json.materials;null==e||e.map(((e,t)=>{var i;this._getMToonExtension(t)&&(null===(i=e.extensions)||void 0===i?void 0:i.KHR_materials_unlit)&&delete e.extensions.KHR_materials_unlit}))}_getMToonExtension(e){var t,i;const n=null===(t=this.parser.json.materials)||void 0===t?void 0:t[e];if(null==n)return void console.warn(`MToonMaterialLoaderPlugin: Attempt to use materials[${e}] of glTF but the material doesn't exist`);const r=null===(i=n.extensions)||void 0===i?void 0:i[He.EXTENSION_NAME];if(null==r)return;const o=r.specVersion;if(Fe.has(o))return r;console.warn(`MToonMaterialLoaderPlugin: Unknown ${He.EXTENSION_NAME} specVersion "${o}"`)}_extendMaterialParams(e,t){var i;return Ie(this,void 0,void 0,(function*(){delete t.metalness,delete t.roughness;const n=new De(this.parser,t);n.assignPrimitive("transparentWithZWrite",e.transparentWithZWrite),n.assignColor("shadeColorFactor",e.shadeColorFactor),n.assignTexture("shadeMultiplyTexture",e.shadeMultiplyTexture,!0),n.assignPrimitive("shadingShiftFactor",e.shadingShiftFactor),n.assignTexture("shadingShiftTexture",e.shadingShiftTexture,!0),n.assignPrimitive("shadingShiftTextureScale",null===(i=e.shadingShiftTexture)||void 0===i?void 0:i.scale),n.assignPrimitive("shadingToonyFactor",e.shadingToonyFactor),n.assignPrimitive("giEqualizationFactor",e.giEqualizationFactor),n.assignColor("matcapFactor",e.matcapFactor),n.assignTexture("matcapTexture",e.matcapTexture,!0),n.assignColor("parametricRimColorFactor",e.parametricRimColorFactor),n.assignTexture("rimMultiplyTexture",e.rimMultiplyTexture,!0),n.assignPrimitive("rimLightingMixFactor",e.rimLightingMixFactor),n.assignPrimitive("parametricRimFresnelPowerFactor",e.parametricRimFresnelPowerFactor),n.assignPrimitive("parametricRimLiftFactor",e.parametricRimLiftFactor),n.assignPrimitive("outlineWidthMode",e.outlineWidthMode),n.assignPrimitive("outlineWidthFactor",e.outlineWidthFactor),n.assignTexture("outlineWidthMultiplyTexture",e.outlineWidthMultiplyTexture,!1),n.assignColor("outlineColorFactor",e.outlineColorFactor),n.assignPrimitive("outlineLightingMixFactor",e.outlineLightingMixFactor),n.assignTexture("uvAnimationMaskTexture",e.uvAnimationMaskTexture,!1),n.assignPrimitive("uvAnimationScrollXSpeedFactor",e.uvAnimationScrollXSpeedFactor),n.assignPrimitive("uvAnimationScrollYSpeedFactor",e.uvAnimationScrollYSpeedFactor),n.assignPrimitive("uvAnimationRotationSpeedFactor",e.uvAnimationRotationSpeedFactor),n.assignPrimitive("v0CompatShade",this.v0CompatShade),n.assignPrimitive("debugMode",this.debugMode),yield n.pending}))}_setupPrimitive(e,t){const i=this._getMToonExtension(t);if(i){const t=this._parseRenderOrder(i);return e.renderOrder=t+this.renderOrderOffset,this._generateOutline(e),void this._addToMaterialSet(e)}}_generateOutline(e){const t=e.material;if(!(t instanceof Ne))return;if("none"===t.outlineWidthMode||t.outlineWidthFactor<=0)return;e.material=[t];const i=t.clone();i.name+=" (Outline)",i.isOutline=!0,i.side=n.BackSide,e.material.push(i);const r=e.geometry,o=r.index?r.index.count:r.attributes.position.count/3;r.addGroup(0,o,0),r.addGroup(0,o,1)}_addToMaterialSet(e){const t=e.material,i=new Set;Array.isArray(t)?t.forEach((e=>i.add(e))):i.add(t);for(const e of i)e instanceof Ne&&this._mToonMaterialSet.add(e)}_parseRenderOrder(e){var t;return(e.transparentWithZWrite?0:19)+(null!==(t=e.renderQueueOffsetNumber)&&void 0!==t?t:0)}}
/*!
     * @pixiv/three-vrm-materials-hdr-emissive-multiplier v2.0.6
     * Support VRMC_hdr_emissiveMultiplier for @pixiv/three-vrm
     *
     * Copyright (c) 2020-2023 pixiv Inc.
     * @pixiv/three-vrm-materials-hdr-emissive-multiplier is distributed under MIT License
     * https://github.com/pixiv/three-vrm/blob/release/LICENSE
     */
function Be(e,t,i,n){return new(i||(i=Promise))((function(r,o){function s(e){try{l(n.next(e))}catch(e){o(e)}}function a(e){try{l(n.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(s,a)}l((n=n.apply(e,t||[])).next())}))}He.EXTENSION_NAME="VRMC_materials_mtoon";class ke{get name(){return ke.EXTENSION_NAME}constructor(e){this.parser=e}extendMaterialParams(e,t){return Be(this,void 0,void 0,(function*(){const i=this._getHDREmissiveMultiplierExtension(e);if(null==i)return;console.warn("VRMMaterialsHDREmissiveMultiplierLoaderPlugin: `VRMC_materials_hdr_emissiveMultiplier` is archived. Use `KHR_materials_emissive_strength` instead.");const n=i.emissiveMultiplier;t.emissiveIntensity=n}))}_getHDREmissiveMultiplierExtension(e){var t,i;const n=null===(t=this.parser.json.materials)||void 0===t?void 0:t[e];if(null==n)return void console.warn(`VRMMaterialsHDREmissiveMultiplierLoaderPlugin: Attempt to use materials[${e}] of glTF but the material doesn't exist`);const r=null===(i=n.extensions)||void 0===i?void 0:i[ke.EXTENSION_NAME];return null!=r?r:void 0}}
/*!
     * @pixiv/three-vrm-materials-v0compat v2.0.6
     * VRM0.0 materials compatibility layer plugin for @pixiv/three-vrm
     *
     * Copyright (c) 2020-2023 pixiv Inc.
     * @pixiv/three-vrm-materials-v0compat is distributed under MIT License
     * https://github.com/pixiv/three-vrm/blob/release/LICENSE
     */
function We(e,t,i,n){return new(i||(i=Promise))((function(r,o){function s(e){try{l(n.next(e))}catch(e){o(e)}}function a(e){try{l(n.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(s,a)}l((n=n.apply(e,t||[])).next())}))}function ze(e){return Math.pow(e,2.2)}ke.EXTENSION_NAME="VRMC_materials_hdr_emissiveMultiplier";class je{get name(){return"VRMMaterialsV0CompatPlugin"}constructor(e){var t;this.parser=e,this._renderQueueMapTransparent=new Map,this._renderQueueMapTransparentZWrite=new Map;const i=this.parser.json;i.extensionsUsed=null!==(t=i.extensionsUsed)&&void 0!==t?t:[],-1===i.extensionsUsed.indexOf("KHR_texture_transform")&&i.extensionsUsed.push("KHR_texture_transform")}beforeRoot(){var e;return We(this,void 0,void 0,(function*(){const t=this.parser.json,i=null===(e=t.extensions)||void 0===e?void 0:e.VRM,n=null==i?void 0:i.materialProperties;n&&(this._populateRenderQueueMap(n),n.forEach(((e,i)=>{var n,r;const o=null===(n=t.materials)||void 0===n?void 0:n[i];if(null!=o)if("VRM/MToon"===e.shader){const n=this._parseV0MToonProperties(e,o);t.materials[i]=n}else if(null===(r=e.shader)||void 0===r?void 0:r.startsWith("VRM/Unlit")){const n=this._parseV0UnlitProperties(e,o);t.materials[i]=n}else"VRM_USE_GLTFSHADER"===e.shader||console.warn(`VRMMaterialsV0CompatPlugin: Unknown shader: ${e.shader}`);else console.warn(`VRMMaterialsV0CompatPlugin: Attempt to use materials[${i}] of glTF but the material doesn't exist`)})))}))}_parseV0MToonProperties(e,t){var i,r,o,s,a,l,u,d,h,c,p,m,f,g,v,_,M,x,y,R,T,E,w,P,S,L,A,I,b,U,V,O,N,C,D,F,H,B,k,W,z,j,Q,X;const G=null!==(r=null===(i=e.keywordMap)||void 0===i?void 0:i._ALPHABLEND_ON)&&void 0!==r&&r,Y=1===(null===(o=e.floatProperties)||void 0===o?void 0:o._ZWrite)&&G,q=this._v0ParseRenderQueue(e),$=null!==(a=null===(s=e.keywordMap)||void 0===s?void 0:s._ALPHATEST_ON)&&void 0!==a&&a,Z=G?"BLEND":$?"MASK":"OPAQUE",J=$?null===(l=e.floatProperties)||void 0===l?void 0:l._Cutoff:void 0,K=0===(null!==(d=null===(u=e.floatProperties)||void 0===u?void 0:u._CullMode)&&void 0!==d?d:2),ee=this._portTextureTransform(e),te=null===(c=null===(h=e.vectorProperties)||void 0===h?void 0:h._Color)||void 0===c?void 0:c.map(((e,t)=>3===t?e:ze(e))),ie=null===(p=e.textureProperties)||void 0===p?void 0:p._MainTex,ne=null!=ie?{index:ie,extensions:Object.assign({},ee)}:void 0,re=null===(m=e.floatProperties)||void 0===m?void 0:m._BumpScale,oe=null===(f=e.textureProperties)||void 0===f?void 0:f._BumpMap,se=null!=oe?{index:oe,scale:re,extensions:Object.assign({},ee)}:void 0,ae=null===(v=null===(g=e.vectorProperties)||void 0===g?void 0:g._EmissionColor)||void 0===v?void 0:v.map(ze),le=null===(_=e.textureProperties)||void 0===_?void 0:_._EmissionMap,ue=null!=le?{index:le,extensions:Object.assign({},ee)}:void 0,de=null===(x=null===(M=e.vectorProperties)||void 0===M?void 0:M._ShadeColor)||void 0===x?void 0:x.map(ze),he=null===(y=e.textureProperties)||void 0===y?void 0:y._ShadeTexture,ce=null!=he?{index:he,extensions:Object.assign({},ee)}:void 0;let pe=null!==(T=null===(R=e.floatProperties)||void 0===R?void 0:R._ShadeShift)&&void 0!==T?T:0,me=null!==(w=null===(E=e.floatProperties)||void 0===E?void 0:E._ShadeToony)&&void 0!==w?w:.9;me=n.MathUtils.lerp(me,1,.5+.5*pe),pe=-pe-(1-me);const fe=null===(P=e.floatProperties)||void 0===P?void 0:P._IndirectLightIntensity,ge=fe?1-fe:void 0,ve=null===(S=e.textureProperties)||void 0===S?void 0:S._SphereAdd,_e=null!=ve?[1,1,1]:void 0,Me=null!=ve?{index:ve}:void 0,xe=null===(L=e.floatProperties)||void 0===L?void 0:L._RimLightingMix,ye=null===(A=e.textureProperties)||void 0===A?void 0:A._RimTexture,Re=null!=ye?{index:ye,extensions:Object.assign({},ee)}:void 0,Te=null===(b=null===(I=e.vectorProperties)||void 0===I?void 0:I._RimColor)||void 0===b?void 0:b.map(ze),Ee=null===(U=e.floatProperties)||void 0===U?void 0:U._RimFresnelPower,we=null===(V=e.floatProperties)||void 0===V?void 0:V._RimLift,Pe=["none","worldCoordinates","screenCoordinates"][null!==(N=null===(O=e.floatProperties)||void 0===O?void 0:O._OutlineWidthMode)&&void 0!==N?N:0];let Se=null!==(D=null===(C=e.floatProperties)||void 0===C?void 0:C._OutlineWidth)&&void 0!==D?D:0;Se*=.01;const Le=null===(F=e.textureProperties)||void 0===F?void 0:F._OutlineWidthTexture,Ae=null!=Le?{index:Le,extensions:Object.assign({},ee)}:void 0,Ie=null===(B=null===(H=e.vectorProperties)||void 0===H?void 0:H._OutlineColor)||void 0===B?void 0:B.map(ze),be=1===(null===(k=e.floatProperties)||void 0===k?void 0:k._OutlineColorMode)?null===(W=e.floatProperties)||void 0===W?void 0:W._OutlineLightingMix:0,Ue=null===(z=e.textureProperties)||void 0===z?void 0:z._UvAnimMaskTexture,Ve=null!=Ue?{index:Ue,extensions:Object.assign({},ee)}:void 0,Oe=null===(j=e.floatProperties)||void 0===j?void 0:j._UvAnimScrollX;let Ne=null===(Q=e.floatProperties)||void 0===Q?void 0:Q._UvAnimScrollY;null!=Ne&&(Ne=-Ne);const Ce={specVersion:"1.0",transparentWithZWrite:Y,renderQueueOffsetNumber:q,shadeColorFactor:de,shadeMultiplyTexture:ce,shadingShiftFactor:pe,shadingToonyFactor:me,giEqualizationFactor:ge,matcapFactor:_e,matcapTexture:Me,rimLightingMixFactor:xe,rimMultiplyTexture:Re,parametricRimColorFactor:Te,parametricRimFresnelPowerFactor:Ee,parametricRimLiftFactor:we,outlineWidthMode:Pe,outlineWidthFactor:Se,outlineWidthMultiplyTexture:Ae,outlineColorFactor:Ie,outlineLightingMixFactor:be,uvAnimationMaskTexture:Ve,uvAnimationScrollXSpeedFactor:Oe,uvAnimationScrollYSpeedFactor:Ne,uvAnimationRotationSpeedFactor:null===(X=e.floatProperties)||void 0===X?void 0:X._UvAnimRotation};return Object.assign(Object.assign({},t),{pbrMetallicRoughness:{baseColorFactor:te,baseColorTexture:ne},normalTexture:se,emissiveTexture:ue,emissiveFactor:ae,alphaMode:Z,alphaCutoff:J,doubleSided:K,extensions:{VRMC_materials_mtoon:Ce}})}_parseV0UnlitProperties(e,t){var i,n,r,o;const s="VRM/UnlitTransparentZWrite"===e.shader,a="VRM/UnlitTransparent"===e.shader||s,l=this._v0ParseRenderQueue(e),u="VRM/UnlitCutout"===e.shader,d=a?"BLEND":u?"MASK":"OPAQUE",h=u?null===(i=e.floatProperties)||void 0===i?void 0:i._Cutoff:void 0,c=this._portTextureTransform(e),p=null===(r=null===(n=e.vectorProperties)||void 0===n?void 0:n._Color)||void 0===r?void 0:r.map(ze),m=null===(o=e.textureProperties)||void 0===o?void 0:o._MainTex,f=null!=m?{index:m,extensions:Object.assign({},c)}:void 0,g={specVersion:"1.0",transparentWithZWrite:s,renderQueueOffsetNumber:l,shadeColorFactor:p,shadeMultiplyTexture:f};return Object.assign(Object.assign({},t),{pbrMetallicRoughness:{baseColorFactor:p,baseColorTexture:f},alphaMode:d,alphaCutoff:h,extensions:{VRMC_materials_mtoon:g}})}_portTextureTransform(e){var t,i,n,r,o;const s=null===(t=e.vectorProperties)||void 0===t?void 0:t._MainTex;if(null==s)return{};const a=[null!==(i=null==s?void 0:s[0])&&void 0!==i?i:0,null!==(n=null==s?void 0:s[1])&&void 0!==n?n:0],l=[null!==(r=null==s?void 0:s[2])&&void 0!==r?r:1,null!==(o=null==s?void 0:s[3])&&void 0!==o?o:1];return a[1]=1-l[1]-a[1],{KHR_texture_transform:{offset:a,scale:l}}}_v0ParseRenderQueue(e){var t,i,n;const r=null!==(i=null===(t=e.keywordMap)||void 0===t?void 0:t._ALPHABLEND_ON)&&void 0!==i&&i,o=1===(null===(n=e.floatProperties)||void 0===n?void 0:n._ZWrite);let s=0;if(r){const t=e.renderQueue;null!=t&&(s=o?this._renderQueueMapTransparentZWrite.get(t):this._renderQueueMapTransparent.get(t))}return s}_populateRenderQueueMap(e){const t=new Set,i=new Set;e.forEach((e=>{var n,r,o;const s=null!==(r=null===(n=e.keywordMap)||void 0===n?void 0:n._ALPHABLEND_ON)&&void 0!==r&&r,a=1===(null===(o=e.floatProperties)||void 0===o?void 0:o._ZWrite);if(s){const n=e.renderQueue;null!=n&&(a?i.add(n):t.add(n))}})),t.size>10&&console.warn(`VRMMaterialsV0CompatPlugin: This VRM uses ${t.size} render queues for Transparent materials while VRM 1.0 only supports up to 10 render queues. The model might not be rendered correctly.`),i.size>10&&console.warn(`VRMMaterialsV0CompatPlugin: This VRM uses ${i.size} render queues for TransparentZWrite materials while VRM 1.0 only supports up to 10 render queues. The model might not be rendered correctly.`),Array.from(t).sort().forEach(((e,i)=>{const n=Math.min(Math.max(i-t.size+1,-9),0);this._renderQueueMapTransparent.set(e,n)})),Array.from(i).sort().forEach(((e,t)=>{const i=Math.min(Math.max(t,0),9);this._renderQueueMapTransparentZWrite.set(e,i)}))}}
/*!
     * @pixiv/three-vrm-node-constraint v2.0.6
     * Node constraint module for @pixiv/three-vrm
     *
     * Copyright (c) 2020-2023 pixiv Inc.
     * @pixiv/three-vrm-node-constraint is distributed under MIT License
     * https://github.com/pixiv/three-vrm/blob/release/LICENSE
     */const Qe=new n.Vector3;class Xe extends n.Group{constructor(e){super(),this._attrPosition=new n.BufferAttribute(new Float32Array([0,0,0,0,0,0]),3),this._attrPosition.setUsage(n.DynamicDrawUsage);const t=new n.BufferGeometry;t.setAttribute("position",this._attrPosition);const i=new n.LineBasicMaterial({color:16711935,depthTest:!1,depthWrite:!1});this._line=new n.Line(t,i),this.add(this._line),this.constraint=e}updateMatrixWorld(e){Qe.setFromMatrixPosition(this.constraint.destination.matrixWorld),this._attrPosition.setXYZ(0,Qe.x,Qe.y,Qe.z),this.constraint.source&&Qe.setFromMatrixPosition(this.constraint.source.matrixWorld),this._attrPosition.setXYZ(1,Qe.x,Qe.y,Qe.z),this._attrPosition.needsUpdate=!0,super.updateMatrixWorld(e)}}function Ge(e,t){return t.set(e.elements[12],e.elements[13],e.elements[14])}const Ye=new n.Vector3,qe=new n.Vector3;function $e(e){return e.invert?e.invert():e.inverse(),e}class Ze{constructor(e,t){this.destination=e,this.source=t,this.weight=1}}const Je=new n.Vector3,Ke=new n.Vector3,et=new n.Vector3,tt=new n.Quaternion,it=new n.Quaternion,nt=new n.Quaternion;class rt extends Ze{get aimAxis(){return this._aimAxis}set aimAxis(e){this._aimAxis=e,this._v3AimAxis.set("PositiveX"===e?1:"NegativeX"===e?-1:0,"PositiveY"===e?1:"NegativeY"===e?-1:0,"PositiveZ"===e?1:"NegativeZ"===e?-1:0)}get dependencies(){const e=new Set([this.source]);return this.destination.parent&&e.add(this.destination.parent),e}constructor(e,t){super(e,t),this._aimAxis="PositiveX",this._v3AimAxis=new n.Vector3(1,0,0),this._dstRestQuat=new n.Quaternion}setInitState(){this._dstRestQuat.copy(this.destination.quaternion)}update(){this.destination.updateWorldMatrix(!0,!1),this.source.updateWorldMatrix(!0,!1);const e=tt.identity(),t=it.identity();var i,n;this.destination.parent&&(i=this.destination.parent.matrixWorld,n=e,i.decompose(Ye,n,qe),$e(t.copy(e)));const r=Je.copy(this._v3AimAxis).applyQuaternion(this._dstRestQuat).applyQuaternion(e),o=Ge(this.source.matrixWorld,Ke).sub(Ge(this.destination.matrixWorld,et)).normalize(),s=nt.setFromUnitVectors(r,o).premultiply(t).multiply(e).multiply(this._dstRestQuat);this.destination.quaternion.copy(this._dstRestQuat).slerp(s,this.weight)}}function ot(e,t,i,n){return new(i||(i=Promise))((function(r,o){function s(e){try{l(n.next(e))}catch(e){o(e)}}function a(e){try{l(n.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(s,a)}l((n=n.apply(e,t||[])).next())}))}function st(e,t){const i=[e];let n=e.parent;for(;null!==n;)i.unshift(n),n=n.parent;i.forEach((e=>{t(e)}))}class at{constructor(){this._constraints=new Set,this._objectConstraintsMap=new Map}get constraints(){return this._constraints}addConstraint(e){this._constraints.add(e);let t=this._objectConstraintsMap.get(e.destination);null==t&&(t=new Set,this._objectConstraintsMap.set(e.destination,t)),t.add(e)}deleteConstraint(e){this._constraints.delete(e);this._objectConstraintsMap.get(e.destination).delete(e)}setInitState(){const e=new Set,t=new Set;for(const i of this._constraints)this._processConstraint(i,e,t,(e=>e.setInitState()))}update(){const e=new Set,t=new Set;for(const i of this._constraints)this._processConstraint(i,e,t,(e=>e.update()))}_processConstraint(e,t,i,n){if(i.has(e))return;if(t.has(e))throw new Error("VRMNodeConstraintManager: Circular dependency detected while updating constraints");t.add(e);const r=e.dependencies;for(const e of r)st(e,(e=>{const r=this._objectConstraintsMap.get(e);if(r)for(const e of r)this._processConstraint(e,t,i,n)}));n(e),i.add(e)}}const lt=new n.Quaternion,ut=new n.Quaternion;class dt extends Ze{get dependencies(){return new Set([this.source])}constructor(e,t){super(e,t),this._dstRestQuat=new n.Quaternion,this._invSrcRestQuat=new n.Quaternion}setInitState(){this._dstRestQuat.copy(this.destination.quaternion),$e(this._invSrcRestQuat.copy(this.source.quaternion))}update(){const e=lt.copy(this._invSrcRestQuat).multiply(this.source.quaternion),t=ut.copy(this._dstRestQuat).multiply(e);this.destination.quaternion.copy(this._dstRestQuat).slerp(t,this.weight)}}const ht=new n.Vector3,ct=new n.Quaternion,pt=new n.Quaternion;class mt extends Ze{get rollAxis(){return this._rollAxis}set rollAxis(e){this._rollAxis=e,this._v3RollAxis.set("X"===e?1:0,"Y"===e?1:0,"Z"===e?1:0)}get dependencies(){return new Set([this.source])}constructor(e,t){super(e,t),this._rollAxis="X",this._v3RollAxis=new n.Vector3(1,0,0),this._dstRestQuat=new n.Quaternion,this._invDstRestQuat=new n.Quaternion,this._invSrcRestQuatMulDstRestQuat=new n.Quaternion}setInitState(){this._dstRestQuat.copy(this.destination.quaternion),$e(this._invDstRestQuat.copy(this._dstRestQuat)),$e(this._invSrcRestQuatMulDstRestQuat.copy(this.source.quaternion)).multiply(this._dstRestQuat)}update(){const e=ct.copy(this._invDstRestQuat).multiply(this.source.quaternion).multiply(this._invSrcRestQuatMulDstRestQuat),t=ht.copy(this._v3RollAxis).applyQuaternion(e),i=pt.setFromUnitVectors(t,this._v3RollAxis).premultiply(this._dstRestQuat).multiply(e);this.destination.quaternion.copy(this._dstRestQuat).slerp(i,this.weight)}}const ft=new Set(["1.0","1.0-beta"]);class gt{get name(){return gt.EXTENSION_NAME}constructor(e,t){this.parser=e,this.helperRoot=null==t?void 0:t.helperRoot}afterRoot(e){return ot(this,void 0,void 0,(function*(){e.userData.vrmNodeConstraintManager=yield this._import(e)}))}_import(e){var t;return ot(this,void 0,void 0,(function*(){const i=this.parser.json;if(!(-1!==(null===(t=i.extensionsUsed)||void 0===t?void 0:t.indexOf(gt.EXTENSION_NAME))))return null;const n=new at,r=yield this.parser.getDependencies("node");return r.forEach(((e,t)=>{var o;const s=i.nodes[t],a=null===(o=null==s?void 0:s.extensions)||void 0===o?void 0:o[gt.EXTENSION_NAME];if(null==a)return;const l=a.specVersion;if(!ft.has(l))return void console.warn(`VRMNodeConstraintLoaderPlugin: Unknown ${gt.EXTENSION_NAME} specVersion "${l}"`);const u=a.constraint;if(null!=u.roll){const t=this._importRollConstraint(e,r,u.roll);n.addConstraint(t)}else if(null!=u.aim){const t=this._importAimConstraint(e,r,u.aim);n.addConstraint(t)}else if(null!=u.rotation){const t=this._importRotationConstraint(e,r,u.rotation);n.addConstraint(t)}})),e.scene.updateMatrixWorld(),n.setInitState(),n}))}_importRollConstraint(e,t,i){const{source:n,rollAxis:r,weight:o}=i,s=t[n],a=new mt(e,s);if(null!=r&&(a.rollAxis=r),null!=o&&(a.weight=o),this.helperRoot){const e=new Xe(a);this.helperRoot.add(e)}return a}_importAimConstraint(e,t,i){const{source:n,aimAxis:r,weight:o}=i,s=t[n],a=new rt(e,s);if(null!=r&&(a.aimAxis=r),null!=o&&(a.weight=o),this.helperRoot){const e=new Xe(a);this.helperRoot.add(e)}return a}_importRotationConstraint(e,t,i){const{source:n,weight:r}=i,o=t[n],s=new dt(e,o);if(null!=r&&(s.weight=r),this.helperRoot){const e=new Xe(s);this.helperRoot.add(e)}return s}}gt.EXTENSION_NAME="VRMC_node_constraint";
/*!
     * @pixiv/three-vrm-springbone v2.0.6
     * Spring bone module for @pixiv/three-vrm
     *
     * Copyright (c) 2020-2023 pixiv Inc.
     * @pixiv/three-vrm-springbone is distributed under MIT License
     * https://github.com/pixiv/three-vrm/blob/release/LICENSE
     */
class vt{}const _t=new n.Vector3,Mt=new n.Vector3;class xt extends vt{get type(){return"capsule"}constructor(e){var t,i,r;super(),this.offset=null!==(t=null==e?void 0:e.offset)&&void 0!==t?t:new n.Vector3(0,0,0),this.tail=null!==(i=null==e?void 0:e.tail)&&void 0!==i?i:new n.Vector3(0,0,0),this.radius=null!==(r=null==e?void 0:e.radius)&&void 0!==r?r:0}calculateCollision(e,t,i,n){_t.copy(this.offset).applyMatrix4(e),Mt.copy(this.tail).applyMatrix4(e),Mt.sub(_t);const r=Mt.lengthSq();n.copy(t).sub(_t);const o=Mt.dot(n);o<=0||(r<=o||Mt.multiplyScalar(o/r),n.sub(Mt));const s=i+this.radius,a=n.length()-s;return n.normalize(),a}}class yt extends vt{get type(){return"sphere"}constructor(e){var t,i;super(),this.offset=null!==(t=null==e?void 0:e.offset)&&void 0!==t?t:new n.Vector3(0,0,0),this.radius=null!==(i=null==e?void 0:e.radius)&&void 0!==i?i:0}calculateCollision(e,t,i,n){n.copy(this.offset).applyMatrix4(e),n.negate().add(t);const r=i+this.radius,o=n.length()-r;return n.normalize(),o}}const Rt=new n.Vector3;class Tt extends n.BufferGeometry{constructor(e){super(),this.worldScale=1,this._currentRadius=0,this._currentOffset=new n.Vector3,this._currentTail=new n.Vector3,this._shape=e,this._attrPos=new n.BufferAttribute(new Float32Array(396),3),this.setAttribute("position",this._attrPos),this._attrIndex=new n.BufferAttribute(new Uint16Array(264),1),this.setIndex(this._attrIndex),this._buildIndex(),this.update()}update(){let e=!1;const t=this._shape.radius/this.worldScale;this._currentRadius!==t&&(this._currentRadius=t,e=!0),this._currentOffset.equals(this._shape.offset)||(this._currentOffset.copy(this._shape.offset),e=!0);const i=Rt.copy(this._shape.tail).divideScalar(this.worldScale);this._currentTail.distanceToSquared(i)>1e-10&&(this._currentTail.copy(i),e=!0),e&&this._buildPosition()}_buildPosition(){Rt.copy(this._currentTail).sub(this._currentOffset);const e=Rt.length()/this._currentRadius;for(let t=0;t<=16;t++){const i=t/16*Math.PI;this._attrPos.setXYZ(t,-Math.sin(i),-Math.cos(i),0),this._attrPos.setXYZ(17+t,e+Math.sin(i),Math.cos(i),0),this._attrPos.setXYZ(34+t,-Math.sin(i),0,-Math.cos(i)),this._attrPos.setXYZ(51+t,e+Math.sin(i),0,Math.cos(i))}for(let t=0;t<32;t++){const i=t/16*Math.PI;this._attrPos.setXYZ(68+t,0,Math.sin(i),Math.cos(i)),this._attrPos.setXYZ(100+t,e,Math.sin(i),Math.cos(i))}const t=Math.atan2(Rt.y,Math.sqrt(Rt.x*Rt.x+Rt.z*Rt.z)),i=-Math.atan2(Rt.z,Rt.x);this.rotateZ(t),this.rotateY(i),this.scale(this._currentRadius,this._currentRadius,this._currentRadius),this.translate(this._currentOffset.x,this._currentOffset.y,this._currentOffset.z),this._attrPos.needsUpdate=!0}_buildIndex(){for(let e=0;e<34;e++){const t=(e+1)%34;this._attrIndex.setXY(2*e,e,t),this._attrIndex.setXY(68+2*e,34+e,34+t)}for(let e=0;e<32;e++){const t=(e+1)%32;this._attrIndex.setXY(136+2*e,68+e,68+t),this._attrIndex.setXY(200+2*e,100+e,100+t)}this._attrIndex.needsUpdate=!0}}class Et extends n.BufferGeometry{constructor(e){super(),this.worldScale=1,this._currentRadius=0,this._currentOffset=new n.Vector3,this._shape=e,this._attrPos=new n.BufferAttribute(new Float32Array(288),3),this.setAttribute("position",this._attrPos),this._attrIndex=new n.BufferAttribute(new Uint16Array(192),1),this.setIndex(this._attrIndex),this._buildIndex(),this.update()}update(){let e=!1;const t=this._shape.radius/this.worldScale;this._currentRadius!==t&&(this._currentRadius=t,e=!0),this._currentOffset.equals(this._shape.offset)||(this._currentOffset.copy(this._shape.offset),e=!0),e&&this._buildPosition()}_buildPosition(){for(let e=0;e<32;e++){const t=e/16*Math.PI;this._attrPos.setXYZ(e,Math.cos(t),Math.sin(t),0),this._attrPos.setXYZ(32+e,0,Math.cos(t),Math.sin(t)),this._attrPos.setXYZ(64+e,Math.sin(t),0,Math.cos(t))}this.scale(this._currentRadius,this._currentRadius,this._currentRadius),this.translate(this._currentOffset.x,this._currentOffset.y,this._currentOffset.z),this._attrPos.needsUpdate=!0}_buildIndex(){for(let e=0;e<32;e++){const t=(e+1)%32;this._attrIndex.setXY(2*e,e,t),this._attrIndex.setXY(64+2*e,32+e,32+t),this._attrIndex.setXY(128+2*e,64+e,64+t)}this._attrIndex.needsUpdate=!0}}const wt=new n.Vector3;class Pt extends n.Group{constructor(e){if(super(),this.matrixAutoUpdate=!1,this.collider=e,this.collider.shape instanceof yt)this._geometry=new Et(this.collider.shape);else{if(!(this.collider.shape instanceof xt))throw new Error("VRMSpringBoneColliderHelper: Unknown collider shape type detected");this._geometry=new Tt(this.collider.shape)}const t=new n.LineBasicMaterial({color:16711935,depthTest:!1,depthWrite:!1});this._line=new n.LineSegments(this._geometry,t),this.add(this._line)}dispose(){this._geometry.dispose()}updateMatrixWorld(e){this.collider.updateWorldMatrix(!0,!1),this.matrix.copy(this.collider.matrixWorld);const t=this.matrix.elements;this._geometry.worldScale=wt.set(t[0],t[1],t[2]).length(),this._geometry.update(),super.updateMatrixWorld(e)}}class St extends n.BufferGeometry{constructor(e){super(),this.worldScale=1,this._currentRadius=0,this._currentTail=new n.Vector3,this._springBone=e,this._attrPos=new n.BufferAttribute(new Float32Array(294),3),this.setAttribute("position",this._attrPos),this._attrIndex=new n.BufferAttribute(new Uint16Array(194),1),this.setIndex(this._attrIndex),this._buildIndex(),this.update()}update(){let e=!1;const t=this._springBone.settings.hitRadius/this.worldScale;this._currentRadius!==t&&(this._currentRadius=t,e=!0),this._currentTail.equals(this._springBone.initialLocalChildPosition)||(this._currentTail.copy(this._springBone.initialLocalChildPosition),e=!0),e&&this._buildPosition()}_buildPosition(){for(let e=0;e<32;e++){const t=e/16*Math.PI;this._attrPos.setXYZ(e,Math.cos(t),Math.sin(t),0),this._attrPos.setXYZ(32+e,0,Math.cos(t),Math.sin(t)),this._attrPos.setXYZ(64+e,Math.sin(t),0,Math.cos(t))}this.scale(this._currentRadius,this._currentRadius,this._currentRadius),this.translate(this._currentTail.x,this._currentTail.y,this._currentTail.z),this._attrPos.setXYZ(96,0,0,0),this._attrPos.setXYZ(97,this._currentTail.x,this._currentTail.y,this._currentTail.z),this._attrPos.needsUpdate=!0}_buildIndex(){for(let e=0;e<32;e++){const t=(e+1)%32;this._attrIndex.setXY(2*e,e,t),this._attrIndex.setXY(64+2*e,32+e,32+t),this._attrIndex.setXY(128+2*e,64+e,64+t)}this._attrIndex.setXY(192,96,97),this._attrIndex.needsUpdate=!0}}const Lt=new n.Vector3;class At extends n.Group{constructor(e){super(),this.matrixAutoUpdate=!1,this.springBone=e,this._geometry=new St(this.springBone);const t=new n.LineBasicMaterial({color:16776960,depthTest:!1,depthWrite:!1});this._line=new n.LineSegments(this._geometry,t),this.add(this._line)}dispose(){this._geometry.dispose()}updateMatrixWorld(e){this.springBone.bone.updateWorldMatrix(!0,!1),this.matrix.copy(this.springBone.bone.matrixWorld);const t=this.matrix.elements;this._geometry.worldScale=Lt.set(t[0],t[1],t[2]).length(),this._geometry.update(),super.updateMatrixWorld(e)}}class It extends n.Object3D{constructor(e){super(),this.shape=e}}const bt=new n.Matrix4;function Ut(e){return e.invert?e.invert():e.getInverse(bt.copy(e)),e}class Vt{get inverse(){return this._shouldUpdateInverse&&(this._inverseCache.copy(this.matrix),Ut(this._inverseCache),this._shouldUpdateInverse=!1),this._inverseCache}constructor(e){this._inverseCache=new n.Matrix4,this._shouldUpdateInverse=!0,this.matrix=e;const t={set:(e,t,i)=>(this._shouldUpdateInverse=!0,e[t]=i,!0)};this._originalElements=e.elements,e.elements=new Proxy(e.elements,t)}revert(){this.matrix.elements=this._originalElements}}const Ot=new n.Matrix4,Nt=new n.Vector3,Ct=new n.Vector3,Dt=new n.Vector3,Ft=new n.Vector3,Ht=new n.Vector3,Bt=new n.Vector3,kt=new n.Quaternion,Wt=new n.Matrix4,zt=new n.Matrix4;class jt{get center(){return this._center}set center(e){var t;(null===(t=this._center)||void 0===t?void 0:t.userData.inverseCacheProxy)&&(this._center.userData.inverseCacheProxy.revert(),delete this._center.userData.inverseCacheProxy),this._center=e,this._center&&(this._center.userData.inverseCacheProxy||(this._center.userData.inverseCacheProxy=new Vt(this._center.matrixWorld)))}get initialLocalChildPosition(){return this._initialLocalChildPosition}get _parentMatrixWorld(){return this.bone.parent?this.bone.parent.matrixWorld:Ot}constructor(e,t,i={},r=[]){var o,s,a,l,u,d;this._currentTail=new n.Vector3,this._prevTail=new n.Vector3,this._boneAxis=new n.Vector3,this._worldSpaceBoneLength=0,this._center=null,this._initialLocalMatrix=new n.Matrix4,this._initialLocalRotation=new n.Quaternion,this._initialLocalChildPosition=new n.Vector3,this.bone=e,this.bone.matrixAutoUpdate=!1,this.child=t,this.settings={hitRadius:null!==(o=i.hitRadius)&&void 0!==o?o:0,stiffness:null!==(s=i.stiffness)&&void 0!==s?s:1,gravityPower:null!==(a=i.gravityPower)&&void 0!==a?a:0,gravityDir:null!==(u=null===(l=i.gravityDir)||void 0===l?void 0:l.clone())&&void 0!==u?u:new n.Vector3(0,-1,0),dragForce:null!==(d=i.dragForce)&&void 0!==d?d:.4},this.colliderGroups=r}setInitState(){this._initialLocalMatrix.copy(this.bone.matrix),this._initialLocalRotation.copy(this.bone.quaternion),this.child?this._initialLocalChildPosition.copy(this.child.position):this._initialLocalChildPosition.copy(this.bone.position).normalize().multiplyScalar(.07);const e=this._getMatrixWorldToCenter(Wt);this.bone.localToWorld(this._currentTail.copy(this._initialLocalChildPosition)).applyMatrix4(e),this._prevTail.copy(this._currentTail),this._boneAxis.copy(this._initialLocalChildPosition).normalize()}reset(){this.bone.quaternion.copy(this._initialLocalRotation),this.bone.updateMatrix(),this.bone.matrixWorld.multiplyMatrices(this._parentMatrixWorld,this.bone.matrix);const e=this._getMatrixWorldToCenter(Wt);this.bone.localToWorld(this._currentTail.copy(this._initialLocalChildPosition)).applyMatrix4(e),this._prevTail.copy(this._currentTail)}update(e){if(e<=0)return;this._calcWorldSpaceBoneLength(),Ft.setFromMatrixPosition(this.bone.matrixWorld);let t=this._getMatrixWorldToCenter(Wt);Ht.copy(Ft).applyMatrix4(t);const i=kt.setFromRotationMatrix(t),n=zt.copy(t).multiply(this._parentMatrixWorld),r=Ct.copy(this._boneAxis).applyMatrix4(this._initialLocalMatrix).applyMatrix4(n).sub(Ht).normalize(),o=Dt.copy(this.settings.gravityDir).applyQuaternion(i).normalize(),s=this._getMatrixCenterToWorld(Wt);Bt.copy(this._currentTail).add(Nt.copy(this._currentTail).sub(this._prevTail).multiplyScalar(1-this.settings.dragForce)).add(Nt.copy(r).multiplyScalar(this.settings.stiffness*e)).add(Nt.copy(o).multiplyScalar(this.settings.gravityPower*e)).applyMatrix4(s),Bt.sub(Ft).normalize().multiplyScalar(this._worldSpaceBoneLength).add(Ft),this._collision(Bt),t=this._getMatrixWorldToCenter(Wt),this._prevTail.copy(this._currentTail),this._currentTail.copy(Nt.copy(Bt).applyMatrix4(t));const a=Ut(Wt.copy(this._parentMatrixWorld).multiply(this._initialLocalMatrix)),l=kt.setFromUnitVectors(this._boneAxis,Nt.copy(Bt).applyMatrix4(a).normalize());this.bone.quaternion.copy(this._initialLocalRotation).multiply(l),this.bone.updateMatrix(),this.bone.matrixWorld.multiplyMatrices(this._parentMatrixWorld,this.bone.matrix)}_collision(e){this.colliderGroups.forEach((t=>{t.colliders.forEach((t=>{const i=t.shape.calculateCollision(t.matrixWorld,e,this.settings.hitRadius,Nt);i<0&&(e.add(Nt.multiplyScalar(-i)),e.sub(Ft).normalize().multiplyScalar(this._worldSpaceBoneLength).add(Ft))}))}))}_calcWorldSpaceBoneLength(){Nt.setFromMatrixPosition(this.bone.matrixWorld),this.child?Ct.setFromMatrixPosition(this.child.matrixWorld):(Ct.copy(this._initialLocalChildPosition),Ct.applyMatrix4(this.bone.matrixWorld)),this._worldSpaceBoneLength=Nt.sub(Ct).length()}_getMatrixCenterToWorld(e){return this._center?e.copy(this._center.matrixWorld):e.identity(),e}_getMatrixWorldToCenter(e){return this._center?e.copy(this._center.userData.inverseCacheProxy.inverse):e.identity(),e}}function Qt(e,t,i,n){return new(i||(i=Promise))((function(r,o){function s(e){try{l(n.next(e))}catch(e){o(e)}}function a(e){try{l(n.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(s,a)}l((n=n.apply(e,t||[])).next())}))}function Xt(e,t){const i=[];let n=e;for(;null!==n;)i.unshift(n),n=n.parent;i.forEach((e=>{t(e)}))}function Gt(e,t){e.children.forEach((e=>{t(e)||Gt(e,t)}))}class Yt{constructor(){this._joints=new Set,this._objectSpringBonesMap=new Map}get joints(){return this._joints}get springBones(){return console.warn("VRMSpringBoneManager: springBones is deprecated. use joints instead."),this._joints}get colliderGroups(){const e=new Set;return this._joints.forEach((t=>{t.colliderGroups.forEach((t=>{e.add(t)}))})),Array.from(e)}get colliders(){const e=new Set;return this.colliderGroups.forEach((t=>{t.colliders.forEach((t=>{e.add(t)}))})),Array.from(e)}addJoint(e){this._joints.add(e);let t=this._objectSpringBonesMap.get(e.bone);null==t&&(t=new Set,this._objectSpringBonesMap.set(e.bone,t)),t.add(e)}addSpringBone(e){console.warn("VRMSpringBoneManager: addSpringBone() is deprecated. use addJoint() instead."),this.addJoint(e)}deleteJoint(e){this._joints.delete(e);this._objectSpringBonesMap.get(e.bone).delete(e)}deleteSpringBone(e){console.warn("VRMSpringBoneManager: deleteSpringBone() is deprecated. use deleteJoint() instead."),this.deleteJoint(e)}setInitState(){const e=new Set,t=new Set,i=new Set;for(const n of this._joints)this._processSpringBone(n,e,t,i,(e=>e.setInitState()))}reset(){const e=new Set,t=new Set,i=new Set;for(const n of this._joints)this._processSpringBone(n,e,t,i,(e=>e.reset()))}update(e){const t=new Set,i=new Set,n=new Set;for(const r of this._joints)this._processSpringBone(r,t,i,n,(t=>t.update(e))),Gt(r.bone,(e=>{var t,i;return(null!==(i=null===(t=this._objectSpringBonesMap.get(e))||void 0===t?void 0:t.size)&&void 0!==i?i:0)>0||(e.updateWorldMatrix(!1,!1),!1)}))}_processSpringBone(e,t,i,n,r){if(i.has(e))return;if(t.has(e))throw new Error("VRMSpringBoneManager: Circular dependency detected while updating springbones");t.add(e);const o=this._getDependencies(e);for(const e of o)Xt(e,(e=>{const o=this._objectSpringBonesMap.get(e);if(o)for(const e of o)this._processSpringBone(e,t,i,n,r);else n.has(e)||(e.updateWorldMatrix(!1,!1),n.add(e))}));e.bone.updateMatrix(),e.bone.updateWorldMatrix(!1,!1),r(e),n.add(e.bone),i.add(e)}_getDependencies(e){const t=new Set,i=e.bone.parent;return i&&t.add(i),e.colliderGroups.forEach((e=>{e.colliders.forEach((e=>{t.add(e)}))})),t}}const qt=new Set(["1.0","1.0-beta"]);class $t{get name(){return $t.EXTENSION_NAME}constructor(e,t){this.parser=e,this.jointHelperRoot=null==t?void 0:t.jointHelperRoot,this.colliderHelperRoot=null==t?void 0:t.colliderHelperRoot}afterRoot(e){return Qt(this,void 0,void 0,(function*(){e.userData.vrmSpringBoneManager=yield this._import(e)}))}_import(e){return Qt(this,void 0,void 0,(function*(){const t=yield this._v1Import(e);if(null!=t)return t;const i=yield this._v0Import(e);return null!=i?i:null}))}_v1Import(e){var t,i,r,o,s;return Qt(this,void 0,void 0,(function*(){const a=e.parser.json;if(!(-1!==(null===(t=a.extensionsUsed)||void 0===t?void 0:t.indexOf($t.EXTENSION_NAME))))return null;const l=new Yt,u=yield e.parser.getDependencies("node"),d=null===(i=a.extensions)||void 0===i?void 0:i[$t.EXTENSION_NAME];if(!d)return null;const h=d.specVersion;if(!qt.has(h))return console.warn(`VRMSpringBoneLoaderPlugin: Unknown ${$t.EXTENSION_NAME} specVersion "${h}"`),null;const c=null===(r=d.colliders)||void 0===r?void 0:r.map(((e,t)=>{var i,r,o,s,a;const l=u[e.node],d=e.shape;if(d.sphere)return this._importSphereCollider(l,{offset:(new n.Vector3).fromArray(null!==(i=d.sphere.offset)&&void 0!==i?i:[0,0,0]),radius:null!==(r=d.sphere.radius)&&void 0!==r?r:0});if(d.capsule)return this._importCapsuleCollider(l,{offset:(new n.Vector3).fromArray(null!==(o=d.capsule.offset)&&void 0!==o?o:[0,0,0]),radius:null!==(s=d.capsule.radius)&&void 0!==s?s:0,tail:(new n.Vector3).fromArray(null!==(a=d.capsule.tail)&&void 0!==a?a:[0,0,0])});throw new Error(`VRMSpringBoneLoaderPlugin: The collider #${t} has no valid shape`)})),p=null===(o=d.colliderGroups)||void 0===o?void 0:o.map(((e,t)=>{var i;return{colliders:(null!==(i=e.colliders)&&void 0!==i?i:[]).map((e=>{const i=null==c?void 0:c[e];if(null==i)throw new Error(`VRMSpringBoneLoaderPlugin: The colliderGroup #${t} attempted to use a collider #${e} but not found`);return i})),name:e.name}}));return null===(s=d.springs)||void 0===s||s.forEach(((e,t)=>{var i;const r=e.joints,o=null===(i=e.colliderGroups)||void 0===i?void 0:i.map((e=>{const i=null==p?void 0:p[e];if(null==i)throw new Error(`VRMSpringBoneLoaderPlugin: The spring #${t} attempted to use a colliderGroup ${e} but not found`);return i})),s=null!=e.center?u[e.center]:void 0;let a;r.forEach((e=>{if(a){const t=a.node,i=u[t],r=e.node,d=u[r],h={hitRadius:a.hitRadius,dragForce:a.dragForce,gravityPower:a.gravityPower,stiffness:a.stiffness,gravityDir:null!=a.gravityDir?(new n.Vector3).fromArray(a.gravityDir):void 0},c=this._importJoint(i,d,h,o);s&&(c.center=s),l.addJoint(c)}a=e}))})),l.setInitState(),l}))}_v0Import(e){var t,i,r;return Qt(this,void 0,void 0,(function*(){const o=e.parser.json;if(!(-1!==(null===(t=o.extensionsUsed)||void 0===t?void 0:t.indexOf("VRM"))))return null;const s=null===(i=o.extensions)||void 0===i?void 0:i.VRM,a=null==s?void 0:s.secondaryAnimation;if(!a)return null;const l=null==a?void 0:a.boneGroups;if(!l)return null;const u=new Yt,d=yield e.parser.getDependencies("node"),h=null===(r=a.colliderGroups)||void 0===r?void 0:r.map((e=>{var t;const i=d[e.node],r=(null!==(t=e.colliders)&&void 0!==t?t:[]).map(((e,t)=>{var r,o,s;const a=new n.Vector3(0,0,0);return e.offset&&a.set(null!==(r=e.offset.x)&&void 0!==r?r:0,null!==(o=e.offset.y)&&void 0!==o?o:0,e.offset.z?-e.offset.z:0),this._importSphereCollider(i,{offset:a,radius:null!==(s=e.radius)&&void 0!==s?s:0})}));return{colliders:r}}));return null==l||l.forEach(((e,t)=>{const i=e.bones;i&&i.forEach((i=>{var r,o,s,a;const l=d[i],c=new n.Vector3;e.gravityDir?c.set(null!==(r=e.gravityDir.x)&&void 0!==r?r:0,null!==(o=e.gravityDir.y)&&void 0!==o?o:0,null!==(s=e.gravityDir.z)&&void 0!==s?s:0):c.set(0,-1,0);const p=null!=e.center?d[e.center]:void 0,m={hitRadius:e.hitRadius,dragForce:e.dragForce,gravityPower:e.gravityPower,stiffness:e.stiffiness,gravityDir:c},f=null===(a=e.colliderGroups)||void 0===a?void 0:a.map((e=>{const i=null==h?void 0:h[e];if(null==i)throw new Error(`VRMSpringBoneLoaderPlugin: The spring #${t} attempted to use a colliderGroup ${e} but not found`);return i}));l.traverse((e=>{var t;const i=null!==(t=e.children[0])&&void 0!==t?t:null,n=this._importJoint(e,i,m,f);p&&(n.center=p),u.addJoint(n)}))}))})),e.scene.updateMatrixWorld(),u.setInitState(),u}))}_importJoint(e,t,i,n){const r=new jt(e,t,i,n);if(this.jointHelperRoot){const e=new At(r);this.jointHelperRoot.add(e),e.renderOrder=this.jointHelperRoot.renderOrder}return r}_importSphereCollider(e,t){const{offset:i,radius:n}=t,r=new yt({offset:i,radius:n}),o=new It(r);if(e.add(o),this.colliderHelperRoot){const e=new Pt(o);this.colliderHelperRoot.add(e),e.renderOrder=this.colliderHelperRoot.renderOrder}return o}_importCapsuleCollider(e,t){const{offset:i,radius:n,tail:r}=t,o=new xt({offset:i,radius:n,tail:r}),s=new It(o);if(e.add(s),this.colliderHelperRoot){const e=new Pt(s);this.colliderHelperRoot.add(e),e.renderOrder=this.colliderHelperRoot.renderOrder}return s}}$t.EXTENSION_NAME="VRMC_springBone";function Zt(e){if(Object.values(e).forEach((e=>{if(null==e?void 0:e.isTexture){e.dispose()}})),e.isShaderMaterial){const t=e.uniforms;t&&Object.values(t).forEach((e=>{const t=e.value;if(null==t?void 0:t.isTexture){t.dispose()}}))}e.dispose()}function Jt(e){const t=e.geometry;t&&t.dispose();const i=e.skeleton;i&&i.dispose();const n=e.material;n&&(Array.isArray(n)?n.forEach((e=>Zt(e))):n&&Zt(n))}class Kt{constructor(){}}Kt.deepDispose=function(e){e.traverse(Jt)},Kt.removeUnnecessaryJoints=function(e){const t=new Map;e.traverse((e=>{if("SkinnedMesh"!==e.type)return;const i=e,r=i.geometry.getAttribute("skinIndex");let o=t.get(r);if(!o){const e=[],s=[],a={},l=r.array;for(let t=0;t<l.length;t++){const n=l[t];void 0===a[n]&&(a[n]=e.length,e.push(i.skeleton.bones[n]),s.push(i.skeleton.boneInverses[n])),l[t]=a[n]}r.copyArray(l),r.needsUpdate=!0,o=new n.Skeleton(e,s),t.set(r,o)}i.bind(o,new n.Matrix4)}))},Kt.removeUnnecessaryVertices=function(e){const i=new Map;e.traverse((e=>{var r,o,s,a;if(!e.isMesh)return;const l=e,u=l.geometry,d=u.index;if(null==d)return;const h=i.get(u);if(null!=h)return void(l.geometry=h);const c=new n.BufferGeometry;c.name=u.name,c.morphTargetsRelative=u.morphTargetsRelative,u.groups.forEach((e=>{c.addGroup(e.start,e.count,e.materialIndex)})),c.boundingBox=null!==(o=null===(r=u.boundingBox)||void 0===r?void 0:r.clone())&&void 0!==o?o:null,c.boundingSphere=null!==(a=null===(s=u.boundingSphere)||void 0===s?void 0:s.clone())&&void 0!==a?a:null,c.setDrawRange(u.drawRange.start,u.drawRange.count),c.userData=u.userData,i.set(u,c);const p=[],m=[];{const e=d.array,i=new e.constructor(e.length);let n=0;for(let t=0;t<e.length;t++){const r=e[t];let o=p[r];null==o&&(p[r]=n,m[n]=r,o=n,n++),i[t]=o}c.setIndex(new t.BufferAttribute(i,1,!1))}Object.keys(u.attributes).forEach((e=>{const i=u.attributes[e];if(i.isInterleavedBufferAttribute)throw new Error("removeUnnecessaryVertices: InterleavedBufferAttribute is not supported");const n=i.array,{itemSize:r,normalized:o}=i,s=new n.constructor(m.length*r);m.forEach(((e,t)=>{for(let i=0;i<r;i++)s[t*r+i]=n[e*r+i]})),c.setAttribute(e,new t.BufferAttribute(s,r,o))}));let f=!0;Object.keys(u.morphAttributes).forEach((e=>{c.morphAttributes[e]=[];const i=u.morphAttributes[e];for(let n=0;n<i.length;n++){const r=i[n];if(r.isInterleavedBufferAttribute)throw new Error("removeUnnecessaryVertices: InterleavedBufferAttribute is not supported");const o=r.array,{itemSize:s,normalized:a}=r,l=new o.constructor(m.length*s);m.forEach(((e,t)=>{for(let i=0;i<s;i++)l[t*s+i]=o[e*s+i]})),f=f&&l.every((e=>0===e)),c.morphAttributes[e][n]=new t.BufferAttribute(l,s,a)}})),f&&(c.morphAttributes={}),l.geometry=c})),Array.from(i.keys()).forEach((e=>{e.dispose()}))},Kt.rotateVRM0=function(e){var t;"0"===(null===(t=e.meta)||void 0===t?void 0:t.metaVersion)&&(e.scene.rotation.y=Math.PI)},e.MToonMaterial=Ne,e.MToonMaterialDebugMode=be,e.MToonMaterialLoaderPlugin=He,e.MToonMaterialOutlineWidthMode=Ue,e.VRM=Le,e.VRMAimConstraint=rt,e.VRMCore=Se,e.VRMCoreLoaderPlugin=class{get name(){return"VRMC_vrm"}constructor(e,t){var i,n,r,o,s;this.parser=e;const a=null==t?void 0:t.helperRoot,l=null==t?void 0:t.autoUpdateHumanBones;this.expressionPlugin=null!==(i=null==t?void 0:t.expressionPlugin)&&void 0!==i?i:new y(e),this.firstPersonPlugin=null!==(n=null==t?void 0:t.firstPersonPlugin)&&void 0!==n?n:new E(e),this.humanoidPlugin=null!==(r=null==t?void 0:t.humanoidPlugin)&&void 0!==r?r:new z(e,{helperRoot:a,autoUpdateHumanBones:l}),this.lookAtPlugin=null!==(o=null==t?void 0:t.lookAtPlugin)&&void 0!==o?o:new Ee(e,{helperRoot:a}),this.metaPlugin=null!==(s=null==t?void 0:t.metaPlugin)&&void 0!==s?s:new Pe(e)}afterRoot(e){return o(this,void 0,void 0,(function*(){yield this.metaPlugin.afterRoot(e),yield this.humanoidPlugin.afterRoot(e),yield this.expressionPlugin.afterRoot(e),yield this.lookAtPlugin.afterRoot(e),yield this.firstPersonPlugin.afterRoot(e);const t=e.userData.vrmMeta,i=e.userData.vrmHumanoid;if(t&&i){const n=new Se({scene:e.scene,expressionManager:e.userData.vrmExpressionManager,firstPerson:e.userData.vrmFirstPerson,humanoid:i,lookAt:e.userData.vrmLookAt,meta:t});e.userData.vrmCore=n}}))}},e.VRMExpression=r,e.VRMExpressionLoaderPlugin=y,e.VRMExpressionManager=c,e.VRMExpressionMaterialColorBind=g,e.VRMExpressionMaterialColorType=p,e.VRMExpressionMorphTargetBind=v,e.VRMExpressionOverrideType={None:"none",Block:"block",Blend:"blend"},e.VRMExpressionPresetName=d,e.VRMExpressionTextureTransformBind=M,e.VRMFirstPerson=R,e.VRMFirstPersonLoaderPlugin=E,e.VRMFirstPersonMeshAnnotationType={Auto:"auto",Both:"both",ThirdPersonOnly:"thirdPersonOnly",FirstPersonOnly:"firstPersonOnly"},e.VRMHumanBoneList=A,e.VRMHumanBoneName={Hips:"hips",Spine:"spine",Chest:"chest",UpperChest:"upperChest",Neck:"neck",Head:"head",LeftEye:"leftEye",RightEye:"rightEye",Jaw:"jaw",LeftUpperLeg:"leftUpperLeg",LeftLowerLeg:"leftLowerLeg",LeftFoot:"leftFoot",LeftToes:"leftToes",RightUpperLeg:"rightUpperLeg",RightLowerLeg:"rightLowerLeg",RightFoot:"rightFoot",RightToes:"rightToes",LeftShoulder:"leftShoulder",LeftUpperArm:"leftUpperArm",LeftLowerArm:"leftLowerArm",LeftHand:"leftHand",RightShoulder:"rightShoulder",RightUpperArm:"rightUpperArm",RightLowerArm:"rightLowerArm",RightHand:"rightHand",LeftThumbMetacarpal:"leftThumbMetacarpal",LeftThumbProximal:"leftThumbProximal",LeftThumbDistal:"leftThumbDistal",LeftIndexProximal:"leftIndexProximal",LeftIndexIntermediate:"leftIndexIntermediate",LeftIndexDistal:"leftIndexDistal",LeftMiddleProximal:"leftMiddleProximal",LeftMiddleIntermediate:"leftMiddleIntermediate",LeftMiddleDistal:"leftMiddleDistal",LeftRingProximal:"leftRingProximal",LeftRingIntermediate:"leftRingIntermediate",LeftRingDistal:"leftRingDistal",LeftLittleProximal:"leftLittleProximal",LeftLittleIntermediate:"leftLittleIntermediate",LeftLittleDistal:"leftLittleDistal",RightThumbMetacarpal:"rightThumbMetacarpal",RightThumbProximal:"rightThumbProximal",RightThumbDistal:"rightThumbDistal",RightIndexProximal:"rightIndexProximal",RightIndexIntermediate:"rightIndexIntermediate",RightIndexDistal:"rightIndexDistal",RightMiddleProximal:"rightMiddleProximal",RightMiddleIntermediate:"rightMiddleIntermediate",RightMiddleDistal:"rightMiddleDistal",RightRingProximal:"rightRingProximal",RightRingIntermediate:"rightRingIntermediate",RightRingDistal:"rightRingDistal",RightLittleProximal:"rightLittleProximal",RightLittleIntermediate:"rightLittleIntermediate",RightLittleDistal:"rightLittleDistal"},e.VRMHumanBoneParentMap=I,e.VRMHumanoid=H,e.VRMHumanoidHelper=L,e.VRMHumanoidLoaderPlugin=z,e.VRMLoaderPlugin=class{get name(){return"VRMLoaderPlugin"}constructor(e,t){var i,n,r,o,s,a,l,u,d,h;this.parser=e;const c=null==t?void 0:t.helperRoot,p=null==t?void 0:t.autoUpdateHumanBones;this.expressionPlugin=null!==(i=null==t?void 0:t.expressionPlugin)&&void 0!==i?i:new y(e),this.firstPersonPlugin=null!==(n=null==t?void 0:t.firstPersonPlugin)&&void 0!==n?n:new E(e),this.humanoidPlugin=null!==(r=null==t?void 0:t.humanoidPlugin)&&void 0!==r?r:new z(e,{helperRoot:c,autoUpdateHumanBones:p}),this.lookAtPlugin=null!==(o=null==t?void 0:t.lookAtPlugin)&&void 0!==o?o:new Ee(e,{helperRoot:c}),this.metaPlugin=null!==(s=null==t?void 0:t.metaPlugin)&&void 0!==s?s:new Pe(e),this.mtoonMaterialPlugin=null!==(a=null==t?void 0:t.mtoonMaterialPlugin)&&void 0!==a?a:new He(e),this.materialsHDREmissiveMultiplierPlugin=null!==(l=null==t?void 0:t.materialsHDREmissiveMultiplierPlugin)&&void 0!==l?l:new ke(e),this.materialsV0CompatPlugin=null!==(u=null==t?void 0:t.materialsV0CompatPlugin)&&void 0!==u?u:new je(e),this.springBonePlugin=null!==(d=null==t?void 0:t.springBonePlugin)&&void 0!==d?d:new $t(e,{colliderHelperRoot:c,jointHelperRoot:c}),this.nodeConstraintPlugin=null!==(h=null==t?void 0:t.nodeConstraintPlugin)&&void 0!==h?h:new gt(e,{helperRoot:c})}beforeRoot(){return Ae(this,void 0,void 0,(function*(){yield this.materialsV0CompatPlugin.beforeRoot(),yield this.mtoonMaterialPlugin.beforeRoot()}))}loadMesh(e){return Ae(this,void 0,void 0,(function*(){return yield this.mtoonMaterialPlugin.loadMesh(e)}))}getMaterialType(e){const t=this.mtoonMaterialPlugin.getMaterialType(e);return null!=t?t:null}extendMaterialParams(e,t){return Ae(this,void 0,void 0,(function*(){yield this.materialsHDREmissiveMultiplierPlugin.extendMaterialParams(e,t),yield this.mtoonMaterialPlugin.extendMaterialParams(e,t)}))}afterRoot(e){return Ae(this,void 0,void 0,(function*(){yield this.metaPlugin.afterRoot(e),yield this.humanoidPlugin.afterRoot(e),yield this.expressionPlugin.afterRoot(e),yield this.lookAtPlugin.afterRoot(e),yield this.firstPersonPlugin.afterRoot(e),yield this.springBonePlugin.afterRoot(e),yield this.nodeConstraintPlugin.afterRoot(e),yield this.mtoonMaterialPlugin.afterRoot(e);const t=e.userData.vrmMeta,i=e.userData.vrmHumanoid;if(t&&i){const n=new Le({scene:e.scene,expressionManager:e.userData.vrmExpressionManager,firstPerson:e.userData.vrmFirstPerson,humanoid:i,lookAt:e.userData.vrmLookAt,meta:t,materials:e.userData.vrmMToonMaterials,springBoneManager:e.userData.vrmSpringBoneManager,nodeConstraintManager:e.userData.vrmNodeConstraintManager});e.userData.vrm=n}}))}},e.VRMLookAt=me,e.VRMLookAtBoneApplier=Me,e.VRMLookAtExpressionApplier=xe,e.VRMLookAtHelper=K,e.VRMLookAtLoaderPlugin=Ee,e.VRMLookAtRangeMap=ye,e.VRMLookAtTypeName={Bone:"bone",Expression:"expression"},e.VRMMetaLoaderPlugin=Pe,e.VRMNodeConstraint=Ze,e.VRMNodeConstraintHelper=Xe,e.VRMNodeConstraintLoaderPlugin=gt,e.VRMNodeConstraintManager=at,e.VRMRequiredHumanBoneName=B,e.VRMRollConstraint=mt,e.VRMRotationConstraint=dt,e.VRMSpringBoneCollider=It,e.VRMSpringBoneColliderHelper=Pt,e.VRMSpringBoneColliderShape=vt,e.VRMSpringBoneColliderShapeCapsule=xt,e.VRMSpringBoneColliderShapeSphere=yt,e.VRMSpringBoneJoint=jt,e.VRMSpringBoneJointHelper=At,e.VRMSpringBoneLoaderPlugin=$t,e.VRMSpringBoneManager=Yt,e.VRMUtils=Kt,Object.defineProperty(e,"__esModule",{value:!0}),Object.assign(t,e)}));
