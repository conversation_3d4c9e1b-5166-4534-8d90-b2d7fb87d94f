{"models": [{"id": "zome", "name": "Zome", "filename": "Zome.vrm", "description": "来自Mate-Engine项目的默认角色Zome，适合作为主要测试模型", "source": "Mate-Engine", "vrm_version": "0.x", "size_mb": 26.1, "features": {"expressions": true, "eye_tracking": true, "lip_sync": true, "physics": true}, "recommended_use": "primary", "notes": "高质量模型，表情丰富，适合作为默认角色"}, {"id": "alice", "name": "<PERSON>", "filename": "Alice.vrm", "description": "来自super-agent-party项目的Alice角色，轻量级测试模型", "source": "super-agent-party", "vrm_version": "1.0", "size_mb": 15.9, "features": {"expressions": true, "eye_tracking": true, "lip_sync": true, "physics": false}, "recommended_use": "testing", "notes": "VRM 1.0格式，适合测试新版本兼容性"}, {"id": "aldina", "name": "Aldina", "filename": "aldina.vrm", "description": "来自Mate-Engine DLC的Aldina角色，中等大小模型", "source": "Mate-Engine DLC", "vrm_version": "0.x", "size_mb": 12.2, "features": {"expressions": true, "eye_tracking": true, "lip_sync": true, "physics": true}, "recommended_use": "alternative", "notes": "DLC角色，可作为备选测试模型"}], "default_model": "zome", "fallback_model": "alice", "loading_order": ["zome", "alice", "aldina"], "version": "1.0", "last_updated": "2025-07-24"}