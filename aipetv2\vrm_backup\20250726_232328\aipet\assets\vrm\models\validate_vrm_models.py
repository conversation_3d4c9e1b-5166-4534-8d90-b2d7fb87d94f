#!/usr/bin/env python3
"""
VRM模型验证脚本
用于检查复制的VRM模型文件的有效性和基本信息
"""

import os
import json
import struct
from pathlib import Path
from typing import Dict, List, Optional

class VRMValidator:
    """VRM模型验证器"""
    
    def __init__(self, models_dir: str):
        self.models_dir = Path(models_dir)
        self.config_file = self.models_dir / "model_configs.json"
        
    def validate_all_models(self) -> Dict[str, bool]:
        """验证所有VRM模型"""
        results = {}
        
        print("🔍 开始验证VRM模型...")
        print(f"📁 模型目录: {self.models_dir}")
        
        # 加载配置文件
        config = self.load_config()
        if not config:
            print("❌ 无法加载模型配置文件")
            return results
        
        # 验证每个模型
        for model in config.get('models', []):
            model_id = model['id']
            filename = model['filename']
            filepath = self.models_dir / filename
            
            print(f"\n📋 验证模型: {model['name']} ({model_id})")
            print(f"   文件: {filename}")
            
            # 检查文件是否存在
            if not filepath.exists():
                print(f"   ❌ 文件不存在: {filepath}")
                results[model_id] = False
                continue
            
            # 检查文件大小
            file_size = filepath.stat().st_size
            size_mb = file_size / (1024 * 1024)
            print(f"   📏 文件大小: {size_mb:.1f} MB")
            
            # 验证VRM格式
            is_valid = self.validate_vrm_format(filepath)
            if is_valid:
                print(f"   ✅ VRM格式验证通过")
                
                # 检测VRM版本
                version = self.detect_vrm_version(filepath)
                if version:
                    print(f"   🏷️  VRM版本: {version}")
                    
                    # 验证配置中的版本信息
                    expected_version = model.get('vrm_version', 'unknown')
                    if version != expected_version:
                        print(f"   ⚠️  版本不匹配: 期望{expected_version}, 实际{version}")
                
            else:
                print(f"   ❌ VRM格式验证失败")
            
            results[model_id] = is_valid
        
        # 输出总结
        self.print_summary(results)
        return results
    
    def load_config(self) -> Optional[Dict]:
        """加载模型配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 加载配置文件失败: {e}")
            return None
    
    def validate_vrm_format(self, filepath: Path) -> bool:
        """验证VRM文件格式"""
        try:
            with open(filepath, 'rb') as f:
                # 检查GLTF魔数
                magic = f.read(4)
                if magic != b'glTF':
                    return False
                
                # 检查版本
                version = struct.unpack('<I', f.read(4))[0]
                if version != 2:
                    return False
                
                # 检查文件长度
                length = struct.unpack('<I', f.read(4))[0]
                actual_size = filepath.stat().st_size
                
                if length != actual_size:
                    print(f"   ⚠️  文件长度不匹配: 头部{length}, 实际{actual_size}")
                
                return True
                
        except Exception as e:
            print(f"   ❌ 格式验证错误: {e}")
            return False
    
    def detect_vrm_version(self, filepath: Path) -> Optional[str]:
        """检测VRM版本"""
        try:
            with open(filepath, 'rb') as f:
                # 跳过GLTF头部
                f.seek(12)
                
                # 读取JSON chunk
                chunk_length = struct.unpack('<I', f.read(4))[0]
                chunk_type = f.read(4)
                
                if chunk_type != b'JSON':
                    return None
                
                json_data = f.read(chunk_length).decode('utf-8')
                gltf_json = json.loads(json_data)
                
                # 检查VRM扩展
                extensions = gltf_json.get('extensions', {})
                
                if 'VRMC_vrm' in extensions:
                    return '1.0'
                elif 'VRM' in extensions:
                    return '0.x'
                else:
                    return 'unknown'
                    
        except Exception as e:
            print(f"   ❌ 版本检测错误: {e}")
            return None
    
    def print_summary(self, results: Dict[str, bool]):
        """打印验证总结"""
        print("\n" + "="*50)
        print("📊 VRM模型验证总结:")
        
        valid_count = sum(1 for v in results.values() if v)
        total_count = len(results)
        
        for model_id, is_valid in results.items():
            status = "✅ 有效" if is_valid else "❌ 无效"
            print(f"   {model_id}: {status}")
        
        print(f"\n📈 验证结果:")
        print(f"   ✅ 有效模型: {valid_count}")
        print(f"   ❌ 无效模型: {total_count - valid_count}")
        print(f"   📊 成功率: {valid_count/total_count*100:.1f}%")
        
        if valid_count == total_count:
            print("\n🎉 所有VRM模型验证通过！可以开始VRM集成开发了！")
        else:
            print("\n⚠️  部分模型验证失败，请检查文件完整性")

def main():
    """主函数"""
    # 获取当前脚本所在目录
    current_dir = Path(__file__).parent
    
    # 创建验证器并运行验证
    validator = VRMValidator(current_dir)
    results = validator.validate_all_models()
    
    return all(results.values())

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
