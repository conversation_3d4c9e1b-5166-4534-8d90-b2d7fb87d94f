<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Libraries</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>VRM 库加载调试</h1>
    <div id="status-container"></div>

    <!-- JavaScript库文件 -->
    <script src="../libs/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.147.0/examples/js/loaders/GLTFLoader.js"></script>
    <script src="../libs/three-vrm.min.js"></script>
    <script src="../libs/three-vrm-v0.min.js"></script>

    <script>
        function addStatus(message, type = 'info') {
            const container = document.getElementById('status-container');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = message;
            container.appendChild(div);
            console.log(message);
        }

        // 检查库加载状态
        document.addEventListener('DOMContentLoaded', function() {
            addStatus('开始检查库加载状态...', 'info');

            // 检查 THREE.js
            if (typeof THREE !== 'undefined') {
                addStatus('✅ THREE.js 已加载', 'success');
                addStatus(`THREE 版本: ${THREE.REVISION}`, 'info');
            } else {
                addStatus('❌ THREE.js 未加载', 'error');
                return;
            }

            // 检查 GLTFLoader
            if (typeof THREE.GLTFLoader !== 'undefined') {
                addStatus('✅ GLTFLoader 已加载', 'success');
                try {
                    const loader = new THREE.GLTFLoader();
                    addStatus('✅ GLTFLoader 可以实例化', 'success');
                } catch (error) {
                    addStatus('❌ GLTFLoader 实例化失败: ' + error.message, 'error');
                }
            } else {
                addStatus('❌ GLTFLoader 未加载', 'error');
            }

            // 检查 VRM 库
            if (typeof THREE_VRM !== 'undefined') {
                addStatus('✅ THREE_VRM 已加载', 'success');
                addStatus('THREE_VRM 属性: ' + Object.keys(THREE_VRM).join(', '), 'info');
            } else {
                addStatus('❌ THREE_VRM 未加载', 'error');
            }

            if (typeof VRM !== 'undefined') {
                addStatus('✅ VRM 已加载', 'success');
                addStatus('VRM 属性: ' + Object.keys(VRM).join(', '), 'info');
            } else {
                addStatus('❌ VRM 未加载', 'error');
            }

            // 尝试创建 VRM 加载器
            try {
                if (typeof THREE_VRM !== 'undefined' && !window.VRM) {
                    window.VRM = THREE_VRM;
                    addStatus('✅ 已将 THREE_VRM 挂载为全局 VRM', 'success');
                }

                if (window.VRM && window.VRM.VRMLoaderPlugin) {
                    const gltfLoader = new THREE.GLTFLoader();
                    gltfLoader.register((parser) => new VRM.VRMLoaderPlugin(parser));
                    addStatus('✅ VRM 1.0 加载器创建成功', 'success');
                } else {
                    addStatus('⚠️ VRM.VRMLoaderPlugin 不可用', 'error');
                }
            } catch (error) {
                addStatus('❌ VRM 加载器创建失败: ' + error.message, 'error');
            }

            addStatus('检查完成！', 'info');
        });
    </script>
</body>
</html>
