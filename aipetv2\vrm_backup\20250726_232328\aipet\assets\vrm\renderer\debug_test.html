<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VRM Debug Test</title>
    <style>
        body {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            font-family: 'Microsoft YaHei', sans-serif;
            padding: 20px;
            margin: 0;
        }
        
        .test-section {
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
        }
        
        .status {
            padding: 5px 10px;
            margin: 5px 0;
            border-radius: 4px;
        }
        
        .success { background: #27ae60; }
        .error { background: #e74c3c; }
        .warning { background: #f39c12; }
        .info { background: #3498db; }
        
        #console-output {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔍 VRM渲染器调试测试</h1>
    
    <div class="test-section">
        <h2>📋 基础环境检查</h2>
        <div id="basic-checks"></div>
    </div>
    
    <div class="test-section">
        <h2>📚 JavaScript库加载状态</h2>
        <div id="library-status"></div>
    </div>
    
    <div class="test-section">
        <h2>🎨 WebGL支持检查</h2>
        <div id="webgl-status"></div>
    </div>
    
    <div class="test-section">
        <h2>🌉 WebChannel通信测试</h2>
        <div id="webchannel-status"></div>
    </div>
    
    <div class="test-section">
        <h2>📝 控制台输出</h2>
        <div id="console-output"></div>
    </div>

    <!-- JavaScript库文件 -->
    <script>
        // 控制台输出重定向
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = `[${timestamp}] ${type.toUpperCase()}: ${args.join(' ')}\n`;
            consoleOutput.textContent += message;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = (...args) => {
            originalLog.apply(console, args);
            addToConsole('log', ...args);
        };
        
        console.error = (...args) => {
            originalError.apply(console, args);
            addToConsole('error', ...args);
        };
        
        console.warn = (...args) => {
            originalWarn.apply(console, args);
            addToConsole('warn', ...args);
        };
        
        // 基础环境检查
        function checkBasicEnvironment() {
            const checks = document.getElementById('basic-checks');
            const results = [];
            
            // 检查浏览器
            results.push({
                name: '浏览器类型',
                status: navigator.userAgent.includes('Chrome') ? 'success' : 'warning',
                message: navigator.userAgent
            });
            
            // 检查本地文件访问
            results.push({
                name: '协议类型',
                status: location.protocol === 'file:' ? 'info' : 'success',
                message: location.protocol
            });
            
            // 检查页面URL
            results.push({
                name: '页面URL',
                status: 'info',
                message: location.href
            });
            
            results.forEach(result => {
                const div = document.createElement('div');
                div.className = `status ${result.status}`;
                div.textContent = `${result.name}: ${result.message}`;
                checks.appendChild(div);
            });
            
            console.log('✅ 基础环境检查完成');
        }
        
        // 检查JavaScript库加载
        function checkLibraries() {
            const status = document.getElementById('library-status');
            const libraries = [
                { name: 'Three.js', check: () => typeof THREE !== 'undefined', path: '../libs/three.min.js' },
                { name: 'VRM 1.0', check: () => typeof THREE_VRM !== 'undefined', path: '../libs/three-vrm.min.js' },
                { name: 'VRM 0.x', check: () => typeof VRMUtils !== 'undefined', path: '../libs/three-vrm-v0.min.js' },
                { name: 'QWebChannel', check: () => typeof QWebChannel !== 'undefined', path: 'qrc:///qtwebchannel/qwebchannel.js' }
            ];

            libraries.forEach(lib => {
                const div = document.createElement('div');
                const isLoaded = lib.check();
                div.className = `status ${isLoaded ? 'success' : 'error'}`;
                div.textContent = `${lib.name}: ${isLoaded ? '✅ 已加载' : '❌ 未加载'} (${lib.path})`;
                status.appendChild(div);

                console.log(`${lib.name}: ${isLoaded ? '已加载' : '未加载'}`);
            });
        }
        
        // 检查WebGL支持
        function checkWebGL() {
            const status = document.getElementById('webgl-status');
            
            try {
                const canvas = document.createElement('canvas');
                const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                
                if (gl) {
                    const renderer = gl.getParameter(gl.RENDERER);
                    const vendor = gl.getParameter(gl.VENDOR);
                    const version = gl.getParameter(gl.VERSION);
                    
                    const div1 = document.createElement('div');
                    div1.className = 'status success';
                    div1.textContent = `✅ WebGL支持: ${version}`;
                    status.appendChild(div1);
                    
                    const div2 = document.createElement('div');
                    div2.className = 'status info';
                    div2.textContent = `GPU: ${renderer} (${vendor})`;
                    status.appendChild(div2);
                    
                    console.log(`✅ WebGL支持: ${version}, GPU: ${renderer}`);
                } else {
                    throw new Error('WebGL不支持');
                }
            } catch (error) {
                const div = document.createElement('div');
                div.className = 'status error';
                div.textContent = `❌ WebGL错误: ${error.message}`;
                status.appendChild(div);
                
                console.error(`❌ WebGL错误: ${error.message}`);
            }
        }
        
        // 检查WebChannel
        function checkWebChannel() {
            const status = document.getElementById('webchannel-status');
            
            if (typeof qt !== 'undefined' && qt.webChannelTransport) {
                const div = document.createElement('div');
                div.className = 'status success';
                div.textContent = '✅ WebChannel可用';
                status.appendChild(div);
                
                console.log('✅ WebChannel可用');
                
                // 尝试连接
                try {
                    new QWebChannel(qt.webChannelTransport, function(channel) {
                        const div2 = document.createElement('div');
                        div2.className = 'status success';
                        div2.textContent = '✅ WebChannel连接成功';
                        status.appendChild(div2);
                        
                        console.log('✅ WebChannel连接成功');
                        
                        if (channel.objects.vrmBridge) {
                            const div3 = document.createElement('div');
                            div3.className = 'status success';
                            div3.textContent = '✅ VRM桥接对象可用';
                            status.appendChild(div3);
                            
                            console.log('✅ VRM桥接对象可用');
                        } else {
                            const div3 = document.createElement('div');
                            div3.className = 'status warning';
                            div3.textContent = '⚠️ VRM桥接对象不可用';
                            status.appendChild(div3);
                            
                            console.warn('⚠️ VRM桥接对象不可用');
                        }
                    });
                } catch (error) {
                    const div2 = document.createElement('div');
                    div2.className = 'status error';
                    div2.textContent = `❌ WebChannel连接失败: ${error.message}`;
                    status.appendChild(div2);
                    
                    console.error(`❌ WebChannel连接失败: ${error.message}`);
                }
            } else {
                const div = document.createElement('div');
                div.className = 'status info';
                div.textContent = 'ℹ️ WebChannel不可用（独立模式）';
                status.appendChild(div);
                
                console.log('ℹ️ WebChannel不可用（独立模式）');
            }
        }
        
        // 页面加载完成后执行检查
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 开始VRM调试测试...');
            
            checkBasicEnvironment();
            
            // 延迟检查库文件，给它们时间加载
            setTimeout(() => {
                checkLibraries();
                checkWebGL();
                checkWebChannel();
                
                console.log('🎉 调试测试完成');
            }, 1000);
        });
        
        // 全局错误处理
        window.addEventListener('error', function(event) {
            console.error('全局错误:', event.error?.message || event.message);
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            console.error('未处理的Promise拒绝:', event.reason);
        });
    </script>
    
    <!-- 尝试加载JavaScript库 -->
    <script src="../libs/three.min.js"
            onload="console.log('✅ Three.js加载成功')"
            onerror="console.error('❌ Three.js加载失败')"></script>
    <script src="../libs/three-vrm.min.js"
            onload="console.log('✅ VRM 1.0库加载成功')"
            onerror="console.error('❌ VRM 1.0库加载失败')"></script>
    <script src="../libs/three-vrm-v0.min.js"
            onload="console.log('✅ VRM 0.x库加载成功')"
            onerror="console.error('❌ VRM 0.x库加载失败')"></script>
    <script src="qrc:///qtwebchannel/qwebchannel.js"
            onload="console.log('✅ QWebChannel加载成功')"
            onerror="console.error('❌ QWebChannel加载失败')"></script>

    <script>
        // 检查库文件是否真正可用
        setTimeout(() => {
            console.log('=== 库文件可用性检查 ===');
            console.log('THREE:', typeof THREE);
            console.log('THREE_VRM:', typeof THREE_VRM);
            console.log('VRM:', typeof VRM);
            console.log('VRMUtils:', typeof VRMUtils);
            console.log('QWebChannel:', typeof QWebChannel);

            if (typeof THREE !== 'undefined') {
                console.log('THREE.REVISION:', THREE.REVISION);
                console.log('THREE.WebGLRenderer:', typeof THREE.WebGLRenderer);
                console.log('THREE.Scene:', typeof THREE.Scene);
                console.log('THREE.PerspectiveCamera:', typeof THREE.PerspectiveCamera);
            }

            if (typeof THREE_VRM !== 'undefined') {
                console.log('THREE_VRM对象属性:', Object.keys(THREE_VRM));
                console.log('THREE_VRM.VRMLoaderPlugin:', typeof THREE_VRM.VRMLoaderPlugin);

                // 尝试将VRM对象挂载到全局
                if (typeof VRM === 'undefined') {
                    window.VRM = THREE_VRM;
                    console.log('✅ 已将THREE_VRM挂载为全局VRM对象');
                }
            }

            if (typeof VRM !== 'undefined') {
                console.log('VRM.VRMLoaderPlugin:', typeof VRM.VRMLoaderPlugin);
                console.log('VRM对象属性:', Object.keys(VRM));
            }

            if (typeof VRMUtils !== 'undefined') {
                console.log('VRMUtils对象属性:', Object.keys(VRMUtils));
            }
        }, 2000);
    </script>
</body>
</html>
