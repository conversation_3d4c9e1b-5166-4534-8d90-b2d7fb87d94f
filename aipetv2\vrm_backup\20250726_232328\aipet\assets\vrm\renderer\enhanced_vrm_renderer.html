<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced VRM Renderer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            background: transparent;
        }

        body {
            background: transparent;
            overflow: hidden;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        #vrm-canvas {
            display: block;
            width: 100vw;
            height: 100vh;
            background: transparent;
        }
        
        #loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 16px;
            z-index: 1000;
            transition: opacity 0.5s ease;
        }
        
        #loading-overlay.hidden {
            opacity: 0;
            pointer-events: none;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        #debug-info {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            font-family: monospace;
            display: none;
            z-index: 999;
        }
        
        #error-display {
            position: fixed;
            bottom: 10px;
            left: 10px;
            right: 10px;
            background: rgba(255, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 5px;
            font-size: 14px;
            display: none;
            z-index: 1001;
        }

        #quality-controls {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Microsoft YaHei', sans-serif;
            font-size: 14px;
            z-index: 1002;
            min-width: 200px;
        }

        #quality-controls h3 {
            margin: 0 0 10px 0;
            font-size: 16px;
            color: #4CAF50;
        }

        #quality-controls select {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: none;
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 14px;
        }

        #quality-controls button {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: none;
            border-radius: 4px;
            background: #4CAF50;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }

        #quality-controls button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <!-- 加载覆盖层 -->
    <div id="loading-overlay">
        <div class="loading-spinner"></div>
        <div id="loading-text">正在初始化VRM渲染器...</div>
    </div>
    
    <!-- 主渲染画布 -->
    <canvas id="vrm-canvas"></canvas>
    
    <!-- 调试信息面板 -->
    <div id="debug-info">
        <div>FPS: <span id="fps-counter">0</span></div>
        <div>模型: <span id="model-info">未加载</span></div>
        <div>状态: <span id="status-info">初始化中</span></div>
    </div>

    <!-- 质量控制面板 -->
    <div id="quality-controls" style="display: block;">
        <h3>🎨 渲染质量</h3>
        <select id="quality-preset">
            <option value="performance" selected>🚀 极致性能 (120+ FPS)</option>
            <option value="low">低质量 (60 FPS)</option>
            <option value="medium">中等质量 (平衡)</option>
            <option value="high">高质量 (推荐)</option>
            <option value="ultra">超高质量 (高端设备)</option>
        </select>
        <button onclick="applyQualityPreset()">应用设置</button>
        <button onclick="toggleDebugInfo()">显示/隐藏调试</button>
        <button onclick="toggleQualityPanel()">隐藏面板</button>
        <button onclick="testJavaScript()" style="background: #ff6b6b;">🧪 测试JS</button>
        <button onclick="testMouseEvents()" style="background: #4ecdc4;">🖱️ 测试鼠标</button>
        <button onclick="fixMouseEvents()" style="background: #f39c12;">🔧 修复鼠标</button>
        <div id="mouse-test-area" style="
            position: absolute;
            top: 50%;
            left: 50%;
            width: 200px;
            height: 100px;
            background: rgba(255,255,255,0.3);
            border: 2px solid #fff;
            transform: translate(-50%, -50%);
            cursor: pointer;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        " onmousedown="handleTestMouseDown()" onmouseup="handleTestMouseUp()" onclick="handleTestClick()">
            点击测试鼠标事件
        </div>
    </div>
    
    <!-- 错误显示面板 -->
    <div id="error-display">
        <strong>错误:</strong> <span id="error-message"></span>
    </div>

    <!-- JavaScript库文件 -->
    <script src="../libs/three.min.js"></script>
    <!-- 使用兼容的GLTFLoader -->
    <script src="https://cdn.jsdelivr.net/npm/three@0.147.0/examples/js/loaders/GLTFLoader.js"></script>
    <script src="../libs/three-vrm.min.js"></script>
    <script src="../libs/three-vrm-v0.min.js"></script>

    <!-- Qt WebChannel支持 -->
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>

    <!-- VRM渲染器核心脚本 -->
    <script src="enhanced_vrm_renderer.js" onload="console.log('✅ enhanced_vrm_renderer.js 已加载')"></script>
    <script src="universal_vrm_loader.js" onload="console.log('✅ universal_vrm_loader.js 已加载')"></script>
    <script src="layered_mouse_tracking.js" onload="console.log('✅ layered_mouse_tracking.js 已加载')"></script>
    
    <script>
        // 立即执行的测试
        console.log('🧪 JavaScript执行测试 - 脚本开始执行');

        // 通过WebChannel通知Python端JavaScript正在工作
        setTimeout(() => {
            if (window.vrmBridge && typeof window.vrmBridge.on_debug_message === 'function') {
                window.vrmBridge.on_debug_message('🧪 JavaScript正在工作！');
            }
        }, 100);

        // 添加全局鼠标事件监听器来调试事件传递
        document.addEventListener('mousedown', function(event) {
            console.log('🌍 全局鼠标按下事件:', event.target.tagName, event.clientX, event.clientY);
        });

        document.addEventListener('mousemove', function(event) {
            // 只在拖动时输出，避免刷屏
            if (window.vrmRenderer && window.vrmRenderer.isDragging) {
                console.log('🌍 全局鼠标移动事件:', event.clientX, event.clientY);
            }
        });

        document.addEventListener('mouseup', function(event) {
            console.log('🌍 全局鼠标释放事件:', event.target.tagName, event.clientX, event.clientY);
        });

        // 全局错误处理
        window.addEventListener('error', function(event) {
            console.error('全局错误:', event.error);
            showError('JavaScript错误: ' + event.error.message);
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            console.error('未处理的Promise拒绝:', event.reason);
            showError('异步错误: ' + event.reason);
        });
        
        // 错误显示函数
        function showError(message) {
            const errorDisplay = document.getElementById('error-display');
            const errorMessage = document.getElementById('error-message');
            errorMessage.textContent = message;
            errorDisplay.style.display = 'block';
            
            // 5秒后自动隐藏
            setTimeout(() => {
                errorDisplay.style.display = 'none';
            }, 5000);
        }
        
        // 加载状态更新
        function updateLoadingStatus(text) {
            const loadingText = document.getElementById('loading-text');
            if (loadingText) {
                loadingText.textContent = text;
            }
        }
        
        // 隐藏加载覆盖层
        function hideLoadingOverlay() {
            const overlay = document.getElementById('loading-overlay');
            overlay.classList.add('hidden');
            setTimeout(() => {
                overlay.style.display = 'none';
            }, 500);
        }
        
        // 显示调试信息
        function showDebugInfo() {
            document.getElementById('debug-info').style.display = 'block';
        }
        
        // 更新调试信息
        function updateDebugInfo(fps, modelInfo, statusInfo) {
            const fpsElement = document.getElementById('fps-counter');
            const modelElement = document.getElementById('model-info');
            const statusElement = document.getElementById('status-info');

            if (fpsElement) fpsElement.textContent = fps;
            if (modelElement) modelElement.textContent = modelInfo;
            if (statusElement) statusElement.textContent = statusInfo;
        }

        // 质量控制函数
        function applyQualityPreset() {
            const preset = document.getElementById('quality-preset').value;
            if (window.vrmRenderer) {
                window.vrmRenderer.setQualityPreset(preset);
                console.log(`🎨 应用质量预设: ${preset}`);
            }
        }

        function toggleDebugInfo() {
            const debugInfo = document.getElementById('debug-info');
            debugInfo.style.display = debugInfo.style.display === 'none' ? 'block' : 'none';
        }

        function toggleQualityPanel() {
            const panel = document.getElementById('quality-controls');
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        }

        // 测试JavaScript是否工作
        function testJavaScript() {
            let status = '🎉 JavaScript正在工作！\n\n';

            // 检查VRM渲染器状态
            if (window.vrmRenderer) {
                status += '✅ VRM渲染器存在\n';
                status += `✅ 初始化状态: ${window.vrmRenderer.isInitialized}\n`;
                status += `✅ VRM模型: ${window.vrmRenderer.vrm ? '已加载' : '未加载'}\n`;
                status += `✅ Canvas: ${window.vrmRenderer.canvas ? '存在' : '不存在'}\n`;

                if (window.vrmRenderer.canvas) {
                    status += `✅ 鼠标跟踪: ${window.vrmRenderer.isMouseTrackingEnabled}\n`;
                    status += `✅ 拖动状态: ${window.vrmRenderer.isDragging}\n`;
                }
            } else {
                status += '❌ VRM渲染器不存在\n';
            }

            // 检查THREE.js
            status += `✅ THREE.js: ${typeof THREE !== 'undefined' ? '已加载' : '未加载'}\n`;

            alert(status);
            console.log('🧪 渲染器状态检查:', status);

            // 通过WebChannel通知Python
            if (window.vrmBridge && typeof window.vrmBridge.on_debug_message === 'function') {
                window.vrmBridge.on_debug_message('🧪 渲染器状态: ' + (window.vrmRenderer ? '存在' : '不存在'));
            }
        }

        // 测试鼠标事件
        function testMouseEvents() {
            if (!window.vrmRenderer || !window.vrmRenderer.canvas) {
                alert('❌ VRM渲染器或Canvas不存在');
                return;
            }

            const canvas = window.vrmRenderer.canvas;
            let status = '🖱️ 鼠标事件测试:\n\n';

            // 模拟鼠标按下事件
            console.log('🧪 模拟鼠标按下事件...');
            const mouseDownEvent = new MouseEvent('mousedown', {
                clientX: 200,
                clientY: 200,
                bubbles: true,
                cancelable: true,
                button: 0
            });
            canvas.dispatchEvent(mouseDownEvent);

            // 检查拖动状态是否改变
            setTimeout(() => {
                status += `拖动状态: ${window.vrmRenderer.isDragging}\n`;

                if (window.vrmRenderer.isDragging) {
                    status += '✅ 鼠标按下事件工作正常\n';

                    // 模拟鼠标移动
                    const mouseMoveEvent = new MouseEvent('mousemove', {
                        clientX: 250,
                        clientY: 250,
                        bubbles: true,
                        cancelable: true
                    });
                    canvas.dispatchEvent(mouseMoveEvent);

                    // 模拟鼠标释放
                    const mouseUpEvent = new MouseEvent('mouseup', {
                        clientX: 250,
                        clientY: 250,
                        bubbles: true,
                        cancelable: true,
                        button: 0
                    });
                    canvas.dispatchEvent(mouseUpEvent);

                    setTimeout(() => {
                        status += `释放后拖动状态: ${window.vrmRenderer.isDragging}\n`;
                        status += '✅ 完整的拖动事件序列已测试';
                        alert(status);
                    }, 100);
                } else {
                    status += '❌ 鼠标按下事件未生效';
                    alert(status);
                }
            }, 100);
        }

        // 测试区域的事件处理函数
        function handleTestMouseDown() {
            alert('🎯 鼠标按下！');
            console.log('🎯 测试区域鼠标按下');
            if (window.vrmBridge && typeof window.vrmBridge.on_debug_message === 'function') {
                window.vrmBridge.on_debug_message('🎯 测试区域鼠标按下');
            }
        }

        function handleTestMouseUp() {
            alert('🎯 鼠标释放！');
            console.log('🎯 测试区域鼠标释放');
            if (window.vrmBridge && typeof window.vrmBridge.on_debug_message === 'function') {
                window.vrmBridge.on_debug_message('🎯 测试区域鼠标释放');
            }
        }

        function handleTestClick() {
            alert('🎯 点击事件！');
            console.log('🎯 测试区域点击');
            if (window.vrmBridge && typeof window.vrmBridge.on_debug_message === 'function') {
                window.vrmBridge.on_debug_message('🎯 测试区域点击');
            }
        }

        // 修复鼠标事件函数
        function fixMouseEvents() {
            if (!window.vrmRenderer) {
                alert('❌ VRM渲染器不存在');
                return;
            }

            if (!window.vrmRenderer.canvas) {
                alert('❌ Canvas不存在');
                return;
            }

            const canvas = window.vrmRenderer.canvas;

            // 强制重新初始化鼠标事件
            try {
                // 移除旧的事件监听器
                canvas.removeEventListener('mousedown', window.vrmRenderer._handleDown);
                canvas.removeEventListener('mousemove', window.vrmRenderer._handleMove);
                canvas.removeEventListener('mouseup', window.vrmRenderer._handleUp);

                // 创建新的事件处理函数
                window.vrmRenderer._handleDown = function(event) {
                    alert('🖱️ Canvas鼠标按下！');
                    window.vrmRenderer.handleMouseDown(event);
                };

                window.vrmRenderer._handleMove = function(event) {
                    window.vrmRenderer.handleMouseMove(event);
                };

                window.vrmRenderer._handleUp = function(event) {
                    alert('🖱️ Canvas鼠标释放！');
                    window.vrmRenderer.handleMouseUp(event);
                };

                // 重新添加事件监听器
                canvas.addEventListener('mousedown', window.vrmRenderer._handleDown);
                canvas.addEventListener('mousemove', window.vrmRenderer._handleMove);
                canvas.addEventListener('mouseup', window.vrmRenderer._handleUp);

                alert('✅ 鼠标事件已重新绑定到Canvas');

            } catch (error) {
                alert('❌ 修复失败: ' + error.message);
            }
        }

        // 简化的WebChannel通信初始化
        function initWebChannel() {
            console.log('🌉 检查VRM桥接对象...');

            // 等待Python端创建的简化桥接对象
            if (window.vrmBridge) {
                console.log('✅ VRM桥接对象已存在');

                // 通知Python端渲染器已准备就绪
                if (window.vrmRenderer && window.vrmRenderer.isInitialized) {
                    window.vrmBridge.on_renderer_ready();
                }
            } else {
                console.log('⚠️ VRM桥接对象不存在，稍后重试...');
                // 延迟重试，等待Python端创建桥接对象
                setTimeout(initWebChannel, 100);
            }
        }

        // 等待所有脚本加载完成
        function waitForScripts() {
            return new Promise((resolve) => {
                const checkScripts = () => {
                    console.log('🔍 检查脚本加载状态:');
                    console.log('  - THREE:', typeof THREE);
                    console.log('  - THREE.GLTFLoader:', typeof THREE?.GLTFLoader);
                    console.log('  - VRMRenderer:', typeof VRMRenderer);

                    if (typeof THREE !== 'undefined' &&
                        typeof THREE.GLTFLoader !== 'undefined' &&
                        typeof VRMRenderer !== 'undefined') {
                        console.log('✅ 所有脚本已加载完成');
                        resolve();
                    } else {
                        console.log('⏳ 等待脚本加载...');
                        setTimeout(checkScripts, 100);
                    }
                };
                checkScripts();
            });
        }

        // 页面加载完成后初始化
        console.log('🧪 注册DOMContentLoaded事件监听器');
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 VRM渲染器页面加载完成 - DOMContentLoaded事件触发');

            // 通过WebChannel通知Python端DOM已加载
            if (window.vrmBridge && typeof window.vrmBridge.on_debug_message === 'function') {
                window.vrmBridge.on_debug_message('🚀 DOM已加载完成');
            }

            updateLoadingStatus('正在检查依赖库...');

            // 检查Three.js是否加载成功
            console.log('🔍 检查THREE:', typeof THREE);
            if (typeof THREE === 'undefined') {
                showError('Three.js库加载失败');
                return;
            }

            // 检查GLTFLoader是否可用
            console.log('🔍 检查GLTFLoader:', typeof THREE.GLTFLoader);
            if (typeof THREE.GLTFLoader === 'undefined') {
                showError('GLTFLoader加载失败');
                return;
            }

            updateLoadingStatus('正在加载VRM库...');

            // 检查VRM库是否加载成功
            console.log('🔍 检查THREE_VRM:', typeof THREE_VRM);
            console.log('🔍 检查VRM:', typeof VRM);

            if (typeof THREE_VRM === 'undefined' && typeof VRM === 'undefined') {
                showError('VRM库加载失败');
                return;
            }

            // 将THREE_VRM挂载为全局VRM对象（兼容性）
            if (typeof THREE_VRM !== 'undefined' && typeof VRM === 'undefined') {
                window.VRM = THREE_VRM;
                console.log('✅ 已将THREE_VRM挂载为全局VRM对象');
            }

            updateLoadingStatus('正在初始化渲染器...');

            // 等待所有脚本加载完成后初始化
            waitForScripts().then(() => {
                try {
                    console.log('🔍 检查VRMRenderer类:', typeof VRMRenderer);
                    console.log('🚀 开始创建VRMRenderer实例...');

                    window.vrmRenderer = new VRMRenderer();
                    console.log('✅ VRMRenderer实例已创建');

                    window.vrmRenderer.init().then(() => {
                        console.log('✅ VRMRenderer初始化完成');

                        // 自动应用极致性能预设以达到120+ FPS
                        window.vrmRenderer.setQualityPreset('performance');
                        console.log('🚀 已应用极致性能预设，目标120+ FPS');

                        updateLoadingStatus('渲染器初始化完成');
                        hideLoadingOverlay();
                        showDebugInfo();

                        // 初始化WebChannel通信
                        initWebChannel();

                    }).catch(error => {
                        console.error('❌ 渲染器初始化失败:', error);
                        showError('渲染器初始化失败: ' + error.message);
                    });

                } catch (error) {
                    console.error('❌ 渲染器创建失败:', error);
                    showError('渲染器创建失败: ' + error.message);
                }
            }).catch(error => {
                console.error('❌ 脚本加载超时:', error);
                showError('脚本加载超时');
            });
        });
        
        // 窗口大小改变时调整画布
        window.addEventListener('resize', function() {
            if (window.vrmRenderer && window.vrmRenderer.handleResize) {
                window.vrmRenderer.handleResize();
            }
        });

        // 键盘快捷键
        document.addEventListener('keydown', function(event) {
            // Ctrl+Q 切换质量面板
            if (event.ctrlKey && event.key === 'q') {
                event.preventDefault();
                toggleQualityPanel();
            }
            // Ctrl+D 切换调试信息
            if (event.ctrlKey && event.key === 'd') {
                event.preventDefault();
                toggleDebugInfo();
            }
            // 数字键0-4快速切换质量
            if (event.key >= '0' && event.key <= '4') {
                const presets = ['performance', 'low', 'medium', 'high', 'ultra'];
                const preset = presets[parseInt(event.key)];
                document.getElementById('quality-preset').value = preset;
                applyQualityPreset();
                event.preventDefault();
            }
        });
    </script>
</body>
</html>
