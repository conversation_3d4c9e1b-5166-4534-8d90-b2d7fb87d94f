/**
 * Enhanced VRM Renderer
 * 增强版VRM渲染器 - 支持VRM 1.0和0.x双版本
 */

class VRMRenderer {
    constructor() {
        // 渲染器核心组件
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.canvas = null;
        
        // VRM相关
        this.vrm = null;
        this.vrmLoader = null;
        this.currentModel = null;
        
        // 动画和控制
        this.clock = new THREE.Clock();
        this.mixer = null;
        this.isAnimating = false;
        
        // 鼠标跟踪
        this.mouseTracker = null;
        this.isMouseTrackingEnabled = true;

        // 拖动状态
        this.isDragging = false;
        this.dragStartX = 0;
        this.dragStartY = 0;
        this.lastMouseX = 0;
        this.lastMouseY = 0;
        this.modelRotationX = 0;
        this.modelRotationY = 0;
        
        // 性能监控
        this.frameCount = 0;
        this.lastTime = performance.now();
        this.fps = 0;
        
        // 状态管理
        this.isInitialized = false;
        this.isModelLoaded = false;
        this.currentExpression = 'neutral';
        
        // 配置选项 - 优化性能和质量（默认中等质量）
        this.config = {
            // 渲染质量设置
            enableShadows: false, // 默认关闭阴影以提升性能
            enableAntialiasing: true,
            pixelRatio: 1, // 使用标准像素比例
            shadowMapSize: 512, // 较小的阴影贴图

            // 背景设置
            backgroundColor: 0x000000,
            backgroundAlpha: 0.0, // 完全透明

            // 摄像机设置
            cameraFov: 45,  // 增加视角以显示更多内容
            cameraNear: 0.1,
            cameraFar: 20.0,
            cameraPosition: { x: 0, y: 0.8, z: 2.5 },  // 降低高度，增加距离以显示全身

            // 光照设置
            lightIntensity: 1.2, // 稍微增加光照强度
            ambientLightIntensity: 0.4, // 环境光强度

            // 模型设置
            modelScale: 1.0,
            modelPosition: { x: 0, y: 0, z: 0 },

            // 性能设置
            targetFPS: 60,
            enablePerformanceMonitor: true
        };
        
        console.log('🎭 VRMRenderer 实例已创建');
    }
    
    /**
     * 初始化渲染器
     */
    async init() {
        try {
            console.log('🚀 开始初始化VRM渲染器...');

            // 获取画布元素
            this.canvas = document.getElementById('vrm-canvas');
            if (!this.canvas) {
                throw new Error('找不到画布元素 #vrm-canvas');
            }

            // 设置画布样式，使其可以接收鼠标事件
            this.canvas.style.cursor = 'pointer';
            this.canvas.style.userSelect = 'none'; // 防止文本选择

            // 直接设置内联事件处理器（更可靠的方法）
            const self = this;
            this.canvas.onmousedown = function(event) {
                alert('🖱️ 内联鼠标按下事件！');
                self.handleMouseDown(event);
            };
            this.canvas.onmousemove = function(event) {
                self.handleMouseMove(event);
            };
            this.canvas.onmouseup = function(event) {
                alert('🖱️ 内联鼠标释放事件！');
                self.handleMouseUp(event);
            };
            this.canvas.onclick = function(event) {
                alert('🖱️ 内联点击事件！');
                self.handleMouseClick(event);
            };

            console.log('✅ Canvas内联事件处理器已设置');

            // 初始化Three.js场景
            console.log('🔧 初始化Three.js场景...');
            this.initScene();
            this.initCamera();
            this.initRenderer();
            this.initLights();
            console.log('✅ Three.js场景初始化完成');

            // 初始化VRM加载器（关键步骤）
            console.log('🔧 初始化VRM加载器...');
            try {
                this.initVRMLoader();
                console.log('✅ VRM加载器初始化成功');
            } catch (loaderError) {
                console.error('❌ VRM加载器初始化失败:', loaderError);
                // 不抛出错误，允许渲染器继续运行，但标记加载器不可用
                this.vrmLoader = null;
            }

            // 初始化鼠标跟踪
            console.log('🔧 准备初始化鼠标跟踪...');
            this.initMouseTracking();

            // 开始渲染循环
            this.startRenderLoop();

            // 不自动加载默认模型，让用户手动选择
            // await this.loadDefaultModel();

            this.isInitialized = true;
            console.log('✅ VRM渲染器初始化完成');

            // 通知Python端渲染器已准备就绪
            this.notifyRendererReady();

            return true;

        } catch (error) {
            console.error('❌ VRM渲染器初始化失败:', error);
            throw error;
        }
    }
    
    /**
     * 初始化Three.js场景
     */
    initScene() {
        this.scene = new THREE.Scene();
        this.scene.background = null; // 透明背景
        console.log('📦 场景初始化完成');
    }
    
    /**
     * 初始化摄像机
     */
    initCamera() {
        const aspect = window.innerWidth / window.innerHeight;
        this.camera = new THREE.PerspectiveCamera(
            this.config.cameraFov,
            aspect,
            this.config.cameraNear,
            this.config.cameraFar
        );
        
        this.camera.position.set(
            this.config.cameraPosition.x,
            this.config.cameraPosition.y,
            this.config.cameraPosition.z
        );
        
        this.camera.lookAt(0, 0.8, 0); // 看向角色身体中心位置
        console.log('📷 摄像机初始化完成');
    }
    
    /**
     * 初始化渲染器
     */
    initRenderer() {
        this.renderer = new THREE.WebGLRenderer({
            canvas: this.canvas,
            alpha: true, // 启用透明度
            antialias: this.config.enableAntialiasing,
            preserveDrawingBuffer: true,
            powerPreference: "high-performance", // 优先使用高性能GPU
            stencil: false, // 禁用模板缓冲以提升性能
            depth: true
        });

        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(this.config.pixelRatio);
        this.renderer.setClearColor(this.config.backgroundColor, this.config.backgroundAlpha);

        // 启用阴影（优化设置）
        if (this.config.enableShadows) {
            this.renderer.shadowMap.enabled = true;
            this.renderer.shadowMap.type = THREE.PCFSoftShadowMap; // 软阴影，质量更好
            this.renderer.shadowMap.autoUpdate = true;
        }

        // 优化的色彩管理
        this.renderer.outputColorSpace = THREE.SRGBColorSpace;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.2; // 稍微增加曝光

        // 性能优化设置
        this.renderer.info.autoReset = false; // 手动重置渲染信息
        this.renderer.sortObjects = true; // 启用对象排序优化

        console.log('🎨 高性能渲染器初始化完成');
        console.log(`📊 像素比例: ${this.config.pixelRatio}, 阴影: ${this.config.enableShadows ? '启用' : '禁用'}`);
    }
    
    /**
     * 初始化光照
     */
    initLights() {
        // 主光源 - 方向光（简化版本以提升性能）
        const directionalLight = new THREE.DirectionalLight(0xffffff, this.config.lightIntensity);
        directionalLight.position.set(1, 2, 1);
        directionalLight.castShadow = this.config.enableShadows;

        if (this.config.enableShadows) {
            // 简化阴影设置
            directionalLight.shadow.mapSize.width = this.config.shadowMapSize;
            directionalLight.shadow.mapSize.height = this.config.shadowMapSize;
            directionalLight.shadow.camera.near = 0.1;
            directionalLight.shadow.camera.far = 5;
            directionalLight.shadow.camera.left = -2;
            directionalLight.shadow.camera.right = 2;
            directionalLight.shadow.camera.top = 2;
            directionalLight.shadow.camera.bottom = -2;
        }

        this.scene.add(directionalLight);

        // 环境光 - 使用配置中的强度
        const ambientLight = new THREE.AmbientLight(0xffffff, this.config.ambientLightIntensity);
        this.scene.add(ambientLight);
        
        console.log('💡 光照系统初始化完成');
    }
    
    /**
     * 初始化VRM加载器
     */
    initVRMLoader() {
        try {
            // 检查THREE.GLTFLoader是否可用
            if (typeof THREE === 'undefined' || !THREE.GLTFLoader) {
                throw new Error('THREE.GLTFLoader不可用');
            }

            // 创建GLTF加载器
            this.vrmLoader = new THREE.GLTFLoader();
            console.log('✅ GLTFLoader创建成功');

            // 检查VRM库版本并配置加载器
            console.log('🔍 检查VRM库状态...');
            console.log('- typeof THREE_VRM:', typeof THREE_VRM);
            console.log('- typeof VRM:', typeof VRM);

            if (typeof THREE_VRM !== 'undefined') {
                console.log('📊 THREE_VRM对象属性:', Object.keys(THREE_VRM));

                // 将THREE_VRM挂载为全局VRM对象
                if (typeof VRM === 'undefined') {
                    window.VRM = THREE_VRM;
                    console.log('✅ 已将THREE_VRM挂载为全局VRM对象');
                }
            }

            if (typeof VRM !== 'undefined') {
                console.log('📊 VRM对象属性:', Object.keys(VRM));

                if (VRM.VRMLoaderPlugin) {
                    // VRM 1.0 API
                    this.vrmLoader.register((parser) => new VRM.VRMLoaderPlugin(parser));
                    console.log('📥 VRM 1.0加载器已配置');
                } else if (VRM.VRMImporter) {
                    // VRM 0.x API - 不需要注册插件，直接使用GLTFLoader
                    console.log('📥 VRM 0.x加载器已配置');
                } else {
                    console.warn('⚠️ 未知的VRM库版本，VRM对象属性:', Object.keys(VRM));
                }
            } else {
                console.warn('⚠️ VRM库未加载');
            }

            // 验证加载器
            if (!this.vrmLoader || typeof this.vrmLoader.load !== 'function') {
                throw new Error('VRM加载器初始化失败');
            }

            console.log('📥 VRM加载器初始化完成');

        } catch (error) {
            console.error('❌ VRM加载器初始化失败:', error);
            this.vrmLoader = null;
            throw error;
        }
    }
    
    /**
     * 初始化鼠标跟踪
     */
    initMouseTracking() {
        console.log('🖱️ 初始化鼠标事件监听...');

        // 移除旧的事件监听器（如果有）
        this.canvas.removeEventListener('click', this._handleClick);
        this.canvas.removeEventListener('mousemove', this._handleMove);
        this.canvas.removeEventListener('mousedown', this._handleDown);
        this.canvas.removeEventListener('mouseup', this._handleUp);

        // 创建绑定的事件处理函数
        this._handleClick = (event) => {
            console.log('🖱️ 点击事件触发');
            this.handleMouseClick(event);
        };

        this._handleMove = (event) => {
            this.handleMouseMove(event);
        };

        this._handleDown = (event) => {
            console.log('🖱️ 鼠标按下事件触发');
            this.handleMouseDown(event);
        };

        this._handleUp = (event) => {
            console.log('🖱️ 鼠标释放事件触发');
            this.handleMouseUp(event);
        };

        // 添加新的事件监听器
        this.canvas.addEventListener('click', this._handleClick);
        this.canvas.addEventListener('mousemove', this._handleMove);
        this.canvas.addEventListener('mousedown', this._handleDown);
        this.canvas.addEventListener('mouseup', this._handleUp);

        console.log('✅ 鼠标事件监听器已初始化');

        // 如果有LayeredMouseTracker，也使用它
        if (typeof LayeredMouseTracker !== 'undefined') {
            this.mouseTracker = new LayeredMouseTracker(this.canvas);
            this.mouseTracker.onMouseMove = (normalizedX, normalizedY) => {
                this.handleNormalizedMouseMove(normalizedX, normalizedY);
            };
            console.log('🖱️ LayeredMouseTracker已启用');
        }

        console.log('🖱️ 基本鼠标事件监听已启用');
    }
    
    /**
     * 加载默认VRM模型
     */
    async loadDefaultModel() {
        try {
            console.log('📦 开始加载默认VRM模型...');
            
            // 尝试加载可用的VRM模型
            const modelPaths = [
                '../models/Alice.vrm',    // VRM 1.0
                '../models/Zome.vrm',     // VRM 0.x
                '../models/aldina.vrm'    // VRM 0.x
            ];
            
            for (const modelPath of modelPaths) {
                try {
                    await this.loadVRMModel(modelPath);
                    console.log(`✅ 成功加载模型: ${modelPath}`);
                    return;
                } catch (error) {
                    console.warn(`⚠️ 模型加载失败: ${modelPath}`, error.message);
                    continue;
                }
            }
            
            throw new Error('所有默认模型都加载失败');
            
        } catch (error) {
            console.error('❌ 默认模型加载失败:', error);
            // 不抛出错误，允许渲染器继续运行
        }
    }
    
    /**
     * 加载VRM模型
     */
    async loadVRMModel(modelPath) {
        // 检查加载器是否可用
        if (!this.vrmLoader) {
            const error = new Error('VRM加载器未初始化');
            console.error('❌', error.message);
            throw error;
        }

        if (typeof this.vrmLoader.load !== 'function') {
            const error = new Error('VRM加载器load方法不可用');
            console.error('❌', error.message);
            throw error;
        }

        console.log('📥 开始加载VRM模型:', modelPath);

        return new Promise((resolve, reject) => {
            this.vrmLoader.load(
                modelPath,
                async (gltf) => {
                    try {
                        // 移除旧模型
                        if (this.vrm) {
                            this.scene.remove(this.vrm.scene);
                            if (this.vrm.dispose) {
                                this.vrm.dispose();
                            }
                        }

                        let vrm = null;

                        // 检查VRM版本并处理
                        if (gltf.userData && gltf.userData.vrm) {
                            // VRM 1.0 格式
                            vrm = gltf.userData.vrm;
                            console.log('📦 检测到VRM 1.0格式');
                        } else if (typeof THREE_VRM !== 'undefined') {
                            // VRM 0.x 格式，使用THREE_VRM
                            console.log('📦 检测到VRM 0.x格式，开始导入...');
                            console.log('📊 THREE_VRM对象:', Object.keys(THREE_VRM));

                            // 尝试不同的VRM 0.x API
                            if (THREE_VRM.VRM && THREE_VRM.VRM.from) {
                                console.log('🔄 使用 THREE_VRM.VRM.from API');
                                vrm = await THREE_VRM.VRM.from(gltf);
                            } else if (THREE_VRM.VRMUtils && THREE_VRM.VRMUtils.extractThumbnailBlob) {
                                console.log('🔄 使用 VRMUtils API');
                                // 这是一个更老的API，直接使用gltf
                                vrm = {
                                    scene: gltf.scene,
                                    update: (delta) => {
                                        // 基本的更新逻辑
                                        if (gltf.animations && gltf.animations.length > 0) {
                                            // 处理动画
                                        }
                                    },
                                    dispose: () => {
                                        if (gltf.scene) {
                                            gltf.scene.traverse((child) => {
                                                if (child.geometry) child.geometry.dispose();
                                                if (child.material) {
                                                    if (Array.isArray(child.material)) {
                                                        child.material.forEach(mat => mat.dispose());
                                                    } else {
                                                        child.material.dispose();
                                                    }
                                                }
                                            });
                                        }
                                    }
                                };
                            } else {
                                console.warn('⚠️ 未找到合适的VRM导入方法，作为普通GLTF处理');
                                // 作为普通GLTF模型处理
                                vrm = {
                                    scene: gltf.scene,
                                    update: () => {},
                                    dispose: () => {}
                                };
                            }
                        } else {
                            // 降级为普通GLTF
                            console.log('📦 作为普通GLTF模型加载');
                            vrm = {
                                scene: gltf.scene,
                                update: () => {},
                                dispose: () => {}
                            };
                        }

                        if (!vrm) {
                            throw new Error('VRM模型导入失败');
                        }

                        this.vrm = vrm;
                        this.scene.add(this.vrm.scene);

                        // 设置模型位置和缩放
                        const modelObject = this.vrm.scene;
                        modelObject.position.set(
                            this.config.modelPosition.x,
                            this.config.modelPosition.y,
                            this.config.modelPosition.z
                        );
                        modelObject.scale.setScalar(this.config.modelScale);

                        // 启用阴影
                        if (this.config.enableShadows) {
                            modelObject.traverse((child) => {
                                if (child.isMesh) {
                                    child.castShadow = true;
                                    child.receiveShadow = true;
                                }
                            });
                        }

                        this.currentModel = modelPath;
                        this.isModelLoaded = true;

                        console.log('✅ VRM模型加载成功:', modelPath);
                        console.log('📊 VRM对象信息:', {
                            hasScene: !!this.vrm.scene,
                            hasUpdate: typeof this.vrm.update === 'function',
                            hasBlendShapeProxy: !!this.vrm.blendShapeProxy,
                            hasLookAt: !!this.vrm.lookAt
                        });

                        resolve(this.vrm);

                    } catch (error) {
                        console.error('❌ VRM模型处理失败:', error);
                        reject(error);
                    }
                },
                (progress) => {
                    const percent = (progress.loaded / progress.total * 100).toFixed(1);
                    console.log(`📥 模型加载进度: ${percent}%`);
                },
                (error) => {
                    console.error('❌ VRM模型加载失败:', error);
                    reject(error);
                }
            );
        });
    }
    
    /**
     * 处理鼠标移动（主要方法）
     */
    handleMouseMove(event) {
        const rect = this.canvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        // 如果正在拖动，执行旋转
        if (this.isDragging && this.vrm) {
            console.log('🔄 正在拖动中...');
            const deltaX = x - this.lastMouseX;
            const deltaY = y - this.lastMouseY;

            // 更新旋转角度
            this.modelRotationY += deltaX * 0.01; // 水平旋转
            this.modelRotationX += deltaY * 0.01; // 垂直旋转

            // 限制垂直旋转角度
            this.modelRotationX = Math.max(-Math.PI / 4, Math.min(Math.PI / 4, this.modelRotationX));

            // 应用旋转到VRM模型
            if (this.vrm.scene) {
                this.vrm.scene.rotation.y = this.modelRotationY;
                this.vrm.scene.rotation.x = this.modelRotationX;
            }

            // 更新上次鼠标位置
            this.lastMouseX = x;
            this.lastMouseY = y;

            console.log(`🔄 模型旋转: X=${this.modelRotationX.toFixed(2)}, Y=${this.modelRotationY.toFixed(2)}`);
        } else if (this.isMouseTrackingEnabled) {
            // 转换为标准化坐标用于视线跟踪
            const normalizedX = (x / rect.width) * 2 - 1;
            const normalizedY = -(y / rect.height) * 2 + 1;

            // 更新鼠标位置（用于视线跟踪等）
            this.updateMousePosition(normalizedX, normalizedY);
        }
    }
    
    /**
     * 开始渲染循环（高性能版本）
     */
    startRenderLoop() {
        const animate = () => {
            requestAnimationFrame(animate);
            this.render();
        };

        animate();
        console.log(`🔄 高性能渲染循环已启动 (无帧率限制)`);
    }
    
    /**
     * 渲染一帧（高性能版本）
     */
    render() {
        // 只在需要时获取deltaTime
        let deltaTime = 0;
        if (this.vrm && this.vrm.update) {
            deltaTime = this.clock.getDelta();
            this.vrm.update(deltaTime);
        }

        // 更新动画混合器（如果存在）
        if (this.mixer) {
            if (deltaTime === 0) deltaTime = this.clock.getDelta();
            this.mixer.update(deltaTime);
        }

        // 渲染场景
        this.renderer.render(this.scene, this.camera);

        // 减少性能监控频率（每10帧更新一次）
        this.frameCount++;
        if (this.frameCount % 10 === 0) {
            this.updatePerformanceCounter();
        }
    }
    
    /**
     * 更新性能计数器（高性能版本）
     */
    updatePerformanceCounter() {
        const currentTime = performance.now();

        if (currentTime - this.lastTime >= 1000) {
            // 计算实际帧数（因为现在每10帧才调用一次这个函数）
            this.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime));

            // 只在启用性能监控时输出详细信息
            if (this.config.enablePerformanceMonitor && this.fps >= 60) {
                console.log(`🚀 高性能模式 - FPS: ${this.fps}`);
            } else if (this.fps < 60) {
                console.log(`📊 FPS: ${this.fps}`);
            }

            // 重置计数器
            this.frameCount = 0;
            this.lastTime = currentTime;

            // 更新调试信息
            if (typeof updateDebugInfo === 'function') {
                updateDebugInfo(
                    this.fps,
                    this.currentModel || '未加载',
                    this.isModelLoaded ? '已加载' : '加载中'
                );
            }
        }
    }
    
    /**
     * 处理窗口大小改变
     */
    handleResize() {
        if (!this.camera || !this.renderer) return;
        
        const width = window.innerWidth;
        const height = window.innerHeight;
        
        this.camera.aspect = width / height;
        this.camera.updateProjectionMatrix();
        
        this.renderer.setSize(width, height);
        
        console.log(`📐 画布大小已调整: ${width}x${height}`);
    }
    
    /**
     * 设置表情
     */
    setExpression(expressionName, weight = 1.0) {
        if (!this.vrm) return;

        try {
            // VRM 1.0 API
            if (this.vrm.expressionManager) {
                this.vrm.expressionManager.setValue(expressionName, weight);
                this.currentExpression = expressionName;
                console.log(`😊 表情已设置(1.0): ${expressionName} (${weight})`);
                return;
            }

            // VRM 0.x API
            if (this.vrm.blendShapeProxy) {
                this.vrm.blendShapeProxy.setValue(expressionName, weight);
                this.currentExpression = expressionName;
                console.log(`😊 表情已设置(0.x): ${expressionName} (${weight})`);
                return;
            }

            console.warn('⚠️ 未找到表情管理器');
        } catch (error) {
            console.warn('⚠️ 表情设置失败:', error.message);
        }
    }
    
    /**
     * 清理资源
     */
    dispose() {
        if (this.vrm) {
            this.scene.remove(this.vrm.scene);
            this.vrm.dispose();
        }
        
        if (this.renderer) {
            this.renderer.dispose();
        }
        
        if (this.mouseTracker) {
            this.mouseTracker.dispose();
        }
        
        console.log('🧹 VRM渲染器资源已清理');
    }

    // ==================== 鼠标事件处理 ====================

    /**
     * 处理鼠标点击事件
     */
    handleMouseClick(event) {
        try {
            const rect = this.canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;

            // 转换为标准化坐标
            const normalizedX = (x / rect.width) * 2 - 1;
            const normalizedY = -(y / rect.height) * 2 + 1;

            console.log(`🖱️ 鼠标点击: (${x}, ${y}) -> 标准化: (${normalizedX.toFixed(2)}, ${normalizedY.toFixed(2)})`);

            // 射线检测
            this.performRaycast(normalizedX, normalizedY);

            // 如果点击了模型，触发表情变化
            if (this.vrm && this.isModelLoaded) {
                this.triggerRandomExpression();
            }
        } catch (error) {
            console.error('🚨 鼠标点击处理错误:', error);
        }
    }



    /**
     * 处理鼠标按下事件
     */
    handleMouseDown(event) {
        console.log('🖱️ 鼠标按下', event);
        this.canvas.style.cursor = 'grabbing';

        // 开始拖动
        this.isDragging = true;
        const rect = this.canvas.getBoundingClientRect();
        this.dragStartX = event.clientX - rect.left;
        this.dragStartY = event.clientY - rect.top;
        this.lastMouseX = this.dragStartX;
        this.lastMouseY = this.dragStartY;

        console.log(`🖱️ 拖动开始: (${this.dragStartX}, ${this.dragStartY}), isDragging=${this.isDragging}`);

        // 阻止默认行为
        event.preventDefault();
    }

    /**
     * 处理鼠标释放事件
     */
    handleMouseUp(event) {
        console.log('🖱️ 鼠标释放');
        this.canvas.style.cursor = 'pointer';

        // 结束拖动
        this.isDragging = false;
    }

    /**
     * 处理标准化鼠标移动（来自LayeredMouseTracker）
     */
    handleNormalizedMouseMove(normalizedX, normalizedY) {
        this.updateMousePosition(normalizedX, normalizedY);
    }

    /**
     * 更新鼠标位置
     */
    updateMousePosition(normalizedX, normalizedY) {
        // 暂时禁用视线跟踪以避免错误
        // TODO: 修复VRM lookAt API兼容性问题
        /*
        if (this.vrm && this.vrm.lookAt) {
            try {
                // 使用正确的VRM lookAt API
                const target = new THREE.Vector3(normalizedX * 2, normalizedY * 2 + 1.4, 1);

                // 检查VRM版本并使用相应的API
                if (typeof this.vrm.lookAt.lookAt === 'function') {
                    // VRM 1.0 API
                    this.vrm.lookAt.lookAt(target);
                } else if (typeof this.vrm.lookAt.target === 'object') {
                    // VRM 0.x API - 直接设置target
                    if (typeof this.vrm.lookAt.target.copy === 'function') {
                        this.vrm.lookAt.target.copy(target);
                    }
                }
            } catch (error) {
                // 完全忽略视线跟踪错误，不输出日志避免刷屏
            }
        }
        */
    }

    /**
     * 执行射线检测
     */
    performRaycast(normalizedX, normalizedY) {
        if (!this.camera || !this.scene) return;

        try {
            const raycaster = new THREE.Raycaster();
            const mouse = new THREE.Vector2(normalizedX, normalizedY);

            raycaster.setFromCamera(mouse, this.camera);

            // 检测与场景中所有对象的交集
            const intersects = raycaster.intersectObjects(this.scene.children, true);

            if (intersects.length > 0) {
                const intersect = intersects[0];
                console.log('🎯 射线检测命中:', intersect.object.name || '未命名对象');

                // 如果命中了VRM模型的一部分
                if (this.vrm && this.vrm.scene) {
                    let isVRMPart = false;
                    this.vrm.scene.traverse((child) => {
                        if (child === intersect.object) {
                            isVRMPart = true;
                        }
                    });

                    if (isVRMPart) {
                        console.log('✨ 点击了VRM模型！');
                        this.onVRMModelClicked(intersect);
                    }
                }
            }
        } catch (error) {
            console.warn('⚠️ 射线检测失败:', error);
        }
    }

    /**
     * VRM模型被点击时的处理
     */
    onVRMModelClicked(intersect) {
        console.log('🎭 VRM模型被点击！');

        // 触发随机表情
        this.triggerRandomExpression();

        // 可以添加其他交互效果，比如粒子效果、声音等
    }

    /**
     * 触发随机表情
     */
    triggerRandomExpression() {
        if (!this.vrm || !this.isModelLoaded) return;

        const expressions = ['happy', 'sad', 'surprised', 'angry', 'neutral', 'blink'];
        const randomExpression = expressions[Math.floor(Math.random() * expressions.length)];
        const randomWeight = 0.5 + Math.random() * 0.5; // 0.5-1.0

        console.log(`😊 触发随机表情: ${randomExpression} (${randomWeight.toFixed(2)})`);

        // 这里可以调用表情设置方法
        // this.setExpression(randomExpression, randomWeight);
    }

    /**
     * 通知Python端渲染器已准备就绪
     */
    notifyRendererReady() {
        try {
            if (window.vrmBridge && typeof window.vrmBridge.on_renderer_ready === 'function') {
                window.vrmBridge.on_renderer_ready();
                console.log('✅ 已通知Python端渲染器准备就绪');
            } else {
                console.log('⚠️ VRM桥接对象不可用，但不再重试避免循环');
            }
        } catch (error) {
            console.error('❌ 通知渲染器准备就绪失败:', error);
        }
    }

    /**
     * 桥接准备就绪回调
     */
    onBridgeReady() {
        console.log('✅ WebChannel桥接已准备就绪');
        // 如果渲染器已初始化，立即通知Python端
        if (this.isInitialized) {
            this.notifyRendererReady();
        }
    }

    /**
     * 设置渲染质量预设
     * @param {string} preset - 质量预设: 'low', 'medium', 'high', 'ultra'
     */
    setQualityPreset(preset) {
        const presets = {
            'performance': {
                enableShadows: false,
                enableAntialiasing: false,
                pixelRatio: 1,
                shadowMapSize: 256,
                targetFPS: 120,
                lightIntensity: 1.0,
                ambientLightIntensity: 0.6,
                enablePerformanceMonitor: false
            },
            'low': {
                enableShadows: false,
                enableAntialiasing: false,
                pixelRatio: 1,
                shadowMapSize: 512,
                targetFPS: 60,
                lightIntensity: 1.0,
                ambientLightIntensity: 0.5,
                enablePerformanceMonitor: true
            },
            'medium': {
                enableShadows: true,
                enableAntialiasing: true,
                pixelRatio: 1,
                shadowMapSize: 1024,
                targetFPS: 60,
                lightIntensity: 1.2,
                ambientLightIntensity: 0.4,
                enablePerformanceMonitor: true
            },
            'high': {
                enableShadows: true,
                enableAntialiasing: true,
                pixelRatio: Math.min(window.devicePixelRatio || 1, 2),
                shadowMapSize: 2048,
                targetFPS: 60,
                lightIntensity: 1.2,
                ambientLightIntensity: 0.4,
                enablePerformanceMonitor: true
            },
            'ultra': {
                enableShadows: true,
                enableAntialiasing: true,
                pixelRatio: window.devicePixelRatio || 1,
                shadowMapSize: 4096,
                targetFPS: 60,
                lightIntensity: 1.3,
                ambientLightIntensity: 0.3,
                enablePerformanceMonitor: true
            }
        };

        if (presets[preset]) {
            Object.assign(this.config, presets[preset]);
            console.log(`🎨 渲染质量已设置为: ${preset.toUpperCase()}`);

            // 如果渲染器已初始化，重新应用设置
            if (this.renderer) {
                this.renderer.setPixelRatio(this.config.pixelRatio);
                if (this.config.enableShadows) {
                    this.renderer.shadowMap.enabled = true;
                } else {
                    this.renderer.shadowMap.enabled = false;
                }
            }
        } else {
            console.warn(`⚠️ 未知的质量预设: ${preset}`);
        }
    }
}

// 导出到全局作用域
window.VRMRenderer = VRMRenderer;

console.log('📜 enhanced_vrm_renderer.js 已加载');
