/**
 * Layered Mouse Tracking
 * 分层鼠标跟踪器 - 提供平滑的鼠标跟踪和视线控制
 */

class LayeredMouseTracker {
    constructor(canvas) {
        this.canvas = canvas;
        this.isEnabled = true;
        this.isTracking = false;
        
        // 鼠标位置
        this.mouseX = 0;
        this.mouseY = 0;
        this.normalizedX = 0;
        this.normalizedY = 0;
        
        // 平滑参数
        this.smoothingFactor = 0.1;
        this.targetX = 0;
        this.targetY = 0;
        this.currentX = 0;
        this.currentY = 0;
        
        // 跟踪范围限制
        this.trackingBounds = {
            left: -1.0,
            right: 1.0,
            top: 1.0,
            bottom: -1.0
        };
        
        // 死区设置（中心区域不跟踪）
        this.deadZone = {
            enabled: true,
            radius: 0.1
        };
        
        // 跟踪层级
        this.layers = {
            head: {
                enabled: true,
                sensitivity: 1.0,
                maxAngle: Math.PI / 6, // 30度
                smoothing: 0.15
            },
            eyes: {
                enabled: true,
                sensitivity: 1.2,
                maxAngle: Math.PI / 4, // 45度
                smoothing: 0.2
            },
            body: {
                enabled: false,
                sensitivity: 0.3,
                maxAngle: Math.PI / 12, // 15度
                smoothing: 0.05
            }
        };
        
        // 回调函数
        this.onMouseMove = null;
        this.onMouseEnter = null;
        this.onMouseLeave = null;
        this.onTrackingStart = null;
        this.onTrackingStop = null;
        
        // 初始化事件监听
        this.initEventListeners();
        
        // 启动平滑更新循环
        this.startSmoothingLoop();
        
        console.log('🖱️ LayeredMouseTracker 已创建');
    }
    
    /**
     * 初始化事件监听器
     */
    initEventListeners() {
        if (!this.canvas) {
            console.warn('⚠️ 画布元素不存在，无法初始化鼠标跟踪');
            return;
        }
        
        // 鼠标移动事件
        this.canvas.addEventListener('mousemove', (event) => {
            this.handleMouseMove(event);
        });
        
        // 鼠标进入事件
        this.canvas.addEventListener('mouseenter', (event) => {
            this.handleMouseEnter(event);
        });
        
        // 鼠标离开事件
        this.canvas.addEventListener('mouseleave', (event) => {
            this.handleMouseLeave(event);
        });
        
        // 触摸事件支持
        this.canvas.addEventListener('touchmove', (event) => {
            event.preventDefault();
            if (event.touches.length > 0) {
                const touch = event.touches[0];
                this.handleMouseMove({
                    clientX: touch.clientX,
                    clientY: touch.clientY
                });
            }
        });
        
        this.canvas.addEventListener('touchstart', (event) => {
            this.handleMouseEnter(event);
        });
        
        this.canvas.addEventListener('touchend', (event) => {
            this.handleMouseLeave(event);
        });
        
        console.log('📱 鼠标和触摸事件监听器已初始化');
    }
    
    /**
     * 处理鼠标移动
     */
    handleMouseMove(event) {
        if (!this.isEnabled) return;
        
        // 获取画布边界
        const rect = this.canvas.getBoundingClientRect();
        
        // 计算相对位置
        this.mouseX = event.clientX - rect.left;
        this.mouseY = event.clientY - rect.top;
        
        // 归一化坐标 (-1 到 1)
        this.normalizedX = (this.mouseX / rect.width) * 2 - 1;
        this.normalizedY = -((this.mouseY / rect.height) * 2 - 1); // Y轴翻转
        
        // 应用边界限制
        this.normalizedX = Math.max(this.trackingBounds.left, 
                          Math.min(this.trackingBounds.right, this.normalizedX));
        this.normalizedY = Math.max(this.trackingBounds.bottom, 
                          Math.min(this.trackingBounds.top, this.normalizedY));
        
        // 检查死区
        if (this.deadZone.enabled) {
            const distance = Math.sqrt(this.normalizedX * this.normalizedX + 
                                     this.normalizedY * this.normalizedY);
            if (distance < this.deadZone.radius) {
                this.normalizedX = 0;
                this.normalizedY = 0;
            }
        }
        
        // 更新目标位置
        this.targetX = this.normalizedX;
        this.targetY = this.normalizedY;
        
        // 开始跟踪
        if (!this.isTracking) {
            this.startTracking();
        }
    }
    
    /**
     * 处理鼠标进入
     */
    handleMouseEnter(event) {
        if (!this.isEnabled) return;
        
        console.log('🖱️ 鼠标进入跟踪区域');
        
        if (this.onMouseEnter) {
            this.onMouseEnter(event);
        }
    }
    
    /**
     * 处理鼠标离开
     */
    handleMouseLeave(event) {
        if (!this.isEnabled) return;
        
        console.log('🖱️ 鼠标离开跟踪区域');
        
        // 停止跟踪
        this.stopTracking();
        
        if (this.onMouseLeave) {
            this.onMouseLeave(event);
        }
    }
    
    /**
     * 开始跟踪
     */
    startTracking() {
        if (this.isTracking) return;
        
        this.isTracking = true;
        console.log('👁️ 开始鼠标跟踪');
        
        if (this.onTrackingStart) {
            this.onTrackingStart();
        }
    }
    
    /**
     * 停止跟踪
     */
    stopTracking() {
        if (!this.isTracking) return;
        
        this.isTracking = false;
        
        // 平滑回到中心位置
        this.targetX = 0;
        this.targetY = 0;
        
        console.log('👁️ 停止鼠标跟踪');
        
        if (this.onTrackingStop) {
            this.onTrackingStop();
        }
    }
    
    /**
     * 启动平滑更新循环
     */
    startSmoothingLoop() {
        const update = () => {
            this.updateSmoothing();
            requestAnimationFrame(update);
        };
        
        update();
        console.log('🔄 平滑更新循环已启动');
    }
    
    /**
     * 更新平滑插值
     */
    updateSmoothing() {
        // 计算平滑插值
        const deltaX = this.targetX - this.currentX;
        const deltaY = this.targetY - this.currentY;
        
        this.currentX += deltaX * this.smoothingFactor;
        this.currentY += deltaY * this.smoothingFactor;
        
        // 如果有显著变化，触发回调
        const threshold = 0.001;
        if (Math.abs(deltaX) > threshold || Math.abs(deltaY) > threshold) {
            if (this.onMouseMove) {
                this.onMouseMove(this.currentX, this.currentY);
            }
        }
    }
    
    /**
     * 获取分层跟踪数据
     */
    getLayeredTrackingData() {
        const data = {};
        
        Object.keys(this.layers).forEach(layerName => {
            const layer = this.layers[layerName];
            if (!layer.enabled) return;
            
            // 应用层级特定的灵敏度和限制
            const adjustedX = this.currentX * layer.sensitivity;
            const adjustedY = this.currentY * layer.sensitivity;
            
            // 限制角度
            const angle = Math.sqrt(adjustedX * adjustedX + adjustedY * adjustedY);
            const maxAngle = layer.maxAngle;
            
            let finalX = adjustedX;
            let finalY = adjustedY;
            
            if (angle > maxAngle) {
                const scale = maxAngle / angle;
                finalX *= scale;
                finalY *= scale;
            }
            
            data[layerName] = {
                x: finalX,
                y: finalY,
                angle: angle,
                normalized: {
                    x: this.currentX,
                    y: this.currentY
                }
            };
        });
        
        return data;
    }
    
    /**
     * 设置跟踪参数
     */
    setTrackingParams(params) {
        if (params.smoothingFactor !== undefined) {
            this.smoothingFactor = Math.max(0.01, Math.min(1.0, params.smoothingFactor));
        }
        
        if (params.trackingBounds) {
            Object.assign(this.trackingBounds, params.trackingBounds);
        }
        
        if (params.deadZone) {
            Object.assign(this.deadZone, params.deadZone);
        }
        
        if (params.layers) {
            Object.keys(params.layers).forEach(layerName => {
                if (this.layers[layerName]) {
                    Object.assign(this.layers[layerName], params.layers[layerName]);
                }
            });
        }
        
        console.log('⚙️ 跟踪参数已更新:', params);
    }
    
    /**
     * 启用/禁用跟踪
     */
    setEnabled(enabled) {
        this.isEnabled = enabled;
        
        if (!enabled && this.isTracking) {
            this.stopTracking();
        }
        
        console.log(`🖱️ 鼠标跟踪${enabled ? '已启用' : '已禁用'}`);
    }
    
    /**
     * 获取当前状态
     */
    getStatus() {
        return {
            enabled: this.isEnabled,
            tracking: this.isTracking,
            mouse: {
                x: this.mouseX,
                y: this.mouseY,
                normalizedX: this.normalizedX,
                normalizedY: this.normalizedY
            },
            current: {
                x: this.currentX,
                y: this.currentY
            },
            target: {
                x: this.targetX,
                y: this.targetY
            }
        };
    }
    
    /**
     * 清理资源
     */
    dispose() {
        if (this.canvas) {
            this.canvas.removeEventListener('mousemove', this.handleMouseMove);
            this.canvas.removeEventListener('mouseenter', this.handleMouseEnter);
            this.canvas.removeEventListener('mouseleave', this.handleMouseLeave);
            this.canvas.removeEventListener('touchmove', this.handleMouseMove);
            this.canvas.removeEventListener('touchstart', this.handleMouseEnter);
            this.canvas.removeEventListener('touchend', this.handleMouseLeave);
        }
        
        this.isEnabled = false;
        this.isTracking = false;
        
        console.log('🧹 LayeredMouseTracker 资源已清理');
    }
}

// 导出到全局作用域
window.LayeredMouseTracker = LayeredMouseTracker;

console.log('📜 layered_mouse_tracking.js 已加载');
