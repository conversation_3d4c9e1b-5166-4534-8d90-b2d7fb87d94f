<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple VRM Test Renderer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: transparent;
            overflow: hidden;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        #test-canvas {
            display: block;
            width: 100vw;
            height: 100vh;
            background: transparent;
        }
        
        #debug-info {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px;
            font-size: 12px;
            border-radius: 5px;
            max-width: 300px;
        }
    </style>
</head>
<body>
    <canvas id="test-canvas"></canvas>
    
    <div id="debug-info">
        <div>状态: <span id="status">初始化中...</span></div>
        <div>THREE.js: <span id="three-status">检查中...</span></div>
        <div>VRM库: <span id="vrm-status">检查中...</span></div>
        <div>模型: <span id="model-status">未加载</span></div>
    </div>

    <!-- JavaScript库文件 -->
    <script src="../libs/three.min.js"></script>
    <!-- 使用CDN的GLTFLoader -->
    <script src="https://cdn.jsdelivr.net/npm/three@0.147.0/examples/js/loaders/GLTFLoader.js"></script>
    <script src="../libs/three-vrm.min.js"></script>
    <script src="../libs/three-vrm-v0.min.js"></script>

    <!-- Qt WebChannel支持 -->
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    
    <script>
        // 简单的测试渲染器
        class SimpleTestRenderer {
            constructor() {
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.canvas = null;
                this.isInitialized = false;
                
                this.updateStatus('开始初始化...');
            }
            
            updateStatus(message) {
                console.log('📊', message);
                document.getElementById('status').textContent = message;
            }
            
            updateThreeStatus(message) {
                document.getElementById('three-status').textContent = message;
            }
            
            updateVRMStatus(message) {
                document.getElementById('vrm-status').textContent = message;
            }
            
            updateModelStatus(message) {
                document.getElementById('model-status').textContent = message;
            }
            
            async init() {
                try {
                    // 检查THREE.js
                    if (typeof THREE === 'undefined') {
                        throw new Error('THREE.js未加载');
                    }
                    this.updateThreeStatus('✅ 已加载');
                    
                    // 检查GLTFLoader
                    if (!THREE.GLTFLoader) {
                        throw new Error('GLTFLoader未加载');
                    }
                    this.updateThreeStatus('✅ 已加载 (含GLTFLoader)');
                    
                    // 检查VRM库
                    this.checkVRMLibraries();
                    
                    // 获取画布
                    this.canvas = document.getElementById('test-canvas');
                    if (!this.canvas) {
                        throw new Error('找不到画布元素');
                    }
                    
                    // 初始化Three.js场景
                    this.initScene();
                    this.initCamera();
                    this.initRenderer();
                    this.initLights();
                    
                    // 添加一个测试立方体
                    this.addTestCube();
                    
                    // 开始渲染循环
                    this.startRenderLoop();
                    
                    this.isInitialized = true;
                    this.updateStatus('✅ 初始化完成');
                    
                    // 创建简化的桥接对象
                    this.createSimpleBridge();
                    
                    return true;
                    
                } catch (error) {
                    console.error('❌ 初始化失败:', error);
                    this.updateStatus('❌ 初始化失败: ' + error.message);
                    throw error;
                }
            }
            
            checkVRMLibraries() {
                let vrmStatus = [];
                
                if (typeof THREE_VRM !== 'undefined') {
                    vrmStatus.push('THREE_VRM');
                    console.log('📊 THREE_VRM属性:', Object.keys(THREE_VRM));
                }
                
                if (typeof VRM !== 'undefined') {
                    vrmStatus.push('VRM');
                    console.log('📊 VRM属性:', Object.keys(VRM));
                }
                
                if (vrmStatus.length > 0) {
                    this.updateVRMStatus('✅ ' + vrmStatus.join(', '));
                } else {
                    this.updateVRMStatus('❌ 未检测到VRM库');
                }
            }
            
            initScene() {
                this.scene = new THREE.Scene();
                this.scene.background = null; // 透明背景
                console.log('📦 场景初始化完成');
            }
            
            initCamera() {
                const aspect = window.innerWidth / window.innerHeight;
                this.camera = new THREE.PerspectiveCamera(30, aspect, 0.1, 20.0);
                this.camera.position.set(0, 1.4, 1.5);
                this.camera.lookAt(0, 1, 0);
                console.log('📷 摄像机初始化完成');
            }
            
            initRenderer() {
                this.renderer = new THREE.WebGLRenderer({
                    canvas: this.canvas,
                    alpha: true,
                    antialias: true,
                    preserveDrawingBuffer: true
                });
                
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.setPixelRatio(window.devicePixelRatio || 1);
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                
                console.log('🎨 渲染器初始化完成');
            }
            
            initLights() {
                // 主光源
                const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
                directionalLight.position.set(1, 1, 1).normalize();
                directionalLight.castShadow = true;
                this.scene.add(directionalLight);
                
                // 环境光
                const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
                this.scene.add(ambientLight);
                
                console.log('💡 光照初始化完成');
            }
            
            addTestCube() {
                // 添加一个红色立方体作为测试
                const geometry = new THREE.BoxGeometry(0.5, 0.5, 0.5);
                const material = new THREE.MeshLambertMaterial({ color: 0xff0000 });
                const cube = new THREE.Mesh(geometry, material);
                cube.position.set(0, 1, 0);
                cube.castShadow = true;
                this.scene.add(cube);
                
                // 添加一个地面
                const planeGeometry = new THREE.PlaneGeometry(5, 5);
                const planeMaterial = new THREE.MeshLambertMaterial({ color: 0x808080 });
                const plane = new THREE.Mesh(planeGeometry, planeMaterial);
                plane.rotation.x = -Math.PI / 2;
                plane.receiveShadow = true;
                this.scene.add(plane);
                
                console.log('🧊 测试立方体已添加');
            }
            
            startRenderLoop() {
                const animate = () => {
                    requestAnimationFrame(animate);
                    
                    // 旋转立方体
                    const cube = this.scene.children.find(child => child.geometry && child.geometry.type === 'BoxGeometry');
                    if (cube) {
                        cube.rotation.x += 0.01;
                        cube.rotation.y += 0.01;
                    }
                    
                    this.renderer.render(this.scene, this.camera);
                };
                
                animate();
                console.log('🔄 渲染循环已启动');
            }
            
            createSimpleBridge() {
                // 创建简化的桥接对象
                if (!window.vrmBridge) {
                    window.vrmBridge = {
                        on_renderer_ready: function() {
                            console.log('📡 渲染器准备就绪信号已发送');
                        },
                        on_model_loaded: function(path) {
                            console.log('📡 模型加载完成信号已发送:', path);
                        },
                        on_model_error: function(error) {
                            console.log('📡 模型错误信号已发送:', error);
                        }
                    };
                }
                
                // 通知渲染器准备就绪
                if (window.vrmBridge.on_renderer_ready) {
                    window.vrmBridge.on_renderer_ready();
                }
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 简单测试渲染器页面加载完成');
            
            window.testRenderer = new SimpleTestRenderer();
            window.testRenderer.init().catch(error => {
                console.error('渲染器初始化失败:', error);
            });
        });
        
        // 窗口大小改变时调整画布
        window.addEventListener('resize', function() {
            if (window.testRenderer && window.testRenderer.renderer) {
                window.testRenderer.camera.aspect = window.innerWidth / window.innerHeight;
                window.testRenderer.camera.updateProjectionMatrix();
                window.testRenderer.renderer.setSize(window.innerWidth, window.innerHeight);
            }
        });
    </script>
</body>
</html>
