/**
 * Universal VRM Loader
 * 通用VRM加载器 - 自动检测并支持VRM 1.0和0.x版本
 */

class UniversalVRMLoader {
    constructor() {
        // 检查THREE.GLTFLoader是否可用
        if (typeof THREE === 'undefined' || typeof THREE.GLTFLoader === 'undefined') {
            throw new Error('THREE.GLTFLoader不可用');
        }

        this.gltfLoader = new THREE.GLTFLoader();
        this.vrmLoaderV1 = null;
        this.vrmLoaderV0 = null;

        // 初始化VRM加载器
        this.initVRMLoaders();

        console.log('🔄 UniversalVRMLoader 已创建');
    }
    
    /**
     * 初始化VRM加载器
     */
    initVRMLoaders() {
        try {
            // 初始化VRM 1.0加载器
            if (typeof VRM !== 'undefined' && VRM.VRMLoaderPlugin) {
                this.vrmLoaderV1 = new THREE.GLTFLoader();
                this.vrmLoaderV1.register((parser) => new VRM.VRMLoaderPlugin(parser));
                console.log('✅ VRM 1.0 加载器已初始化');
            }
            
            // 初始化VRM 0.x加载器 (如果可用)
            if (typeof VRMSchema !== 'undefined' && typeof VRMUtils !== 'undefined') {
                this.vrmLoaderV0 = new THREE.GLTFLoader();
                // VRM 0.x的加载器注册方式可能不同
                console.log('✅ VRM 0.x 加载器已初始化');
            }
            
        } catch (error) {
            console.warn('⚠️ VRM加载器初始化部分失败:', error.message);
        }
    }
    
    /**
     * 检测VRM版本
     */
    async detectVRMVersion(url) {
        try {
            // 通过文件头或元数据检测VRM版本
            const response = await fetch(url);
            const arrayBuffer = await response.arrayBuffer();
            
            // 简单的版本检测逻辑
            const uint8Array = new Uint8Array(arrayBuffer.slice(0, 1024));
            const headerString = new TextDecoder().decode(uint8Array);
            
            if (headerString.includes('"VRM"') && headerString.includes('"specVersion"')) {
                // 检查是否包含VRM 1.0特征
                if (headerString.includes('"1.0"') || headerString.includes('vrm-1.0')) {
                    return '1.0';
                }
            }
            
            // 默认假设为VRM 0.x
            return '0.x';
            
        } catch (error) {
            console.warn('⚠️ VRM版本检测失败，默认使用1.0:', error.message);
            return '1.0';
        }
    }
    
    /**
     * 加载VRM模型
     */
    load(url, onLoad, onProgress, onError) {
        console.log(`📥 开始加载VRM模型: ${url}`);
        
        // 首先尝试使用VRM 1.0加载器
        this.loadWithV1(url, onLoad, onProgress, (error) => {
            console.warn('⚠️ VRM 1.0加载失败，尝试VRM 0.x:', error.message);
            
            // 如果VRM 1.0失败，尝试VRM 0.x
            this.loadWithV0(url, onLoad, onProgress, (error2) => {
                console.warn('⚠️ VRM 0.x加载也失败，尝试基础GLTF:', error2.message);
                
                // 最后尝试基础GLTF加载
                this.loadAsGLTF(url, onLoad, onProgress, onError);
            });
        });
    }
    
    /**
     * 使用VRM 1.0加载器
     */
    loadWithV1(url, onLoad, onProgress, onError) {
        if (!this.vrmLoaderV1) {
            onError(new Error('VRM 1.0加载器不可用'));
            return;
        }
        
        this.vrmLoaderV1.load(
            url,
            (gltf) => {
                try {
                    const vrm = gltf.userData.vrm;
                    if (vrm) {
                        console.log('✅ VRM 1.0模型加载成功');
                        onLoad(gltf);
                    } else {
                        throw new Error('GLTF中未找到VRM数据');
                    }
                } catch (error) {
                    onError(error);
                }
            },
            onProgress,
            onError
        );
    }
    
    /**
     * 使用VRM 0.x加载器
     */
    loadWithV0(url, onLoad, onProgress, onError) {
        if (!this.vrmLoaderV0) {
            onError(new Error('VRM 0.x加载器不可用'));
            return;
        }
        
        // VRM 0.x的加载逻辑
        this.gltfLoader.load(
            url,
            async (gltf) => {
                try {
                    // 尝试解析VRM 0.x数据
                    if (gltf.userData && gltf.userData.gltfExtensions && gltf.userData.gltfExtensions.VRM) {
                        console.log('✅ VRM 0.x模型加载成功');
                        
                        // 创建VRM 0.x包装对象
                        const vrmWrapper = {
                            scene: gltf.scene,
                            userData: { vrm: gltf },
                            update: (deltaTime) => {
                                // VRM 0.x的更新逻辑
                            },
                            dispose: () => {
                                // VRM 0.x的清理逻辑
                            }
                        };
                        
                        onLoad(vrmWrapper);
                    } else {
                        throw new Error('未找到VRM 0.x扩展数据');
                    }
                } catch (error) {
                    onError(error);
                }
            },
            onProgress,
            onError
        );
    }
    
    /**
     * 作为基础GLTF加载
     */
    loadAsGLTF(url, onLoad, onProgress, onError) {
        console.log('📦 尝试作为基础GLTF模型加载');
        
        this.gltfLoader.load(
            url,
            (gltf) => {
                try {
                    console.log('✅ GLTF模型加载成功');
                    
                    // 创建基础包装对象
                    const basicWrapper = {
                        scene: gltf.scene,
                        userData: { vrm: null },
                        update: (deltaTime) => {
                            // 基础更新逻辑
                        },
                        dispose: () => {
                            // 基础清理逻辑
                            if (gltf.scene) {
                                gltf.scene.traverse((child) => {
                                    if (child.geometry) child.geometry.dispose();
                                    if (child.material) {
                                        if (Array.isArray(child.material)) {
                                            child.material.forEach(material => material.dispose());
                                        } else {
                                            child.material.dispose();
                                        }
                                    }
                                });
                            }
                        }
                    };
                    
                    onLoad(basicWrapper);
                    
                } catch (error) {
                    onError(error);
                }
            },
            onProgress,
            onError
        );
    }
    
    /**
     * 设置加载管理器
     */
    setManager(manager) {
        this.gltfLoader.setManager(manager);
        if (this.vrmLoaderV1) this.vrmLoaderV1.setManager(manager);
        if (this.vrmLoaderV0) this.vrmLoaderV0.setManager(manager);
    }
    
    /**
     * 设置路径
     */
    setPath(path) {
        this.gltfLoader.setPath(path);
        if (this.vrmLoaderV1) this.vrmLoaderV1.setPath(path);
        if (this.vrmLoaderV0) this.vrmLoaderV0.setPath(path);
    }
    
    /**
     * 设置资源路径
     */
    setResourcePath(resourcePath) {
        this.gltfLoader.setResourcePath(resourcePath);
        if (this.vrmLoaderV1) this.vrmLoaderV1.setResourcePath(resourcePath);
        if (this.vrmLoaderV0) this.vrmLoaderV0.setResourcePath(resourcePath);
    }
    
    /**
     * 设置请求头
     */
    setRequestHeader(requestHeader) {
        this.gltfLoader.setRequestHeader(requestHeader);
        if (this.vrmLoaderV1) this.vrmLoaderV1.setRequestHeader(requestHeader);
        if (this.vrmLoaderV0) this.vrmLoaderV0.setRequestHeader(requestHeader);
    }
    
    /**
     * 设置凭证
     */
    setWithCredentials(value) {
        this.gltfLoader.setWithCredentials(value);
        if (this.vrmLoaderV1) this.vrmLoaderV1.setWithCredentials(value);
        if (this.vrmLoaderV0) this.vrmLoaderV0.setWithCredentials(value);
    }
    
    /**
     * 注册插件
     */
    register(callback) {
        this.gltfLoader.register(callback);
        if (this.vrmLoaderV1) this.vrmLoaderV1.register(callback);
        if (this.vrmLoaderV0) this.vrmLoaderV0.register(callback);
    }
    
    /**
     * 注销插件
     */
    unregister(callback) {
        this.gltfLoader.unregister(callback);
        if (this.vrmLoaderV1) this.vrmLoaderV1.unregister(callback);
        if (this.vrmLoaderV0) this.vrmLoaderV0.unregister(callback);
    }
}

// 导出到全局作用域
window.UniversalVRMLoader = UniversalVRMLoader;

console.log('📜 universal_vrm_loader.js 已加载');
