#!/usr/bin/env python3
"""
VRM测试服务器启动脚本
解决浏览器本地文件访问限制问题
"""

import http.server
import socketserver
import os
import sys
import webbrowser
from pathlib import Path

def start_server(port=8000):
    """启动HTTP服务器"""
    
    # 切换到VRM目录
    vrm_dir = Path(__file__).parent
    os.chdir(vrm_dir)
    
    print(f"🚀 启动VRM测试服务器...")
    print(f"📁 服务目录: {vrm_dir}")
    print(f"🌐 端口: {port}")
    
    # 创建服务器
    handler = http.server.SimpleHTTPRequestHandler
    
    # 添加CORS头部支持
    class CORSRequestHandler(handler):
        def end_headers(self):
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', '*')
            super().end_headers()
            
        def guess_type(self, path):
            """为VRM文件设置正确的MIME类型"""
            mimetype, encoding = super().guess_type(path)
            if path.endswith('.vrm'):
                return 'model/gltf-binary', encoding
            return mimetype, encoding
    
    with socketserver.TCPServer(("", port), CORSRequestHandler) as httpd:
        print(f"✅ 服务器已启动: http://localhost:{port}")
        print(f"🔗 调试页面: http://localhost:{port}/debug_vrm_loader.html")
        print(f"🔗 渲染器页面: http://localhost:{port}/renderer/enhanced_vrm_renderer.html")
        print("\n📋 可用的测试文件:")
        
        # 列出可用的VRM文件
        models_dir = vrm_dir / "models"
        if models_dir.exists():
            for vrm_file in models_dir.glob("*.vrm"):
                print(f"   - {vrm_file.name}")
        
        print("\n按 Ctrl+C 停止服务器")
        
        # 自动打开浏览器
        try:
            webbrowser.open(f"http://localhost:{port}/debug_vrm_loader.html")
        except:
            pass
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 服务器已停止")

if __name__ == "__main__":
    port = 8000
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("❌ 端口号必须是数字")
            sys.exit(1)
    
    start_server(port)
