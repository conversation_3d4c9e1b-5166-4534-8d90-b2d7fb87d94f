#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VRM表情数据模型

定义VRM表情和变形目标的数据结构
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Tuple
from enum import Enum

class ExpressionType(Enum):
    """表情类型"""
    PRESET = "preset"      # 预设表情
    CUSTOM = "custom"      # 自定义表情

class PresetExpression(Enum):
    """预设表情名称"""
    NEUTRAL = "neutral"
    HAPPY = "happy"
    ANGRY = "angry"
    SAD = "sad"
    RELAXED = "relaxed"
    SURPRISED = "surprised"
    BLINK = "blink"
    BLINK_L = "blinkLeft"
    BLINK_R = "blinkRight"
    LOOKUP = "lookUp"
    LOOKDOWN = "lookDown"
    LOOKLEFT = "lookLeft"
    LOOKRIGHT = "lookRight"
    AA = "aa"
    IH = "ih"
    OU = "ou"
    EE = "ee"
    OH = "oh"

@dataclass
class MorphTarget:
    """变形目标"""
    
    # 目标信息
    node_index: int = 0
    mesh_index: int = 0
    target_index: int = 0
    weight: float = 0.0
    
    # 变形数据
    position_deltas: List[Tuple[float, float, float]] = field(default_factory=list)
    normal_deltas: List[Tuple[float, float, float]] = field(default_factory=list)
    tangent_deltas: List[Tuple[float, float, float]] = field(default_factory=list)
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "nodeIndex": self.node_index,
            "meshIndex": self.mesh_index,
            "targetIndex": self.target_index,
            "weight": self.weight,
            "positionDeltas": self.position_deltas,
            "normalDeltas": self.normal_deltas,
            "tangentDeltas": self.tangent_deltas
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'MorphTarget':
        """从字典创建"""
        return cls(
            node_index=data.get("nodeIndex", 0),
            mesh_index=data.get("meshIndex", 0),
            target_index=data.get("targetIndex", 0),
            weight=data.get("weight", 0.0),
            position_deltas=data.get("positionDeltas", []),
            normal_deltas=data.get("normalDeltas", []),
            tangent_deltas=data.get("tangentDeltas", [])
        )

@dataclass
class MaterialColorBind:
    """材质颜色绑定"""
    
    material_index: int = 0
    property_name: str = ""
    target_value: Tuple[float, float, float, float] = (1.0, 1.0, 1.0, 1.0)
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "materialIndex": self.material_index,
            "propertyName": self.property_name,
            "targetValue": list(self.target_value)
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'MaterialColorBind':
        """从字典创建"""
        return cls(
            material_index=data.get("materialIndex", 0),
            property_name=data.get("propertyName", ""),
            target_value=tuple(data.get("targetValue", [1.0, 1.0, 1.0, 1.0]))
        )

@dataclass
class TextureTransformBind:
    """纹理变换绑定"""
    
    material_index: int = 0
    property_name: str = ""
    offset: Tuple[float, float] = (0.0, 0.0)
    scale: Tuple[float, float] = (1.0, 1.0)
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "materialIndex": self.material_index,
            "propertyName": self.property_name,
            "offset": list(self.offset),
            "scale": list(self.scale)
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'TextureTransformBind':
        """从字典创建"""
        return cls(
            material_index=data.get("materialIndex", 0),
            property_name=data.get("propertyName", ""),
            offset=tuple(data.get("offset", [0.0, 0.0])),
            scale=tuple(data.get("scale", [1.0, 1.0]))
        )

@dataclass
class VRMExpression:
    """VRM表情数据"""
    
    # 基础信息
    name: str = ""
    index: int = 0
    expression_type: ExpressionType = ExpressionType.CUSTOM
    
    # 权重信息
    weight: float = 0.0
    is_binary: bool = False
    
    # 变形目标
    morph_targets: List[MorphTarget] = field(default_factory=list)
    
    # 材质绑定
    material_color_binds: List[MaterialColorBind] = field(default_factory=list)
    texture_transform_binds: List[TextureTransformBind] = field(default_factory=list)
    
    # 覆盖设置
    override_blink: str = "none"      # none, block, blend
    override_look_at: str = "none"    # none, block, blend
    override_mouth: str = "none"      # none, block, blend
    
    def __post_init__(self):
        """初始化后处理"""
        # 确保权重在有效范围内
        self.weight = max(0.0, min(1.0, self.weight))
    
    @property
    def is_preset(self) -> bool:
        """是否为预设表情"""
        return self.expression_type == ExpressionType.PRESET
    
    @property
    def is_custom(self) -> bool:
        """是否为自定义表情"""
        return self.expression_type == ExpressionType.CUSTOM
    
    @property
    def has_morph_targets(self) -> bool:
        """是否有变形目标"""
        return len(self.morph_targets) > 0
    
    @property
    def has_material_binds(self) -> bool:
        """是否有材质绑定"""
        return (len(self.material_color_binds) > 0 or 
                len(self.texture_transform_binds) > 0)
    
    @property
    def morph_target_count(self) -> int:
        """变形目标数量"""
        return len(self.morph_targets)
    
    @property
    def material_bind_count(self) -> int:
        """材质绑定数量"""
        return len(self.material_color_binds) + len(self.texture_transform_binds)
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            "name": self.name,
            "index": self.index,
            "type": self.expression_type.value,
            "weight": self.weight,
            "isBinary": self.is_binary,
            
            "morphTargets": [target.to_dict() for target in self.morph_targets],
            "materialColorBinds": [bind.to_dict() for bind in self.material_color_binds],
            "textureTransformBinds": [bind.to_dict() for bind in self.texture_transform_binds],
            
            "overrideBlink": self.override_blink,
            "overrideLookAt": self.override_look_at,
            "overrideMouth": self.override_mouth,
            
            # 状态信息
            "isPreset": self.is_preset,
            "isCustom": self.is_custom,
            "hasMorphTargets": self.has_morph_targets,
            "hasMaterialBinds": self.has_material_binds,
            "morphTargetCount": self.morph_target_count,
            "materialBindCount": self.material_bind_count
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'VRMExpression':
        """从字典创建实例"""
        expression = cls(
            name=data.get("name", ""),
            index=data.get("index", 0),
            expression_type=ExpressionType(data.get("type", "custom")),
            weight=data.get("weight", 0.0),
            is_binary=data.get("isBinary", False),
            override_blink=data.get("overrideBlink", "none"),
            override_look_at=data.get("overrideLookAt", "none"),
            override_mouth=data.get("overrideMouth", "none")
        )
        
        # 添加变形目标
        for target_data in data.get("morphTargets", []):
            expression.morph_targets.append(MorphTarget.from_dict(target_data))
        
        # 添加材质颜色绑定
        for bind_data in data.get("materialColorBinds", []):
            expression.material_color_binds.append(MaterialColorBind.from_dict(bind_data))
        
        # 添加纹理变换绑定
        for bind_data in data.get("textureTransformBinds", []):
            expression.texture_transform_binds.append(TextureTransformBind.from_dict(bind_data))
        
        return expression
    
    def apply_weight(self, weight: float):
        """应用权重到所有变形目标"""
        self.weight = max(0.0, min(1.0, weight))
        
        # 更新变形目标权重
        for morph_target in self.morph_targets:
            morph_target.weight = self.weight
    
    def blend_with(self, other: 'VRMExpression', factor: float) -> 'VRMExpression':
        """与另一个表情混合"""
        # 创建新的表情实例
        blended = VRMExpression(
            name=f"{self.name}_blend_{other.name}",
            expression_type=ExpressionType.CUSTOM,
            weight=self.weight * (1.0 - factor) + other.weight * factor
        )
        
        # TODO: 实现变形目标和材质绑定的混合逻辑
        
        return blended
    
    def get_affected_meshes(self) -> List[int]:
        """获取受影响的网格索引"""
        mesh_indices = set()
        for morph_target in self.morph_targets:
            mesh_indices.add(morph_target.mesh_index)
        return list(mesh_indices)
    
    def get_affected_materials(self) -> List[int]:
        """获取受影响的材质索引"""
        material_indices = set()
        for bind in self.material_color_binds:
            material_indices.add(bind.material_index)
        for bind in self.texture_transform_binds:
            material_indices.add(bind.material_index)
        return list(material_indices)
