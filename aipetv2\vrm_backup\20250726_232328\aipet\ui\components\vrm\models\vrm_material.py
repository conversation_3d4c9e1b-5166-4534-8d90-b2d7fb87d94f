#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VRM材质数据模型

定义VRM材质的数据结构，特别是MToon材质
"""

from dataclasses import dataclass, field
from typing import List, Optional, Tuple, Dict, Any
from enum import Enum

class MaterialType(Enum):
    """材质类型"""
    STANDARD = "Standard"
    MTOON = "MToon"
    UNLIT = "Unlit"

class BlendMode(Enum):
    """混合模式"""
    OPAQUE = "Opaque"
    CUTOUT = "Cutout"
    TRANSPARENT = "Transparent"

class CullMode(Enum):
    """剔除模式"""
    OFF = "Off"
    FRONT = "Front"
    BACK = "Back"

@dataclass
class VRMMaterial:
    """VRM材质数据"""
    
    # 基础信息
    name: str = ""
    index: int = 0
    material_type: MaterialType = MaterialType.MTOON
    
    # 基础属性
    base_color: Tuple[float, float, float, float] = (1.0, 1.0, 1.0, 1.0)
    base_color_texture: str = ""
    
    # MToon特有属性
    shade_color: Tuple[float, float, float] = (0.8, 0.8, 0.8)
    shade_multiply_texture: str = ""
    
    # 阴影控制
    shading_shift_factor: float = 0.0
    shading_shift_texture: str = ""
    shading_shift_texture_scale: float = 1.0
    shading_toony_factor: float = 0.9
    
    # 全局照明
    gi_equalization_factor: float = 0.9
    
    # 边缘光
    parametric_rim_color_factor: Tuple[float, float, float] = (0.0, 0.0, 0.0)
    rim_multiply_texture: str = ""
    rim_lighting_mix_factor: float = 0.0
    parametric_rim_fresnel_power_factor: float = 1.0
    parametric_rim_lift_factor: float = 0.0
    
    # MatCap
    matcap_factor: Tuple[float, float, float] = (0.0, 0.0, 0.0)
    matcap_texture: str = ""
    
    # 自发光
    emissive_factor: Tuple[float, float, float] = (0.0, 0.0, 0.0)
    emissive_texture: str = ""
    emissive_intensity: float = 1.0
    
    # 法线贴图
    normal_texture: str = ""
    normal_scale: float = 1.0
    
    # 描边
    outline_width_mode: str = "None"
    outline_width_factor: float = 0.0
    outline_width_multiply_texture: str = ""
    outline_color_factor: Tuple[float, float, float] = (0.0, 0.0, 0.0)
    outline_lighting_mix_factor: float = 1.0
    
    # UV动画
    uv_animation_mask_texture: str = ""
    uv_animation_scroll_x_speed_factor: float = 0.0
    uv_animation_scroll_y_speed_factor: float = 0.0
    uv_animation_rotation_speed_factor: float = 0.0
    
    # 渲染设置
    blend_mode: BlendMode = BlendMode.OPAQUE
    cull_mode: CullMode = CullMode.BACK
    alpha_cutoff: float = 0.5
    double_sided: bool = False
    
    # 其他属性
    metallic_factor: float = 0.0
    roughness_factor: float = 1.0
    
    # 纹理变换
    texture_transforms: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.texture_transforms:
            self.texture_transforms = {}
    
    @property
    def is_transparent(self) -> bool:
        """是否透明"""
        return (self.blend_mode == BlendMode.TRANSPARENT or 
                self.base_color[3] < 1.0)
    
    @property
    def has_outline(self) -> bool:
        """是否有描边"""
        return (self.outline_width_mode != "None" and 
                self.outline_width_factor > 0.0)
    
    @property
    def has_emission(self) -> bool:
        """是否有自发光"""
        return (self.emissive_factor != (0.0, 0.0, 0.0) or 
                self.emissive_texture != "")
    
    @property
    def has_rim_light(self) -> bool:
        """是否有边缘光"""
        return self.parametric_rim_color_factor != (0.0, 0.0, 0.0)
    
    @property
    def has_matcap(self) -> bool:
        """是否有MatCap"""
        return (self.matcap_factor != (0.0, 0.0, 0.0) or 
                self.matcap_texture != "")
    
    @property
    def has_uv_animation(self) -> bool:
        """是否有UV动画"""
        return (self.uv_animation_scroll_x_speed_factor != 0.0 or
                self.uv_animation_scroll_y_speed_factor != 0.0 or
                self.uv_animation_rotation_speed_factor != 0.0)
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            "name": self.name,
            "index": self.index,
            "type": self.material_type.value,
            
            # 基础属性
            "baseColor": list(self.base_color),
            "baseColorTexture": self.base_color_texture,
            
            # MToon属性
            "shadeColor": list(self.shade_color),
            "shadeMultiplyTexture": self.shade_multiply_texture,
            "shadingShiftFactor": self.shading_shift_factor,
            "shadingShiftTexture": self.shading_shift_texture,
            "shadingShiftTextureScale": self.shading_shift_texture_scale,
            "shadingToonyFactor": self.shading_toony_factor,
            "giEqualizationFactor": self.gi_equalization_factor,
            
            # 边缘光
            "parametricRimColorFactor": list(self.parametric_rim_color_factor),
            "rimMultiplyTexture": self.rim_multiply_texture,
            "rimLightingMixFactor": self.rim_lighting_mix_factor,
            "parametricRimFresnelPowerFactor": self.parametric_rim_fresnel_power_factor,
            "parametricRimLiftFactor": self.parametric_rim_lift_factor,
            
            # MatCap
            "matcapFactor": list(self.matcap_factor),
            "matcapTexture": self.matcap_texture,
            
            # 自发光
            "emissiveFactor": list(self.emissive_factor),
            "emissiveTexture": self.emissive_texture,
            "emissiveIntensity": self.emissive_intensity,
            
            # 法线
            "normalTexture": self.normal_texture,
            "normalScale": self.normal_scale,
            
            # 描边
            "outlineWidthMode": self.outline_width_mode,
            "outlineWidthFactor": self.outline_width_factor,
            "outlineWidthMultiplyTexture": self.outline_width_multiply_texture,
            "outlineColorFactor": list(self.outline_color_factor),
            "outlineLightingMixFactor": self.outline_lighting_mix_factor,
            
            # UV动画
            "uvAnimationMaskTexture": self.uv_animation_mask_texture,
            "uvAnimationScrollXSpeedFactor": self.uv_animation_scroll_x_speed_factor,
            "uvAnimationScrollYSpeedFactor": self.uv_animation_scroll_y_speed_factor,
            "uvAnimationRotationSpeedFactor": self.uv_animation_rotation_speed_factor,
            
            # 渲染设置
            "blendMode": self.blend_mode.value,
            "cullMode": self.cull_mode.value,
            "alphaCutoff": self.alpha_cutoff,
            "doubleSided": self.double_sided,
            
            # 其他
            "metallicFactor": self.metallic_factor,
            "roughnessFactor": self.roughness_factor,
            
            # 状态标志
            "isTransparent": self.is_transparent,
            "hasOutline": self.has_outline,
            "hasEmission": self.has_emission,
            "hasRimLight": self.has_rim_light,
            "hasMatcap": self.has_matcap,
            "hasUVAnimation": self.has_uv_animation,
            
            # 纹理变换
            "textureTransforms": self.texture_transforms
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'VRMMaterial':
        """从字典创建实例"""
        return cls(
            name=data.get("name", ""),
            index=data.get("index", 0),
            material_type=MaterialType(data.get("type", "MToon")),
            
            base_color=tuple(data.get("baseColor", [1.0, 1.0, 1.0, 1.0])),
            base_color_texture=data.get("baseColorTexture", ""),
            
            shade_color=tuple(data.get("shadeColor", [0.8, 0.8, 0.8])),
            shade_multiply_texture=data.get("shadeMultiplyTexture", ""),
            
            shading_shift_factor=data.get("shadingShiftFactor", 0.0),
            shading_shift_texture=data.get("shadingShiftTexture", ""),
            shading_shift_texture_scale=data.get("shadingShiftTextureScale", 1.0),
            shading_toony_factor=data.get("shadingToonyFactor", 0.9),
            gi_equalization_factor=data.get("giEqualizationFactor", 0.9),
            
            parametric_rim_color_factor=tuple(data.get("parametricRimColorFactor", [0.0, 0.0, 0.0])),
            rim_multiply_texture=data.get("rimMultiplyTexture", ""),
            rim_lighting_mix_factor=data.get("rimLightingMixFactor", 0.0),
            parametric_rim_fresnel_power_factor=data.get("parametricRimFresnelPowerFactor", 1.0),
            parametric_rim_lift_factor=data.get("parametricRimLiftFactor", 0.0),
            
            matcap_factor=tuple(data.get("matcapFactor", [0.0, 0.0, 0.0])),
            matcap_texture=data.get("matcapTexture", ""),
            
            emissive_factor=tuple(data.get("emissiveFactor", [0.0, 0.0, 0.0])),
            emissive_texture=data.get("emissiveTexture", ""),
            emissive_intensity=data.get("emissiveIntensity", 1.0),
            
            normal_texture=data.get("normalTexture", ""),
            normal_scale=data.get("normalScale", 1.0),
            
            outline_width_mode=data.get("outlineWidthMode", "None"),
            outline_width_factor=data.get("outlineWidthFactor", 0.0),
            outline_width_multiply_texture=data.get("outlineWidthMultiplyTexture", ""),
            outline_color_factor=tuple(data.get("outlineColorFactor", [0.0, 0.0, 0.0])),
            outline_lighting_mix_factor=data.get("outlineLightingMixFactor", 1.0),
            
            uv_animation_mask_texture=data.get("uvAnimationMaskTexture", ""),
            uv_animation_scroll_x_speed_factor=data.get("uvAnimationScrollXSpeedFactor", 0.0),
            uv_animation_scroll_y_speed_factor=data.get("uvAnimationScrollYSpeedFactor", 0.0),
            uv_animation_rotation_speed_factor=data.get("uvAnimationRotationSpeedFactor", 0.0),
            
            blend_mode=BlendMode(data.get("blendMode", "Opaque")),
            cull_mode=CullMode(data.get("cullMode", "Back")),
            alpha_cutoff=data.get("alphaCutoff", 0.5),
            double_sided=data.get("doubleSided", False),
            
            metallic_factor=data.get("metallicFactor", 0.0),
            roughness_factor=data.get("roughnessFactor", 1.0),
            
            texture_transforms=data.get("textureTransforms", {})
        )
    
    def get_shader_uniforms(self) -> Dict[str, Any]:
        """获取着色器uniform变量"""
        return {
            "litFactor": self.base_color[:3],
            "shadeColorFactor": self.shade_color,
            "shadingShiftFactor": self.shading_shift_factor,
            "shadingToonyFactor": self.shading_toony_factor,
            "giEqualizationFactor": self.gi_equalization_factor,
            "parametricRimColorFactor": self.parametric_rim_color_factor,
            "rimLightingMixFactor": self.rim_lighting_mix_factor,
            "parametricRimFresnelPowerFactor": self.parametric_rim_fresnel_power_factor,
            "parametricRimLiftFactor": self.parametric_rim_lift_factor,
            "matcapFactor": self.matcap_factor,
            "emissiveFactor": self.emissive_factor,
            "emissiveIntensity": self.emissive_intensity,
            "outlineColorFactor": self.outline_color_factor,
            "outlineWidthFactor": self.outline_width_factor,
            "outlineLightingMixFactor": self.outline_lighting_mix_factor,
            "normalScale": self.normal_scale,
            "alphaCutoff": self.alpha_cutoff
        }
