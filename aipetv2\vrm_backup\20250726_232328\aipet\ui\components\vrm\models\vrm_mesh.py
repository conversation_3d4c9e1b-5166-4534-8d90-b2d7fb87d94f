#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VRM网格数据模型

定义VRM几何体的数据结构
"""

from dataclasses import dataclass
from typing import List, Optional, Tuple
import numpy as np

@dataclass
class VRMMesh:
    """VRM网格数据"""
    
    # 基础信息
    name: str = ""
    index: int = 0
    
    # 几何数据
    vertices: List[Tuple[float, float, float]] = None
    normals: List[Tuple[float, float, float]] = None
    uvs: List[Tuple[float, float]] = None
    indices: List[int] = None
    
    # 材质信息
    material_index: int = 0
    
    # 表情相关
    morph_targets: List[dict] = None
    
    # 骨骼相关
    bone_indices: List[List[int]] = None
    bone_weights: List[List[float]] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.vertices is None:
            self.vertices = []
        if self.normals is None:
            self.normals = []
        if self.uvs is None:
            self.uvs = []
        if self.indices is None:
            self.indices = []
        if self.morph_targets is None:
            self.morph_targets = []
        if self.bone_indices is None:
            self.bone_indices = []
        if self.bone_weights is None:
            self.bone_weights = []
    
    @property
    def vertex_count(self) -> int:
        """顶点数量"""
        return len(self.vertices)
    
    @property
    def triangle_count(self) -> int:
        """三角形数量"""
        return len(self.indices) // 3
    
    @property
    def has_normals(self) -> bool:
        """是否有法线数据"""
        return len(self.normals) == len(self.vertices)
    
    @property
    def has_uvs(self) -> bool:
        """是否有UV数据"""
        return len(self.uvs) == len(self.vertices)
    
    @property
    def has_morph_targets(self) -> bool:
        """是否有表情变形目标"""
        return len(self.morph_targets) > 0
    
    @property
    def has_bone_data(self) -> bool:
        """是否有骨骼数据"""
        return len(self.bone_indices) == len(self.vertices)
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            "name": self.name,
            "index": self.index,
            "vertices": self.vertices,
            "normals": self.normals,
            "uvs": self.uvs,
            "indices": self.indices,
            "materialIndex": self.material_index,
            "morphTargets": self.morph_targets,
            "boneIndices": self.bone_indices,
            "boneWeights": self.bone_weights,
            "vertexCount": self.vertex_count,
            "triangleCount": self.triangle_count,
            "hasNormals": self.has_normals,
            "hasUVs": self.has_uvs,
            "hasMorphTargets": self.has_morph_targets,
            "hasBoneData": self.has_bone_data
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'VRMMesh':
        """从字典创建实例"""
        return cls(
            name=data.get("name", ""),
            index=data.get("index", 0),
            vertices=data.get("vertices", []),
            normals=data.get("normals", []),
            uvs=data.get("uvs", []),
            indices=data.get("indices", []),
            material_index=data.get("materialIndex", 0),
            morph_targets=data.get("morphTargets", []),
            bone_indices=data.get("boneIndices", []),
            bone_weights=data.get("boneWeights", [])
        )
    
    def validate(self) -> Tuple[bool, List[str]]:
        """验证数据完整性"""
        errors = []
        
        if not self.vertices:
            errors.append("缺少顶点数据")
        
        if self.normals and len(self.normals) != len(self.vertices):
            errors.append("法线数量与顶点数量不匹配")
        
        if self.uvs and len(self.uvs) != len(self.vertices):
            errors.append("UV数量与顶点数量不匹配")
        
        if self.indices and len(self.indices) % 3 != 0:
            errors.append("索引数量不是3的倍数")
        
        if self.bone_indices and len(self.bone_indices) != len(self.vertices):
            errors.append("骨骼索引数量与顶点数量不匹配")
        
        if self.bone_weights and len(self.bone_weights) != len(self.vertices):
            errors.append("骨骼权重数量与顶点数量不匹配")
        
        return len(errors) == 0, errors
    
    def get_bounding_box(self) -> Tuple[Tuple[float, float, float], Tuple[float, float, float]]:
        """获取包围盒"""
        if not self.vertices:
            return (0, 0, 0), (0, 0, 0)
        
        vertices_array = np.array(self.vertices)
        min_coords = vertices_array.min(axis=0)
        max_coords = vertices_array.max(axis=0)
        
        return tuple(min_coords), tuple(max_coords)
    
    def calculate_normals(self):
        """计算法线（如果缺失）"""
        if not self.vertices or not self.indices:
            return
        
        vertices_array = np.array(self.vertices)
        normals_array = np.zeros_like(vertices_array)
        
        # 计算每个三角形的法线
        for i in range(0, len(self.indices), 3):
            i0, i1, i2 = self.indices[i:i+3]
            
            v0 = vertices_array[i0]
            v1 = vertices_array[i1]
            v2 = vertices_array[i2]
            
            # 计算三角形法线
            edge1 = v1 - v0
            edge2 = v2 - v0
            normal = np.cross(edge1, edge2)
            
            # 归一化
            length = np.linalg.norm(normal)
            if length > 0:
                normal = normal / length
            
            # 累加到顶点法线
            normals_array[i0] += normal
            normals_array[i1] += normal
            normals_array[i2] += normal
        
        # 归一化顶点法线
        for i in range(len(normals_array)):
            length = np.linalg.norm(normals_array[i])
            if length > 0:
                normals_array[i] = normals_array[i] / length
        
        self.normals = [tuple(normal) for normal in normals_array]
