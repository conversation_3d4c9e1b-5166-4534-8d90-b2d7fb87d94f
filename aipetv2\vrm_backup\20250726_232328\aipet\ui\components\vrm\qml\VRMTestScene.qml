// VRM测试场景
// 快速验证Qt Quick 3D和VRM控制器功能

import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQuick3D 6.0
import VRM 1.0
import "." as VRMComponents
import "../shaders" as MToonShaders

ApplicationWindow {
    id: testWindow
    
    title: "Qt Quick 3D VRM 快速测试"
    width: 1000
    height: 700
    visible: true
    
    // VRM控制器
    VRMController {
        id: vrmController
        
        Component.onCompleted: {
            console.log("VRM控制器已创建")
            debugLog("QML组件初始化完成")
        }
    }
    
    // 主布局
    ColumnLayout {
        anchors.fill: parent
        spacing: 0
        
        // 标题栏
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 50
            color: "#2a2a2a"
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: 10
                
                Text {
                    text: "🎭 Qt Quick 3D VRM 测试环境"
                    color: "white"
                    font.pixelSize: 18
                    font.bold: true
                }
                
                Item { Layout.fillWidth: true }
                
                Text {
                    text: vrmController.testMessage
                    color: vrmController.modelLoaded ? "lightgreen" : "orange"
                    font.pixelSize: 14
                }
            }
        }
        
        // 3D视图区域
        View3D {
            id: view3d
            Layout.fillWidth: true
            Layout.fillHeight: true
            
            environment: SceneEnvironment {
                backgroundMode: SceneEnvironment.Color
                clearColor: "#1a1a1a"
                antialiasingMode: SceneEnvironment.MSAA
                antialiasingQuality: SceneEnvironment.High
            }
            
            // 相机
            PerspectiveCamera {
                id: camera
                position: Qt.vector3d(0, 0, 5)
                eulerRotation.x: 0
                fieldOfView: 45
                
                // 相机动画
                SequentialAnimation on eulerRotation.y {
                    running: vrmController.modelLoaded
                    loops: Animation.Infinite
                    NumberAnimation {
                        from: 0
                        to: 360
                        duration: 10000
                    }
                }
            }
            
            // 主光源
            DirectionalLight {
                id: mainLight
                eulerRotation.x: -30
                eulerRotation.y: -30
                brightness: 1.0
                castsShadow: true
                color: "#ffffff"
            }
            
            // 补光
            DirectionalLight {
                id: fillLight
                eulerRotation.x: 30
                eulerRotation.y: 150
                brightness: 0.3
                castsShadow: false
                color: "#87ceeb"
            }
            
            // 环境光
            DirectionalLight {
                id: ambientLight
                eulerRotation.x: 90
                brightness: 0.1
                color: "#404040"
            }
            
            // VRM几何体显示节点
            Node {
                id: vrmModelNode
                visible: vrmController.modelLoaded
                
                // 旋转动画
                RotationAnimation on eulerRotation.y {
                    running: vrmController.modelLoaded
                    from: 0
                    to: 360
                    duration: 8000
                    loops: Animation.Infinite
                }

                // 浮动动画
                SequentialAnimation on position.y {
                    running: vrmController.modelLoaded
                    loops: Animation.Infinite
                    NumberAnimation {
                        from: 0
                        to: 0.3
                        duration: 3000
                        easing.type: Easing.InOutSine
                    }
                    NumberAnimation {
                        from: 0.3
                        to: 0
                        duration: 3000
                        easing.type: Easing.InOutSine
                    }
                }
                
                // VRM几何体显示 - 模拟人形结构
                Node {
                    id: vrmGeometryDisplay
                    visible: vrmController.modelLoaded

                    // 头部 - 使用MToon着色器
                    Model {
                        source: "#Sphere"
                        scale: Qt.vector3d(0.4, 0.4, 0.4)
                        position: Qt.vector3d(0, 1.5, 0)
                        materials: MToonShaders.MToonShader {
                            id: headMaterial
                            litFactor: "#ffdbac"  // 肤色
                            shadeColorFactor: "#d4a574"  // 阴影肤色
                            shadingShiftFactor: 0.0
                            shadingToonyFactor: 0.9
                            parametricRimColorFactor: "#ffffff"
                            parametricRimFresnelPowerFactor: 2.0
                            parametricRimLiftFactor: 0.1
                            debugMode: 0  // 正常渲染模式
                        }
                    }
                    
                    // 身体 - 使用MToon着色器
                    Model {
                        source: "#Cube"
                        scale: Qt.vector3d(0.6, 1.0, 0.3)
                        position: Qt.vector3d(0, 0.5, 0)
                        materials: MToonShaders.MToonShader {
                            id: bodyMaterial
                            litFactor: "#4a90e2"  // 蓝色衣服
                            shadeColorFactor: "#2c5aa0"  // 阴影蓝色
                            shadingShiftFactor: -0.1
                            shadingToonyFactor: 0.8
                            parametricRimColorFactor: "#87ceeb"
                            parametricRimFresnelPowerFactor: 1.5
                            parametricRimLiftFactor: 0.05
                            emissiveFactor: "#001122"  // 微弱自发光
                            emissiveIntensity: 0.1
                        }
                    }
                    
                    // 左臂 - 使用MToon着色器
                    Model {
                        source: "#Cube"
                        scale: Qt.vector3d(0.2, 0.8, 0.2)
                        position: Qt.vector3d(-0.5, 0.5, 0)
                        materials: MToonShaders.MToonShader {
                            id: leftArmMaterial
                            litFactor: "#ffdbac"  // 肤色
                            shadeColorFactor: "#d4a574"
                            shadingShiftFactor: 0.1
                            shadingToonyFactor: 0.9
                            parametricRimColorFactor: "#ffeecc"
                            parametricRimFresnelPowerFactor: 3.0
                            parametricRimLiftFactor: 0.2
                        }
                    }

                    // 右臂 - 使用MToon着色器
                    Model {
                        source: "#Cube"
                        scale: Qt.vector3d(0.2, 0.8, 0.2)
                        position: Qt.vector3d(0.5, 0.5, 0)
                        materials: MToonShaders.MToonShader {
                            id: rightArmMaterial
                            litFactor: "#ffdbac"  // 肤色
                            shadeColorFactor: "#d4a574"
                            shadingShiftFactor: 0.1
                            shadingToonyFactor: 0.9
                            parametricRimColorFactor: "#ffeecc"
                            parametricRimFresnelPowerFactor: 3.0
                            parametricRimLiftFactor: 0.2
                        }
                    }
                    
                    // 左腿 - 使用MToon着色器
                    Model {
                        source: "#Cube"
                        scale: Qt.vector3d(0.25, 1.0, 0.25)
                        position: Qt.vector3d(-0.2, -0.5, 0)
                        materials: MToonShaders.MToonShader {
                            id: leftLegMaterial
                            litFactor: "#2c3e50"  // 深色裤子
                            shadeColorFactor: "#1a252f"  // 更深的阴影
                            shadingShiftFactor: -0.2
                            shadingToonyFactor: 0.7
                            parametricRimColorFactor: "#34495e"
                            parametricRimFresnelPowerFactor: 1.0
                            parametricRimLiftFactor: 0.0
                        }
                    }

                    // 右腿 - 使用MToon着色器
                    Model {
                        source: "#Cube"
                        scale: Qt.vector3d(0.25, 1.0, 0.25)
                        position: Qt.vector3d(0.2, -0.5, 0)
                        materials: MToonShaders.MToonShader {
                            id: rightLegMaterial
                            litFactor: "#2c3e50"  // 深色裤子
                            shadeColorFactor: "#1a252f"  // 更深的阴影
                            shadingShiftFactor: -0.2
                            shadingToonyFactor: 0.7
                            parametricRimColorFactor: "#34495e"
                            parametricRimFresnelPowerFactor: 1.0
                            parametricRimLiftFactor: 0.0
                        }
                    }
                    
                    // 显示几何体数量的小立方体（代表VRM的复杂性）
                    Repeater {
                        model: Math.min(vrmController.geometryCount, 12)  // 最多显示12个
                        
                        delegate: Model {
                            source: "#Cube"
                            scale: Qt.vector3d(0.1, 0.1, 0.1)
                            position: Qt.vector3d(
                                (index % 4 - 1.5) * 0.3,
                                2.5 + Math.floor(index / 4) * 0.2,
                                0
                            )
                            
                            materials: PrincipledMaterial {
                                baseColor: {
                                    var colors = ["#ff6b6b", "#4ecdc4", "#45b7d1", "#96ceb4", "#feca57", "#ff9ff3", "#54a0ff", "#5f27cd", "#fd79a8", "#fdcb6e", "#6c5ce7", "#a29bfe"]
                                    return colors[index % colors.length]
                                }
                                metalness: 0.2
                                roughness: 0.3
                            }
                            
                            // 小立方体的旋转动画
                            RotationAnimation on eulerRotation.y {
                                running: vrmController.modelLoaded
                                from: 0
                                to: 360
                                duration: 2000 + (index * 100)
                                loops: Animation.Infinite
                            }
                        }
                    }
                }
                

            }

            // 回退立方体（当没有VRM数据时显示）
            Model {
                id: fallbackCube
                visible: !vrmController.modelLoaded

                source: "#Cube"
                scale: Qt.vector3d(1.0, 1.0, 1.0)

                materials: PrincipledMaterial {
                    baseColor: "#666666"
                    metalness: 0.1
                    roughness: 0.7
                }

                // 缓慢旋转
                RotationAnimation on eulerRotation.y {
                    running: !vrmController.modelLoaded
                    from: 0
                    to: 360
                    duration: 10000
                    loops: Animation.Infinite
                }
            }
            
            // 地面
            Model {
                source: "#Rectangle"
                scale: Qt.vector3d(10, 10, 1)
                eulerRotation.x: -90
                position.y: -2
                
                materials: PrincipledMaterial {
                    baseColor: "#2a2a2a"
                    metalness: 0.0
                    roughness: 0.8
                }
            }
            
            // 状态文本（3D空间中）
            Node {
                position: Qt.vector3d(0, 3, 0)
                visible: !vrmController.modelLoaded  // 只在未加载时显示
                
                Text {
                    anchors.centerIn: parent
                    text: "⏳ 等待加载VRM模型..."
                    color: "orange"
                    font.pixelSize: 14
                    horizontalAlignment: Text.AlignHCenter
                }
            }
        }
        
        // 控制面板
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 120
            color: "#1a1a1a"
            border.color: "#333333"
            border.width: 1
            
            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 15
                spacing: 10
                
                // 按钮行
                RowLayout {
                    Layout.fillWidth: true
                    spacing: 15
                    
                    Button {
                        text: "加载 Zome.vrm"
                        onClicked: {
                            console.log("点击加载Zome.vrm")
                            vrmController.loadModel("aipet/assets/vrm/models/Zome.vrm")
                        }
                        
                        flat: true
                        
                        background: Rectangle {
                            color: parent.pressed ? "#4a90e2" : "#3a7bd5"
                            radius: 5
                        }
                    }
                    
                    Button {
                        text: "加载 Alice.vrm"
                        onClicked: {
                            console.log("点击加载Alice.vrm")
                            vrmController.loadModel("aipet/assets/vrm/models/Alice.vrm")
                        }
                        
                        flat: true
                        
                        background: Rectangle {
                            color: parent.pressed ? "#e74c3c" : "#c0392b"
                            radius: 5
                        }
                    }
                    
                    Button {
                        text: "清除模型"
                        onClicked: {
                            console.log("点击清除模型")
                            vrmController.clearModel()
                        }
                        
                        flat: true
                        
                        background: Rectangle {
                            color: parent.pressed ? "#95a5a6" : "#7f8c8d"
                            radius: 5
                        }
                    }
                    
                    Button {
                        text: "测试连接"
                        onClicked: {
                            console.log("点击测试连接")
                            vrmController.testConnection()
                        }

                        flat: true

                        background: Rectangle {
                            color: parent.pressed ? "#f39c12" : "#e67e22"
                            radius: 5
                        }
                    }

                    // MToon调试模式切换按钮
                    Button {
                        text: (function() {
                            switch(headMaterial.debugMode) {
                                case 0: return "正常模式"
                                case 1: return "法线模式"
                                case 2: return "阴影模式"
                                case 3: return "UV模式"
                                default: return "未知模式"
                            }
                        })()
                        onClicked: {
                            var nextMode = (headMaterial.debugMode + 1) % 4
                            console.log("切换MToon调试模式:", nextMode)

                            // 同步所有材质的调试模式
                            headMaterial.debugMode = nextMode
                            bodyMaterial.debugMode = nextMode
                            leftArmMaterial.debugMode = nextMode
                            rightArmMaterial.debugMode = nextMode
                            leftLegMaterial.debugMode = nextMode
                            rightLegMaterial.debugMode = nextMode
                        }

                        flat: true

                        background: Rectangle {
                            color: parent.pressed ? "#9b59b6" : "#8e44ad"
                            radius: 5
                        }
                    }

                    Item { Layout.fillWidth: true }
                }
                
                // MToon参数控制行
                RowLayout {
                    Layout.fillWidth: true
                    spacing: 15

                    Text {
                        text: "边缘光强度:"
                        color: "white"
                        font.pixelSize: 12
                    }

                    Slider {
                        id: rimPowerSlider
                        from: 0.1
                        to: 5.0
                        value: 2.0
                        stepSize: 0.1

                        onValueChanged: {
                            // 同步所有材质的边缘光强度
                            headMaterial.parametricRimFresnelPowerFactor = value
                            bodyMaterial.parametricRimFresnelPowerFactor = value
                            leftArmMaterial.parametricRimFresnelPowerFactor = value
                            rightArmMaterial.parametricRimFresnelPowerFactor = value
                            leftLegMaterial.parametricRimFresnelPowerFactor = value
                            rightLegMaterial.parametricRimFresnelPowerFactor = value
                        }

                        background: Rectangle {
                            x: rimPowerSlider.leftPadding
                            y: rimPowerSlider.topPadding + rimPowerSlider.availableHeight / 2 - height / 2
                            implicitWidth: 200
                            implicitHeight: 4
                            width: rimPowerSlider.availableWidth
                            height: implicitHeight
                            radius: 2
                            color: "#333333"
                        }

                        handle: Rectangle {
                            x: rimPowerSlider.leftPadding + rimPowerSlider.visualPosition * (rimPowerSlider.availableWidth - width)
                            y: rimPowerSlider.topPadding + rimPowerSlider.availableHeight / 2 - height / 2
                            implicitWidth: 16
                            implicitHeight: 16
                            radius: 8
                            color: rimPowerSlider.pressed ? "#f0f0f0" : "#ffffff"
                            border.color: "#cccccc"
                        }
                    }

                    Text {
                        text: rimPowerSlider.value.toFixed(1)
                        color: "lightblue"
                        font.pixelSize: 12
                        Layout.preferredWidth: 30
                    }

                    Text {
                        text: "卡通化程度:"
                        color: "white"
                        font.pixelSize: 12
                    }

                    Slider {
                        id: toonySlider
                        from: 0.1
                        to: 1.0
                        value: 0.9
                        stepSize: 0.05

                        onValueChanged: {
                            // 同步所有材质的卡通化程度
                            headMaterial.shadingToonyFactor = value
                            bodyMaterial.shadingToonyFactor = value
                            leftArmMaterial.shadingToonyFactor = value
                            rightArmMaterial.shadingToonyFactor = value
                            leftLegMaterial.shadingToonyFactor = value
                            rightLegMaterial.shadingToonyFactor = value
                        }

                        background: Rectangle {
                            x: toonySlider.leftPadding
                            y: toonySlider.topPadding + toonySlider.availableHeight / 2 - height / 2
                            implicitWidth: 200
                            implicitHeight: 4
                            width: toonySlider.availableWidth
                            height: implicitHeight
                            radius: 2
                            color: "#333333"
                        }

                        handle: Rectangle {
                            x: toonySlider.leftPadding + toonySlider.visualPosition * (toonySlider.availableWidth - width)
                            y: toonySlider.topPadding + toonySlider.availableHeight / 2 - height / 2
                            implicitWidth: 16
                            implicitHeight: 16
                            radius: 8
                            color: toonySlider.pressed ? "#f0f0f0" : "#ffffff"
                            border.color: "#cccccc"
                        }
                    }

                    Text {
                        text: toonySlider.value.toFixed(2)
                        color: "lightblue"
                        font.pixelSize: 12
                        Layout.preferredWidth: 30
                    }

                    Item { Layout.fillWidth: true }
                }

                // 状态信息行
                RowLayout {
                    Layout.fillWidth: true
                    spacing: 20
                    
                    Text {
                        text: vrmController.modelLoaded ?
                              "状态: ✅ 已加载 " + vrmController.modelName + " (几何体:" + vrmController.geometryCount + " 材质:" + vrmController.materialCount + ")" :
                              "状态: ⏳ 未加载"
                        color: vrmController.modelLoaded ? "lightgreen" : "orange"
                        font.pixelSize: 14
                    }

                    Text {
                        text: "MToon着色器: ✅ 已启用"
                        color: "lightgreen"
                        font.pixelSize: 14
                    }

                    Text {
                        text: "调试模式: " + (function() {
                            switch(headMaterial.debugMode) {
                                case 0: return "正常"
                                case 1: return "法线"
                                case 2: return "阴影"
                                case 3: return "UV"
                                default: return "未知"
                            }
                        })()
                        color: "lightblue"
                        font.pixelSize: 14
                    }
                    
                    Item { Layout.fillWidth: true }
                    
                    Text {
                        text: "FPS: 60"
                        color: "lightblue"
                        font.pixelSize: 14
                    }
                }
            }
        }
    }
    
    // 键盘快捷键
    Item {
        focus: true
        Keys.onPressed: {
            switch(event.key) {
                case Qt.Key_1:
                    vrmController.loadModel("aipet/assets/vrm/models/Zome.vrm")
                    break
                case Qt.Key_2:
                    vrmController.loadModel("aipet/assets/vrm/models/Alice.vrm")
                    break
                case Qt.Key_C:
                    vrmController.clearModel()
                    break
                case Qt.Key_T:
                    vrmController.testConnection()
                    break
                case Qt.Key_D:
                    // 切换MToon调试模式
                    var nextMode = (headMaterial.debugMode + 1) % 4
                    console.log("键盘切换MToon调试模式:", nextMode)
                    headMaterial.debugMode = nextMode
                    bodyMaterial.debugMode = nextMode
                    leftArmMaterial.debugMode = nextMode
                    rightArmMaterial.debugMode = nextMode
                    leftLegMaterial.debugMode = nextMode
                    rightLegMaterial.debugMode = nextMode
                    break
                case Qt.Key_R:
                    // 重置MToon参数
                    console.log("重置MToon参数")
                    rimPowerSlider.value = 2.0
                    toonySlider.value = 0.9
                    break
            }
        }
    }
    
    // 监听模型变化
    Connections {
        target: vrmController
        function onModelChanged() {
            console.log("🔄 模型数据已更新:")
            console.log("  - 模型已加载:", vrmController.modelLoaded)
            console.log("  - 模型名称:", vrmController.modelName)
            console.log("  - 几何体数量:", vrmController.geometryCount)
            console.log("  - 材质数量:", vrmController.materialCount)
        }
    }
    
    // 组件完成时的初始化
    Component.onCompleted: {
        console.log("VRM测试场景初始化完成")
        vrmController.debugLog("测试场景已就绪")
        
        // 输出初始状态
        console.log("初始状态:")
        console.log("  - 模型已加载:", vrmController.modelLoaded)
        console.log("  - 几何体数量:", vrmController.geometryCount)
    }
}
