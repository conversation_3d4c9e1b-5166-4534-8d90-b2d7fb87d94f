// MToon着色器组件
// 基于three-vrm的MToon实现，移植到Qt Quick 3D
// 支持卡通渲染、边缘光、MatCap等效果

import QtQuick 2.15
import QtQuick3D 6.0

CustomMaterial {
    id: mtoonMaterial
    
    // ========== 材质属性 ==========
    
    // 基础颜色
    property color litFactor: "#ffffff"
    property real opacity: 1.0
    
    // 阴影颜色
    property color shadeColorFactor: "#7f7f7f"
    property var shadeMultiplyTexture: null
    
    // 阴影控制
    property real shadingShiftFactor: 0.0
    property real shadingToonyFactor: 0.9
    
    // GI均衡
    property real giEqualizationFactor: 0.9
    
    // 边缘光
    property color parametricRimColorFactor: "#000000"
    property var rimMultiplyTexture: null
    property real rimLightingMixFactor: 0.0
    property real parametricRimFresnelPowerFactor: 1.0
    property real parametricRimLiftFactor: 0.0
    
    // MatCap
    property color matcapFactor: "#000000"
    property var matcapTexture: null
    
    // 自发光
    property color emissiveFactor: "#000000"
    property real emissiveIntensity: 1.0
    property var emissiveTexture: null
    
    // 法线
    property var normalTexture: null
    property real normalScale: 1.0
    
    // 描边
    property color outlineColorFactor: "#000000"
    property real outlineLightingMixFactor: 1.0
    property real outlineWidthFactor: 0.0
    
    // UV动画
    property real uvAnimationScrollXOffset: 0.0
    property real uvAnimationScrollYOffset: 0.0
    property real uvAnimationRotationPhase: 0.0
    property var uvAnimationMaskTexture: null
    
    // 调试模式
    property int debugMode: 0 // 0=None, 1=Normal, 2=LitShadeRate, 3=UV
    
    // 渲染状态
    property bool isOutline: false
    property bool transparentWithZWrite: false
    
    // ========== 着色器源码 ==========
    
    vertexShader: "mtoon.vert"
    fragmentShader: "mtoon.frag"
    
    // ========== Uniform传递 ==========
    
    property var uniforms: ({
        // 基础属性
        "litFactor": Qt.vector3d(litFactor.r, litFactor.g, litFactor.b),
        "opacity": opacity,
        
        // 阴影属性
        "shadeColorFactor": Qt.vector3d(shadeColorFactor.r, shadeColorFactor.g, shadeColorFactor.b),
        "shadingShiftFactor": shadingShiftFactor,
        "shadingToonyFactor": shadingToonyFactor,
        "giEqualizationFactor": giEqualizationFactor,
        
        // 边缘光属性
        "parametricRimColorFactor": Qt.vector3d(parametricRimColorFactor.r, parametricRimColorFactor.g, parametricRimColorFactor.b),
        "rimLightingMixFactor": rimLightingMixFactor,
        "parametricRimFresnelPowerFactor": parametricRimFresnelPowerFactor,
        "parametricRimLiftFactor": parametricRimLiftFactor,
        
        // MatCap属性
        "matcapFactor": Qt.vector3d(matcapFactor.r, matcapFactor.g, matcapFactor.b),
        
        // 自发光属性
        "emissiveFactor": Qt.vector3d(emissiveFactor.r, emissiveFactor.g, emissiveFactor.b),
        "emissiveIntensity": emissiveIntensity,
        
        // 法线属性
        "normalScale": normalScale,
        
        // 描边属性
        "outlineColorFactor": Qt.vector3d(outlineColorFactor.r, outlineColorFactor.g, outlineColorFactor.b),
        "outlineLightingMixFactor": outlineLightingMixFactor,
        "outlineWidthFactor": outlineWidthFactor,
        
        // UV动画属性
        "uvAnimationScrollXOffset": uvAnimationScrollXOffset,
        "uvAnimationScrollYOffset": uvAnimationScrollYOffset,
        "uvAnimationRotationPhase": uvAnimationRotationPhase,
        
        // 调试和状态
        "debugMode": debugMode,
        "isOutline": isOutline ? 1.0 : 0.0
    })
    
    // ========== 纹理绑定 ==========
    // 注意：纹理输入将在需要时动态添加
    
    // ========== 渲染状态设置 ==========
    
    cullMode: Material.BackFaceCulling
    depthDrawMode: transparentWithZWrite ? Material.AlwaysDepthDraw : Material.OpaqueOnlyDepthDraw
    
    // ========== 辅助函数 ==========
    
    // 设置基础颜色
    function setBaseColor(color) {
        litFactor = color
    }
    
    // 设置阴影颜色
    function setShadeColor(color) {
        shadeColorFactor = color
    }
    
    // 设置边缘光颜色
    function setRimColor(color) {
        parametricRimColorFactor = color
    }
    
    // 设置自发光颜色
    function setEmissiveColor(color) {
        emissiveFactor = color
    }
    
    // 设置描边颜色
    function setOutlineColor(color) {
        outlineColorFactor = color
    }
    
    // 启用调试模式
    function setDebugMode(mode) {
        debugMode = mode
    }
    
    // 更新UV动画
    function updateUVAnimation(deltaTime) {
        // 这里可以添加基于时间的UV动画更新逻辑
        // 目前由外部控制offset和phase值
    }
    
    // ========== 组件初始化 ==========
    
    Component.onCompleted: {
        console.log("MToon着色器组件初始化完成")
        console.log("  - 顶点着色器:", vertexShader)
        console.log("  - 片段着色器:", fragmentShader)
        console.log("  - 基础颜色:", litFactor)
        console.log("  - 阴影颜色:", shadeColorFactor)
    }
}
