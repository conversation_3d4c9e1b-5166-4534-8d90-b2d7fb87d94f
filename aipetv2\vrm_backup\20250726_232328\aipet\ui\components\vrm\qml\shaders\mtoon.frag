#version 440 core

// MToon片段着色器
// 基于three-vrm的MToon片段着色器实现
// 支持卡通渲染、边缘光、MatCap等效果

// ========== 输入变量 ==========
in vec3 vViewPosition;
in vec3 vNormal;
in vec2 vUv;
in vec3 vWorldPosition;
in vec3 vTangent;
in vec3 vBitangent;

// ========== 输出变量 ==========
out vec4 fragColor;

// ========== Uniform变量 ==========
layout(std140, binding = 0) uniform qt_Uniforms {
    mat4 qt_Matrix;
    mat4 qt_ModelMatrix;
    mat4 qt_ViewMatrix;
    mat4 qt_ProjectionMatrix;
    mat4 qt_ModelViewMatrix;
    mat3 qt_NormalMatrix;
    vec3 qt_CameraPosition;
    float qt_Time;
};

// MToon材质属性
uniform vec3 litFactor;
uniform float opacity;
uniform vec3 shadeColorFactor;
uniform float shadingShiftFactor;
uniform float shadingToonyFactor;
uniform float giEqualizationFactor;

// 边缘光属性
uniform vec3 parametricRimColorFactor;
uniform float rimLightingMixFactor;
uniform float parametricRimFresnelPowerFactor;
uniform float parametricRimLiftFactor;

// MatCap属性
uniform vec3 matcapFactor;

// 自发光属性
uniform vec3 emissiveFactor;
uniform float emissiveIntensity;

// 法线属性
uniform float normalScale;

// 描边属性
uniform vec3 outlineColorFactor;
uniform float outlineLightingMixFactor;
uniform float isOutline;

// 调试模式
uniform int debugMode;

// 纹理（暂时注释，后续添加）
// uniform sampler2D shadeMultiplyTexture;
// uniform sampler2D rimMultiplyTexture;
// uniform sampler2D matcapTexture;
// uniform sampler2D emissiveTexture;
// uniform sampler2D normalTexture;

// ========== 辅助函数 ==========

// 线性步进函数（卡通化关键）
float linearstep(float a, float b, float t) {
    return clamp((t - a) / (b - a), 0.0, 1.0);
}

// 获取卡通阴影因子
float getShading(float dotNL, float shadow, float shadingShift) {
    float shading = dotNL;
    shading = shading + shadingShift;
    shading = linearstep(-1.0 + shadingToonyFactor, 1.0 - shadingToonyFactor, shading);
    shading *= shadow;
    return shading;
}

// 混合漫反射和阴影颜色
vec3 getDiffuse(vec3 diffuseColor, vec3 shadeColor, float shading, vec3 lightColor) {
    vec3 col = lightColor * mix(shadeColor, diffuseColor, shading) / 3.14159265359; // BRDF_Lambert
    return col;
}

// 计算边缘光
vec3 calculateRim(vec3 viewDir, vec3 normal, vec3 rimColor, float rimPower, float rimLift) {
    float rimFactor = 1.0 - dot(viewDir, normal) + rimLift;
    rimFactor = clamp(rimFactor, 0.0, 1.0);
    return rimColor * pow(rimFactor, rimPower);
}

// 计算MatCap
vec3 calculateMatcap(vec3 viewDir, vec3 normal, vec3 matcapColor) {
    // 简化的MatCap计算（球形环境映射）
    vec3 x = normalize(vec3(viewDir.z, 0.0, -viewDir.x));
    vec3 y = cross(viewDir, x);
    vec2 sphereUv = 0.5 + 0.5 * vec2(dot(x, normal), -dot(y, normal));
    
    // 这里应该从MatCap纹理采样，暂时使用颜色
    return matcapColor;
}

// ========== 主函数 ==========
void main() {
    // 基础设置
    vec2 uv = vUv;
    vec3 normal = normalize(vNormal);
    vec3 viewDir = normalize(vViewPosition);
    
    // 调试模式
    if (debugMode == 1) { // Normal
        fragColor = vec4(0.5 + 0.5 * normal, 1.0);
        return;
    } else if (debugMode == 3) { // UV
        fragColor = vec4(uv, 0.0, 1.0);
        return;
    }
    
    // 基础颜色
    vec4 diffuseColor = vec4(litFactor, opacity);
    vec3 shadeColor = shadeColorFactor;
    
    // 简化的光照计算（单个方向光）
    vec3 lightDir = normalize(vec3(0.5, 1.0, 0.5)); // 固定光源方向
    vec3 lightColor = vec3(1.0);
    float dotNL = clamp(dot(normal, lightDir), -1.0, 1.0);
    
    // 阴影因子（简化，实际应该从阴影贴图计算）
    float shadow = 1.0;
    
    // 卡通阴影
    float shading = getShading(dotNL, shadow, shadingShiftFactor);
    
    // 漫反射颜色
    vec3 col = getDiffuse(diffuseColor.rgb, shadeColor, shading, lightColor);
    
    // 调试模式：显示光照/阴影比率
    if (debugMode == 2) { // LitShadeRate
        fragColor = vec4(vec3(shading), diffuseColor.a);
        return;
    }
    
    // 边缘光
    vec3 rim = calculateRim(viewDir, normal, parametricRimColorFactor, 
                           parametricRimFresnelPowerFactor, parametricRimLiftFactor);
    
    // MatCap
    vec3 matcap = calculateMatcap(viewDir, normal, matcapFactor);
    
    // 组合边缘光和MatCap
    vec3 rimMix = mix(vec3(1.0), lightColor, 1.0); // 简化的光照混合
    col += rimMix * (rim + matcap);
    
    // 自发光
    vec3 totalEmissive = emissiveFactor * emissiveIntensity;
    col += totalEmissive;
    
    // 描边处理
    if (isOutline > 0.5) {
        col = outlineColorFactor * mix(vec3(1.0), col, outlineLightingMixFactor);
    }
    
    // 最终颜色输出
    fragColor = vec4(col, diffuseColor.a);
}
