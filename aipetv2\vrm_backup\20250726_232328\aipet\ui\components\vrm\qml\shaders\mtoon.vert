#version 440 core

// MToon顶点着色器
// 基于three-vrm的MToon顶点着色器实现
// 支持描边、UV动画等功能

// ========== 输入属性 ==========
layout(location = 0) in vec3 qt_Vertex;
layout(location = 1) in vec3 qt_Normal;
layout(location = 2) in vec2 qt_MultiTexCoord0;
layout(location = 3) in vec4 qt_Tangent;

// ========== Uniform变量 ==========
layout(std140, binding = 0) uniform qt_Uniforms {
    mat4 qt_Matrix;
    mat4 qt_ModelMatrix;
    mat4 qt_ViewMatrix;
    mat4 qt_ProjectionMatrix;
    mat4 qt_ModelViewMatrix;
    mat3 qt_NormalMatrix;
    vec3 qt_CameraPosition;
    float qt_Time;
};

// MToon特定uniform
uniform float outlineWidthFactor;
uniform float isOutline;
uniform float uvAnimationScrollXOffset;
uniform float uvAnimationScrollYOffset;
uniform float uvAnimationRotationPhase;

// ========== 输出变量 ==========
out vec3 vViewPosition;
out vec3 vNormal;
out vec2 vUv;
out vec3 vWorldPosition;
out vec3 vTangent;
out vec3 vBitangent;

// ========== 辅助函数 ==========

// UV变换函数
vec2 transformUV(vec2 uv, vec2 scrollOffset, float rotationPhase) {
    // UV动画遮罩（简化版，实际应该从纹理采样）
    float uvAnimMask = 1.0;
    
    // 旋转变换
    float uvRotCos = cos(rotationPhase * uvAnimMask);
    float uvRotSin = sin(rotationPhase * uvAnimMask);
    mat2 rotMatrix = mat2(uvRotCos, -uvRotSin, uvRotSin, uvRotCos);
    
    // 应用旋转（围绕中心点）
    uv = rotMatrix * (uv - 0.5) + 0.5;
    
    // 应用滚动偏移
    uv = uv + scrollOffset * uvAnimMask;
    
    return uv;
}

// 描边顶点偏移计算
vec3 calculateOutlineOffset(vec3 position, vec3 normal) {
    if (isOutline < 0.5) {
        return vec3(0.0);
    }
    
    // 计算世界空间法线长度
    vec3 worldNormal = normalize(qt_NormalMatrix * normal);
    float worldNormalLength = length(worldNormal);
    
    // 描边偏移
    vec3 outlineOffset = outlineWidthFactor * worldNormalLength * normal;
    
    return outlineOffset;
}

// ========== 主函数 ==========
void main() {
    // 基础顶点变换
    vec3 position = qt_Vertex;
    vec3 normal = normalize(qt_Normal);
    vec2 uv = qt_MultiTexCoord0;
    
    // 计算切线和副切线
    vec3 tangent = normalize(qt_Tangent.xyz);
    vec3 bitangent = cross(normal, tangent) * qt_Tangent.w;
    
    // 描边处理
    vec3 outlineOffset = calculateOutlineOffset(position, normal);
    position += outlineOffset;
    
    // 如果是描边，翻转法线
    if (isOutline > 0.5) {
        normal = -normal;
    }
    
    // UV动画变换
    vec2 scrollOffset = vec2(uvAnimationScrollXOffset, uvAnimationScrollYOffset);
    vUv = transformUV(uv, scrollOffset, uvAnimationRotationPhase);
    
    // 计算各种空间的位置和法线
    vec4 worldPosition = qt_ModelMatrix * vec4(position, 1.0);
    vec4 viewPosition = qt_ModelViewMatrix * vec4(position, 1.0);
    
    vWorldPosition = worldPosition.xyz;
    vViewPosition = -viewPosition.xyz; // three.js风格的视图位置
    
    // 变换法线、切线、副切线到世界空间
    vNormal = normalize(qt_NormalMatrix * normal);
    vTangent = normalize(qt_NormalMatrix * tangent);
    vBitangent = normalize(qt_NormalMatrix * bitangent);
    
    // 最终顶点位置
    gl_Position = qt_ProjectionMatrix * viewPosition;
    
    // 描边的深度偏移（防止z-fighting）
    if (isOutline > 0.5) {
        gl_Position.z += 1e-6 * gl_Position.w;
    }
}
