// MToon通用着色器函数库
// 包含MToon着色器的通用函数和常量定义
// 可以被顶点着色器和片段着色器共同使用

#ifndef MTOON_COMMON_GLSL
#define MTOON_COMMON_GLSL

// ========== 常量定义 ==========

// 数学常量
#define PI 3.14159265359
#define RECIPROCAL_PI 0.31830988618
#define EPSILON 1e-6

// 调试模式常量
#define DEBUG_NONE 0
#define DEBUG_NORMAL 1
#define DEBUG_LITSHADERATE 2
#define DEBUG_UV 3

// 描边宽度模式
#define OUTLINE_WIDTH_NONE 0
#define OUTLINE_WIDTH_WORLD 1
#define OUTLINE_WIDTH_SCREEN 2

// ========== 结构体定义 ==========

// MToon材质结构体
struct MToonMaterial {
    vec3 diffuseColor;
    vec3 shadeColor;
    float shadingShift;
    float shadingToony;
    vec3 rimColor;
    float rimPower;
    float rimLift;
    vec3 matcapColor;
    vec3 emissiveColor;
    float emissiveIntensity;
};

// 光照信息结构体
struct LightInfo {
    vec3 direction;
    vec3 color;
    float intensity;
    float shadow;
};

// ========== 数学函数 ==========

// 安全的normalize函数
vec3 safeNormalize(vec3 v) {
    float len = length(v);
    return len > EPSILON ? v / len : vec3(0.0, 0.0, 1.0);
}

// 线性步进函数（卡通化的核心）
float linearstep(float a, float b, float t) {
    return clamp((t - a) / (b - a), 0.0, 1.0);
}

// 平滑步进函数
float smootherstep(float edge0, float edge1, float x) {
    float t = clamp((x - edge0) / (edge1 - edge0), 0.0, 1.0);
    return t * t * t * (t * (t * 6.0 - 15.0) + 10.0);
}

// Fresnel计算
float fresnel(vec3 viewDir, vec3 normal, float power) {
    float cosTheta = clamp(dot(viewDir, normal), 0.0, 1.0);
    return pow(1.0 - cosTheta, power);
}

// ========== 颜色函数 ==========

// sRGB到线性空间转换
vec3 sRGBToLinear(vec3 srgb) {
    return pow(srgb, vec3(2.2));
}

// 线性空间到sRGB转换
vec3 linearToSRGB(vec3 linear) {
    return pow(linear, vec3(1.0 / 2.2));
}

// 颜色混合函数
vec3 blendColors(vec3 base, vec3 overlay, float factor) {
    return mix(base, overlay, factor);
}

// ========== UV变换函数 ==========

// UV旋转变换
vec2 rotateUV(vec2 uv, float angle) {
    float cosAngle = cos(angle);
    float sinAngle = sin(angle);
    mat2 rotMatrix = mat2(cosAngle, -sinAngle, sinAngle, cosAngle);
    return rotMatrix * (uv - 0.5) + 0.5;
}

// UV缩放变换
vec2 scaleUV(vec2 uv, vec2 scale) {
    return (uv - 0.5) * scale + 0.5;
}

// UV平移变换
vec2 translateUV(vec2 uv, vec2 offset) {
    return uv + offset;
}

// 组合UV变换
vec2 transformUV(vec2 uv, vec2 offset, float rotation, vec2 scale) {
    uv = scaleUV(uv, scale);
    uv = rotateUV(uv, rotation);
    uv = translateUV(uv, offset);
    return uv;
}

// ========== 光照函数 ==========

// Lambert漫反射BRDF
vec3 BRDF_Lambert(vec3 diffuseColor) {
    return RECIPROCAL_PI * diffuseColor;
}

// 卡通阴影计算
float getToonShading(float dotNL, float shadow, float shadingShift, float shadingToony) {
    float shading = dotNL + shadingShift;
    shading = linearstep(-1.0 + shadingToony, 1.0 - shadingToony, shading);
    return shading * shadow;
}

// 漫反射颜色混合
vec3 getMixedDiffuse(vec3 diffuseColor, vec3 shadeColor, float shading, vec3 lightColor) {
    vec3 mixedColor = mix(shadeColor, diffuseColor, shading);
    return lightColor * BRDF_Lambert(mixedColor);
}

// ========== 边缘光函数 ==========

// 参数化边缘光计算
vec3 getParametricRim(vec3 viewDir, vec3 normal, vec3 rimColor, float rimPower, float rimLift) {
    float rimFactor = 1.0 - dot(viewDir, normal) + rimLift;
    rimFactor = clamp(rimFactor, 0.0, 1.0);
    return rimColor * pow(rimFactor, rimPower);
}

// 基于Fresnel的边缘光
vec3 getFresnelRim(vec3 viewDir, vec3 normal, vec3 rimColor, float rimPower) {
    float fresnelFactor = fresnel(viewDir, normal, rimPower);
    return rimColor * fresnelFactor;
}

// ========== MatCap函数 ==========

// 计算MatCap UV坐标
vec2 getMatcapUV(vec3 viewDir, vec3 normal) {
    vec3 x = safeNormalize(vec3(viewDir.z, 0.0, -viewDir.x));
    vec3 y = cross(viewDir, x);
    return 0.5 + 0.5 * vec2(dot(x, normal), -dot(y, normal));
}

// MatCap颜色计算（无纹理版本）
vec3 getMatcapColor(vec3 viewDir, vec3 normal, vec3 matcapFactor) {
    vec2 matcapUV = getMatcapUV(viewDir, normal);
    // 简单的程序化MatCap效果
    float intensity = length(matcapUV - 0.5) * 2.0;
    intensity = 1.0 - clamp(intensity, 0.0, 1.0);
    return matcapFactor * intensity;
}

// ========== 描边函数 ==========

// 计算描边偏移
vec3 getOutlineOffset(vec3 position, vec3 normal, float outlineWidth, int widthMode) {
    if (widthMode == OUTLINE_WIDTH_NONE) {
        return vec3(0.0);
    }
    
    vec3 offset = normal * outlineWidth;
    
    // 世界坐标模式：直接使用法线偏移
    if (widthMode == OUTLINE_WIDTH_WORLD) {
        return offset;
    }
    
    // 屏幕坐标模式：需要考虑透视投影
    // 这里简化处理，实际需要更复杂的计算
    return offset;
}

// ========== 调试函数 ==========

// 获取调试颜色
vec4 getDebugColor(int debugMode, vec3 normal, vec2 uv, float shadingRate) {
    switch (debugMode) {
        case DEBUG_NORMAL:
            return vec4(0.5 + 0.5 * normal, 1.0);
        case DEBUG_LITSHADERATE:
            return vec4(vec3(shadingRate), 1.0);
        case DEBUG_UV:
            return vec4(uv, 0.0, 1.0);
        default:
            return vec4(0.0);
    }
}

// ========== 工具函数 ==========

// 检查是否为有效颜色
bool isValidColor(vec3 color) {
    return all(greaterThanEqual(color, vec3(0.0))) && all(lessThanEqual(color, vec3(1.0)));
}

// 颜色饱和度调整
vec3 adjustSaturation(vec3 color, float saturation) {
    float gray = dot(color, vec3(0.299, 0.587, 0.114));
    return mix(vec3(gray), color, saturation);
}

// 颜色对比度调整
vec3 adjustContrast(vec3 color, float contrast) {
    return (color - 0.5) * contrast + 0.5;
}

#endif // MTOON_COMMON_GLSL
